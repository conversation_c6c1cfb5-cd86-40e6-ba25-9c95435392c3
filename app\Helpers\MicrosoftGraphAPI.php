<?php

namespace App\Helpers;

use GuzzleHttp\Client;

class MicrosoftGraphAPI
{
    /**
     * Generate a token to use the Microsoft Graph APIs
     *
     * @return mixed
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private static function getToken()
    {
        $guzzle = new Client();
        $url = 'https://login.microsoftonline.com/'.config('azure.tenantId').'/oauth2/token?api-version=1.0';

        $token = json_decode($guzzle->post($url, [
            'form_params' => [
                'client_id' => config('azure.clientId'),
                'client_secret' => config('azure.appSecret'),
                'resource' => config('azure.resource'),
                'grant_type' => 'client_credentials',
                'scopes' => config('azure.scopes'),
            ],
        ])->getBody()->getContents(), false, 512, JSON_THROW_ON_ERROR);

        return $token->access_token;
    }
}
