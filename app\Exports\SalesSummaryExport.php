<?php

namespace App\Exports;

use App\Http\Controllers\Admin\SalesSummaryController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SalesSummaryExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison, WithStyles, WithEvents
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(SalesSummaryController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet
                    ->getDelegate()
                    ->getStyle('A:P')
                    ->getAlignment()
                    ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER)
                    ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            },
        ];
    }

    public function headings(): array
    {
        $headings = [
            'Sales Name',
            'Direct Manager',
            'Trading Accounts',
            'Total Withdrawals',
            'Avg Withdrawals',
            'Total Deposits',
            'Avg Deposits',
            'Total FTDs',
            'Sum FTDs',
            'Total Assigned Apps',
            'Total Assigned Individual Apps',
            'Total Assigned Agent Apps',
            'Used Payments',
        ];

        return $headings;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12
                ]
            ],

        ];
    }

    public function map($row): array
    {
        return  [
            $row->mainUser->getUserHolderName(),
            $row->appRelation?->closerApplication?->mainUser?->getUserHolderName() ?? '-',
            $row->trading_accounts,
            $row->total_withdrawals_amount,
            $row->total_withdrawals_amount && $row->total_withdrawals_transactions ? number_format(($row->total_withdrawals_amount / $row->total_withdrawals_transactions), 2, '.', '') : 0.0  ,
            number_format($row->total_deposits_amount, 2, '.', '') ,
            $row->total_deposits_amount && $row->total_deposits_transactions ? number_format(($row->total_deposits_amount / $row->total_deposits_transactions), 2, '.', '') : 0.0  ,
            $row->ftd_count ?? '0',
            $row->ftd_sum ?? '0',
            $row->assigned_applications,
            $row->assigned_applications_individuals,
            $row->assigned_applications_agents,
            $row->used_payments
        ];
    }
}
