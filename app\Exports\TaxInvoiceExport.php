<?php

namespace App\Exports;

use App\Http\Controllers\Admin\TaxInvoiceController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class TaxInvoiceExport implements FromQuery, ShouldAutoSize, WithHeadings, WithMapping, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(TaxInvoiceController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Title',
            'Reference Number',
            'Total Amount',
            'Quantity',
            'Issue Date',
            'Status',
            'Description',
            'Return Reason',
        ];
    }

    public function map($row): array
    {
        return [
            $row->title ?? '-',
            $row->reference_number ?? '-',
            $row->currency.' '.$row->amount ?? '-',
            $row->quantity ?? '-',
            $row->issue_date ?? '-',
            $row->status?->name ?? '-',
            $row->description ?? '-',
            $row->return_reason ?? '-',
        ];
    }
}
