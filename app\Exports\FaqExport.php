<?php

namespace App\Exports;

use App\Models\AffiliateWebsite;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\Http\Controllers\Admin\FaqController;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class FaqExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison, WithTitle
{
    use Exportable;

    private $affiliate_websites;

    public function __construct(protected string $userId, protected array $requestData, protected $language = null)
    {
        $this->affiliate_websites = AffiliateWebsite::query()->pluck('name', 'id')->toArray();
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(FaqController::class)->index(
            request: request()->merge($this->requestData),
            export: true,
            language: $this->language
        );
    }

    public function headings(): array
    {
        return [
            'ID',
            'Question',
            'Answer',
            'Category',
            'Entity',
            'Created Date',
            'Updated On',
        ];
    }

    public function map($row): array
    {
        $answer = substr(rip_tags($row->answer), 0, 32500);
        $answer = mb_convert_encoding($answer, 'UTF-8');

        $aff_websites = [];
        foreach ($this->affiliate_websites as $site_id => $name) {
            if (in_array($site_id, $row->websites)) {
                $aff_websites[] = $name;
            }
        }

        return [
            $row->id,
            $row->question ?? '-',
            $answer ?? '-',
            $row->faqCategory?->name ?? '-',
            implode(', ', $aff_websites) ?? '-',
            $row->created_at,
            $row->updated_at ?? '-',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return strtoupper($this->language);
    }
}
