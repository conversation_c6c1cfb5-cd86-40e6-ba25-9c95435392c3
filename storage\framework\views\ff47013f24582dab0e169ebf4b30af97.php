
<?php $__env->startSection('content'); ?>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title"><?php if(isset($readonly)): ?> <?php echo e(__('content.show_role')); ?> <?php else: ?> <?php echo e(__('content.edit_role')); ?>  <?php endif; ?></h4>
            <div class="heading-elements">
                <?php if (isset($component)) { $__componentOriginal4a5ea6e2b79cb83dd7cdae38a5a11514 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4a5ea6e2b79cb83dd7cdae38a5a11514 = $attributes; } ?>
<?php $component = App\View\Components\LanguageTranslatableSwitcher::resolve(['model' => $role] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('language-translatable-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\LanguageTranslatableSwitcher::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4a5ea6e2b79cb83dd7cdae38a5a11514)): ?>
<?php $attributes = $__attributesOriginal4a5ea6e2b79cb83dd7cdae38a5a11514; ?>
<?php unset($__attributesOriginal4a5ea6e2b79cb83dd7cdae38a5a11514); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4a5ea6e2b79cb83dd7cdae38a5a11514)): ?>
<?php $component = $__componentOriginal4a5ea6e2b79cb83dd7cdae38a5a11514; ?>
<?php unset($__componentOriginal4a5ea6e2b79cb83dd7cdae38a5a11514); ?>
<?php endif; ?>
            </div>
        </div>
        <div class="card-content">
            <div class="card-body">
                <form id="roles-form"  class="form-horizontal" action="<?php echo e(route('roles.update', $role->id)); ?>"method="POST">
                    <div class="row">
                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <?php echo e(csrf_field()); ?>

                            <?php echo e(method_field('PUT')); ?>

                            <input name="locale" type="hidden" value="<?php echo e($role->locale); ?>">

                            <?php if(in_array('name', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('name') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control" for="name"><?php echo e(__('content.name')); ?></label>
                                    <div class="col-md-9">
                                        <input <?php echo e($readonly ?? ''); ?> type="text" id="name" name="name" value="<?php echo e((old('name')) ?: $role->name); ?>"
                                            class="form-control">
                                        <?php if($errors->has('name')): ?>
                                        <span class="help-block">
                                            <strong><?php echo e($errors->first('name')); ?></strong>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('display_name', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('display_name') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control" for="name"><?php echo e(__('content.display_name')); ?></label>
                                            <div class="col-md-9">
                                        <input <?php echo e($readonly ?? ''); ?> type="text" id="display_name" name="display_name" value="<?php echo e((old('display_name')) ?: $role->display_name); ?>"
                                            class="form-control">
                                        <?php if($errors->has('display_name')): ?>
                                        <span class="help-block">
                                            <strong><?php echo e($errors->first('display_name')); ?></strong>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('department_email', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('department_email') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control" for="name"><?php echo e(__('content.notification_email')); ?></label>
                                        <div class="col-md-9">
                                        <input <?php echo e($readonly ?? ''); ?> type="text" id="department_email" name="department_email" value="<?php echo e((old('department_email')) ?: $role->department_email); ?>" class="form-control">
                                        <?php if($errors->has('department_email')): ?>
                                            <span class="help-block">
                                                <strong><?php echo e($errors->first('department_email')); ?></strong>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('ip_addresses', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('ip_addresses') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control" for="name"><?php echo e(__('content.restricted_ip_addresses')); ?></label>
                                    <div class="col-md-9">
                                        <input <?php echo e($readonly ?? ''); ?> type="text" id="ip_addresses" name="ip_addresses" value="<?php echo e((old('ip_addresses')) ?? implode(', ', (array) $role->ip_addresses)); ?>" class="form-control" data-role="tagsinput">
                                        <?php if($errors->has('ip_addresses')): ?>
                                            <span class="help-block">
                                                <strong><?php echo e($errors->first('ip_addresses')); ?></strong>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('is_department', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('is_department') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.department_role')); ?></label>
                                    <div class="col-md-9">
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input <?php echo e($readonly ?? ''); ?> value="1" type="radio" class="custom-control-input" id="is_department_yes" name="is_department" <?php if(old('is_department') && old('is_department') =='1'): ?> checked <?php elseif( $role->is_department == '1'): ?> checked <?php endif; ?>>
                                            <label class="custom-control-label" for="is_department_yes"><?php echo e(__('content.yes')); ?></label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input  value="0" type="radio" class="custom-control-input" id="is_department_no" name="is_department" <?php if(old('is_department') && old('is_department') =='0'): ?> checked <?php elseif( $role->is_department == '0'): ?> checked <?php endif; ?>>
                                            <label class="custom-control-label" for="is_department_no"><?php echo e(__('content.no')); ?></label>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('is_manager', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('is_manager') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.department_manager')); ?></label>
                                    <div class="col-md-9">
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input <?php echo e($readonly ?? ''); ?> value="1" type="radio" class="custom-control-input" id="is_manager_yes" name="is_manager" <?php if(old('is_manager') && old('is_manager') =='1'): ?> checked <?php elseif( $role->is_manager == '1'): ?> checked <?php endif; ?>>
                                            <label class="custom-control-label" for="is_manager_yes"><?php echo e(__('content.yes')); ?></label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input  value="0" type="radio" class="custom-control-input" id="is_manager_no" name="is_manager" <?php if(old('is_manager') && old('is_manager') =='0'): ?> checked <?php elseif( $role->is_manager == '0'): ?> checked <?php endif; ?>>
                                            <label class="custom-control-label" for="is_manager_no"><?php echo e(__('content.no')); ?></label>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('registrable', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('registrable') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.registrable')); ?></label>
                                    <div class="col-md-9">
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input <?php echo e($readonly ?? ''); ?> value="1" type="radio" class="custom-control-input" id="registrable_yes" name="registrable" <?php if(old('registrable') && old('registrable') =='1'): ?> checked <?php elseif( $role->registrable == '1'): ?> checked <?php endif; ?>>
                                            <label class="custom-control-label" for="registrable_yes"><?php echo e(__('content.yes')); ?></label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input  value="0" type="radio" class="custom-control-input" id="registrable_no" name="registrable" <?php if(old('registrable') && old('registrable') =='0'): ?> checked <?php elseif( $role->registrable == '0'): ?> checked <?php endif; ?>>
                                            <label class="custom-control-label" for="registrable_no"><?php echo e(__('content.no')); ?></label>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('settings', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('settings') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.settings')); ?></label>
                                    <div class="col-md-9">
                                        <?php $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sett): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="custom-control custom-checkbox">
                                                <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input" id="setting_<?php echo e($sett); ?>" name="settings[]" value="<?php echo e($sett); ?>"
                                                <?php if(is_array(old('settings')) && in_array($sett, old('settings'))
                                                || empty(old('permissions')) && is_array($role_settings) && in_array($sett,$role_settings)): ?>
                                                checked
                                                <?php endif; ?>>
                                                <label class="custom-control-label" for="setting_<?php echo e($sett); ?>"><?php echo e(__('content.'.$sett)); ?></label>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('authorized_ams', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row authorized_ams <?php echo e($errors->has('authorized_ams') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.account_managers')); ?> </label>
                                    <div class="col-md-9">
                                        <select name="authorized_ams[]" class="form-control selectpicker"
                                            data-live-search="true" data-live-search-placeholder="<?php echo e(__('content.account_managers')); ?>"
                                            title="<?php echo e(__('content.account_managers')); ?>" data-actions-box="true" multiple>
                                            <?php $__currentLoopData = $account_managers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manager_id => $manager_name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($manager_id); ?>" <?php if(is_array($role->authorized_ams) && in_array($manager_id, $role->authorized_ams)): ?> selected <?php endif; ?>>
                                                    <?php echo e($manager_name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php if($errors->has('authorized_ams')): ?>
                                            <span class="help-block">
                                                <strong><?php echo e($errors->first('authorized_ams')); ?></strong>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('authorized_roles', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('authorized_roles') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.authorized_roles')); ?></label>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input" id="all_roles"
                                                        data-checkbox-all-target="select_all_roles" name="authorized_roles[]" value="all"
                                                        <?php if(old('authorized_roles')
                                                            && in_array('all', old('authorized_roles'))
                                                            || ($role->authorized_roles && in_array('all', json_decode($role->authorized_roles)))): ?>
                                                            checked
                                                        <?php endif; ?>
                                                        >
                                                    <label class="custom-control-label" for="all_roles"><?php echo e(__('content.all_roles')); ?></label>
                                                </div>
                                            </div>
                                        </div>
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($loop->index % 3 == '0'): ?>
                                                <div class="row">
                                            <?php endif; ?>
                                            <div class="col-md-4 col-sm-12 col-xs-12 select_all_roles">
                                                <div class="custom-control custom-checkbox">
                                                    <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input" id="authorized_role_<?php echo e($r->id); ?>" name="authorized_roles[]" value="<?php echo e($r->id); ?>"
                                                    <?php if(is_array(old('authorized_roles')) && in_array($r->id, old('authorized_roles'))
                                                    || is_array($authorized_roles) && in_array($r->id, $authorized_roles)): ?>
                                                    checked
                                                    <?php endif; ?>
                                                    >
                                                    <label class="custom-control-label" for="authorized_role_<?php echo e($r->id); ?>">
                                                        <b class="text-uppercase"><?php echo e($r->name); ?></b>: <?php echo e($r->display_name); ?>

                                                    </label>
                                                </div>
                                            </div>
                                            <?php if((($loop->index + 1) % 3 == '0') || $loop->last): ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('website_ids', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('website_ids') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.manage_websites')); ?> </label>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input" id="all_websites"
                                                        data-checkbox-all-target="select_all_websites" name="website_ids[]" value="all"
                                                        <?php if(old('website_ids') && in_array('all', old('website_ids')) || ($role->website_ids && in_array('all', $role->website_ids))): ?>
                                                            checked
                                                        <?php endif; ?>>
                                                    <label class="custom-control-label" for="all_websites"><?php echo e(__('content.all_websites')); ?></label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12 select_all_websites">
                                                <?php $__currentLoopData = $all_websites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $website): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="custom-control custom-checkbox">
                                                        <input <?php echo e($readonly ?? ''); ?>

                                                        <?php if(old('website_ids') && in_array('all', old('website_ids')) || ($role->website_ids && in_array($website->id, $role->website_ids))): ?>
                                                            checked
                                                        <?php endif; ?>
                                                        name="website_ids[]" value="<?php echo e($website->id); ?>" type="checkbox" class="custom-control-input" id="website_<?php echo e($website->id); ?>">
                                                        <label class="custom-control-label" for="website_<?php echo e($website->id); ?>"><?php echo e($website->name); ?></label>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($errors->has('website_ids')): ?>
                                                    <span class="help-block">
                                                        <strong><?php echo e($errors->first('website_ids')); ?></strong>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('authorized_regions', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row authorized_regions <?php echo e($errors->has('authorized_regions') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.authorized_regions')); ?></label>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input" id="all_countries" name="authorized_regions[]" value="all"
                                                    <?php if(old('authorized_regions') && in_array('all', old('authorized_regions'))
                                                        || (is_array($enabled_countries) && in_array('all', $enabled_countries))): ?> checked <?php endif; ?>>
                                                    <label class="custom-control-label" for="all_countries"><?php echo e(__('content.all_countries')); ?></label>
                                                </div>
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input type="checkbox" class="custom-control-input check_all" id="select_all_country">
                                                    <label class="custom-control-label" for="select_all_country"><?php echo e(__('content.select_all')); ?></label>
                                                </div>
                                            </div>

                                            <?php $__currentLoopData = $all_countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $region => $countries): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="col-md-4 col-sm-6 col-xs-12 mt-2 mb-2 all_countries_checkboxes">
                                                    <h5 class="text-uppercase"><strong><?php echo e($region); ?></strong></h5>
                                                    <div class="custom-control custom-checkbox">
                                                        <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input check_regions" region="<?php echo e($region); ?>" id="select_<?php echo e(str_replace(' ', '_', $region)); ?>">
                                                        <label class="custom-control-label" for="select_<?php echo e(str_replace(' ', '_', $region)); ?>"><?php echo e(__('content.select_all')); ?></label>
                                                    </div>
                                                    <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="custom-control custom-checkbox">
                                                            <input type="checkbox" class="custom-control-input all" id="country_ids_<?php echo e($country->id); ?>" name="authorized_regions[]"
                                                            value="<?php echo e($country->id); ?>" region="<?php echo e($country->region); ?>"
                                                            <?php if(old('authorized_regions') && in_array($country->id, old('authorized_regions')) || (is_array($enabled_countries)
                                                                && in_array($country->id, $enabled_countries))): ?> checked <?php endif; ?>>
                                                            <label class="custom-control-label" for="country_ids_<?php echo e($country->id); ?>"><?php echo e($country->name); ?></label>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                            <?php if($errors->has('authorized_regions')): ?>
                                                <span class="help-block">
                                                    <strong><?php echo e($errors->first('authorized_regions')); ?></strong>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('managed_roles', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row managed_roles <?php echo e($errors->has('managed_roles') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.managed_roles')); ?></label>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input type="checkbox" class="custom-control-input" id="all_managed_roles"
                                                        data-checkbox-all-target="select_all_managed_roles" name="managed_roles[]" value="all"
                                                        <?php if(old('managed_roles') && in_array('all', old('managed_roles'))): ?>
                                                            checked
                                                        <?php elseif(in_array('all', $role->managed_roles ?? [])): ?>
                                                            checked
                                                        <?php endif; ?>
                                                        >
                                                    <label class="custom-control-label" for="all_managed_roles"><?php echo e(__('content.all_roles')); ?></label>
                                                </div>
                                            </div>
                                        </div>
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($loop->index % 3 == '0'): ?>
                                                <div class="row">
                                            <?php endif; ?>
                                            <div class="col-md-4 col-sm-12 col-xs-12 select_all_managed_roles">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" class="custom-control-input" id="managed_role_<?php echo e($r->id); ?>" name="managed_roles[]" value="<?php echo e($r->id); ?>"
                                                    <?php if(is_array(old('managed_roles')) && in_array($r->id, old('managed_roles'))): ?>
                                                        checked
                                                    <?php elseif(in_array($r->id, $role->managed_roles ?? [])): ?>
                                                        checked
                                                    <?php endif; ?>
                                                    >
                                                    <label class="custom-control-label" for="managed_role_<?php echo e($r->id); ?>">
                                                        <b class="text-uppercase"><?php echo e($r->name); ?></b>: <?php echo e($r->display_name); ?>

                                                    </label>
                                                </div>
                                            </div>
                                            <?php if((($loop->index + 1) % 3 == '0') || $loop->last): ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(in_array('permissions', $translatable_fields) || $role->locale == 'en'): ?>
                                <div class="form-group row <?php echo e($errors->has('permissions') ? ' has-error' : ''); ?>">
                                    <label class="col-md-3 label-control"><?php echo e(__('content.permissions')); ?></label>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="custom-control custom-checkbox">
                                                    <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input check_all2" id="all_permissions">
                                                    <label class="custom-control-label" for="all_permissions"><?php echo e(__('content.select_all')); ?></label>
                                                </div>
                                            </div>
                                            <?php $title = 0 ?>
                                            <?php $c = 0 ?>

                                            <?php $__currentLoopData = $permission_arr; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table => $permissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php $title += 1; ?>
                                                <?php $c += 1; ?>

                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12 mt-1 mb-2">
                                                    <h5 class="text-uppercase"><strong><?php echo e(str_replace("_"," ",$table)); ?></strong></h5>
                                                    <div class="custom-control custom-checkbox">
                                                        <input <?php echo e($readonly ?? ''); ?> type="checkbox" class="custom-control-input select_all<?php echo e($title); ?> check_table" id="select_all_<?php echo e($table); ?>">
                                                        <label class="custom-control-label" for="select_all_<?php echo e($table); ?>"><?php echo e(__('content.select_all')); ?></label>
                                                    </div>
                                                    <?php $p_id = 1; ?>
                                                    <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission => $description): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="custom-control custom-checkbox">
                                                            <input <?php echo e($readonly ?? ''); ?> type="checkbox"
                                                            class="custom-control-input x<?php echo e($c); ?> all2"
                                                            name="permissions[]"
                                                            id="permissions_<?php echo e($c); ?>_<?php echo e($p_id); ?>"
                                                            value="<?php echo e($permission); ?>" <?php if(is_array(old('permissions')) && in_array($permission,old('permissions')) || empty(old('permissions')) && is_array($role_permissions) && in_array($permission,$role_permissions)): ?> checked <?php endif; ?>>
                                                            <label class="custom-control-label" for="permissions_<?php echo e($c); ?>_<?php echo e($p_id); ?>">
                                                                <?php if($description): ?>
                                                                    <i data-title="tooltip" data-placement="top"
                                                                    data-original-title="<?php echo e($description); ?>"
                                                                    class="la la-question-circle text-info"></i>
                                                                <?php endif; ?>

                                                                <?php echo e(str_replace("-"," ",str_replace("_"," ",$permission))); ?>

                                                            </label>
                                                        </div>
                                                        <?php $p_id ++ ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                                <script type="application/javascript">
                                                    $(document).ready(function(){
                                                        $(".select_all<?php echo e($title); ?>").click(function(){
                                                            $(".x<?php echo e($c); ?>").prop('checked', $(this).prop('checked'));
                                                        });
                                                    });
                                                </script>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <?php if($errors->has('permissions')): ?>
                                            <span class="help-block">
                                                <strong><?php echo e($errors->first('permissions')); ?></strong>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(!$readonly): ?>
                                <div class="form-actions">
                                    <div class="form-group row">
                                        <div class="offset-md-3 col-md-9">
                                            <button type="submit" class="btn btn-md btn-outline-dark btn-loading"><?php echo e(__('content.update')); ?></button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('templates.crm.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\projects\ingotbrokers\resources\views/templates/crm/admin/role/edit.blade.php ENDPATH**/ ?>