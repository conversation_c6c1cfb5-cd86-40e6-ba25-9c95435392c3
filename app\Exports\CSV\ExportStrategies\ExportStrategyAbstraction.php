<?php

namespace App\Exports\CSV\ExportStrategies;

use Closure;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\SerializableClosure\Exceptions\PhpVersionNotSupportedException;
use <PERSON><PERSON>\SerializableClosure\SerializableClosure;

abstract class ExportStrategyAbstraction
{
    public array $extra = [];

    /**
     * @var Closure[]
     */
    protected ?array $callbacks;

    /**
     * @param  mixed  $data
     * @return ExportStrategyAbstraction
     */
    abstract public function setData(mixed $data): ExportStrategyAbstraction;

    /**
     * @param $csvFile
     * @return void
     */
    abstract public function handle(&$csvFile): void;

    /**
     * @param  string  $key
     * @param  Closure|null  $callbackHandling
     */
    public function setCallbackByKey(string $key, ?Closure $callbackHandling): void
    {
        $this->callbacks[$key] = $callbackHandling;
    }

    /**
     * @param $csvFile
     * @param $data
     * @return void
     */
    protected function fillToFile(&$csvFile, $data): void
    {
        $callback = $this->callbacks['handle'];

        foreach ($data as $fields) {
            if ($callback) {
                $fields = $callback($fields, $this);
            } else {
                $fields = $fields instanceof Model ? $fields->getOriginal() : (array) $fields;
            }

            fputcsv($csvFile, $fields, ',', '"');
        }
    }

    /**
     * @param  mixed  $result
     * @param $csvFile
     * @return void
     */
    protected function fillHeaders(mixed $result, $csvFile): void
    {
        $callback = $this->callbacks['handle'];
        $first = array_first($result);
        $first = ($callback ? $callback($first, $this) : (array) $first);
        fputcsv($csvFile, array_keys($first), ',', '"');
    }

    /**
     * @throws PhpVersionNotSupportedException
     */
    public function __serialize(): array
    {
        return [
            'callbackBefore' => isset($this->callbacks['before']) && !empty($this->callbacks['before'])
                ? serialize(new SerializableClosure($this->callbacks['before']))
                : null,
            'callbackAfter' => isset($this->callbacks['after']) && !empty($this->callbacks['after'])
                ? serialize(new SerializableClosure($this->callbacks['after']))
                : null,
            'callbackHandling' => isset($this->callbacks['handle']) && !empty($this->callbacks['handle'])
                ? serialize(new SerializableClosure($this->callbacks['handle']))
                : null,
            'extra' => serialize($this->extra),
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->callbacks['before'] = $data['callbackBefore'] !== null ? unserialize($data['callbackBefore'])->getClosure() : null;
        $this->callbacks['after'] = $data['callbackAfter'] !== null ? unserialize($data['callbackAfter'])->getClosure() : null;
        $this->callbacks['handle'] = $data['callbackHandling'] !== null ? unserialize($data['callbackHandling'])->getClosure() : null;
        $this->extra = unserialize($data['extra']);
    }
}
