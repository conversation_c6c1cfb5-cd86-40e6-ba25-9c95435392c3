<?php

namespace App\Exports;

use App\Http\Controllers\Admin\PaymentMethodReportController;
use App\Models\Country;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PaymentMethodExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    protected array $countries = [];

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->countries = Country::query()->pluck('name', 'id')->toArray();
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(PaymentMethodReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Payment Method',
            'Min Deposit',
            '<PERSON> Deposit',
            'Deposit Fees',
            'Min Withdrawal',
            'Max Withdrawal',
            'Withdrawal Fees',
            'Countries',
        ];
    }

    public function map($method): array
    {
        $data = [
            $method->name,
            $method->min_deposit ?? 'NULL',
            $method->max_deposit ?? 'NULL',
            $method->deposit_fees ?? 'NULL',
            $method->min_withdrawal ?? 'NULL',
            $method->max_withdrawal ?? 'NULL',
            $method->withdrawal_fees ?? 'NULL',
        ];

        $method_countries = $method->countries;
        if (strpos($method_countries, 'all')) {
            $data[] = 'All Countries';
        } else {
            $method_countries = json_decode($method_countries);
            if ($method_countries == null) {
                $data[] = 'No Countries';
            } else {
                $countries_array = [];
                foreach ($method_countries as $countryId) {
                    $countries_array[] = $this->countries[$countryId];
                }
                $data[] = implode(', ', (array) $countries_array);
            }
        }

        return $data;
    }
}
