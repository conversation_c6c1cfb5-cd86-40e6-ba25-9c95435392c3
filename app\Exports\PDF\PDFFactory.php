<?php

namespace App\Exports\PDF;

use App\Exports\PDF\Adapters\DumpPdfAdapter;
use App\Exports\PDF\Adapters\MPDFAdapter;
use hisorange\BrowserDetect\Exceptions\Exception;

class PDFFactory
{
    /**
     * @param $type
     * @return PDF
     *
     * @throws Exception
     */
    public static function make($type): PDF
    {
        return match ($type) {
            'mpdf' => new MPDFAdapter(),
            'dump-pdf' => new DumpPdfAdapter(),
            default => throw new Exception("$type is not allowed")
        };
    }
}
