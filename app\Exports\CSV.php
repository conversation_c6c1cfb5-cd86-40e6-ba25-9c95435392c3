<?php

namespace App\Exports;

use App\Exports\CSV\ExportStrategies\Chunkier;
use App\Exports\CSV\ExportStrategies\ExportStrategyAbstraction;
use Closure;

class CSV extends ExportManger
{
    protected array $extra = [];

    protected ExportStrategyAbstraction $strategy;

    public function __construct($strategy = null)
    {
        $this->strategy = $strategy ?? new Chunkier();
        parent::__construct('csv');
        $this->setFileName('default');
    }

    public function callbackBeforeHandle(?Closure $callbackHandling): CSV
    {
        $this->strategy->setCallbackByKey('before', $callbackHandling);

        return $this;
    }

    /**
     * @param  Closure|null  $callbackHandling
     * @return CSV
     */
    public function callbackAfterHandle(?Closure $callbackHandling): CSV
    {
        $this->strategy->setCallbackByKey('after', $callbackHandling);

        return $this;
    }

    /**
     * @param  Closure|null  $callbackHandling
     * @return CSV
     */
    public function callbackHandle(?Closure $callbackHandling): CSV
    {
        $this->strategy->setCallbackByKey('handle', $callbackHandling);

        return $this;
    }

    /**
     * @return ExportStrategyAbstraction
     */
    public function getStrategy(): ExportStrategyAbstraction
    {
        return $this->strategy;
    }

    /**
     * @return string
     */
    public function getFileNamePath(): string
    {
        return storage_path("csv/{$this->fileName}");
    }

    /**
     * @param  array  $extra
     */
    public function setExtra(array $extra): void
    {
        $this->extra = $extra;
    }

    public function setData(object|array $data): static
    {
        $this->strategy->setData($data); // TODO: Change the autogenerated stub

        return $this;
    }

    protected function template(): mixed
    {
        // Open File
        $csvFile = fopen($this->getFileNamePath(), 'w');

        // Insert the UTF-8 BOM in the file
        fwrite($csvFile, $bom = (chr(0xEF).chr(0xBB).chr(0xBF)));

        if (!empty($this->extra)) {
            foreach ($this->extra as $file) {
                $result = [];
                array_walk_recursive($file, function ($item) use (&$result) {
                    $result[] = $item;
                });
                fputcsv($csvFile, $result, ',', '"');
            }
            fputcsv($csvFile, [], ',', '"');
        }

        $this->strategy->handle($csvFile);

        // Close the file
        fclose($csvFile);

        return null;
    }
}
