<?php

namespace App\Exports;

use App\Http\Controllers\Admin\ClientTimelineReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ClientTimelineExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(ClientTimelineReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Client Name',
            'Employee Name',
            'Action',
            'Comment',
            'Time',
        ];
    }

    public function map($row): array
    {
        return [
            $row->client,
            $row->employee,
            $row->action_type,
            $row->comment,
            $row->created_at,
        ];
    }
}
