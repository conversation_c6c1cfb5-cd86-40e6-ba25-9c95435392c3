<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use App\Http\Controllers\Admin\CellxpertDealController;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class CellxpertDealExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(CellxpertDealController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID',
            'App ID',
            'Platform',
            'Position ID',
            'Open Price',
            'Close Price',
            'Symbol',
            'Volume',
            'Lot Volume',
            'Spread',
            'CBV',
            'Open Date',
            'Close Date',
            'Base Currency',
            'P&L',
            'Status',
            'Created At',
        ];
    }

    public function map($row): array
    {
        return [
            $row->id ?? '-',
            $row->user_id ?? '-',
            $row->trading_platform ?? '-',
            $row->position_id ?? '-',
            $row->open_price ?? '-',
            $row->close_price ?? '-',
            $row->symbol ?? '-',
            $row->volume ?? '-',
            $row->lot_volume ?? '-',
            $row->spread ?? '-',
            $row->cbv ?? '-',
            $row->open_date ?? '-',
            $row->close_date ?? '-',
            $row->base_currency ?? '-',
            $row->pl ?? '-',
            $row->status ?? '-',
            $row->created_at ?? '-',
        ];
    }
}
