<?php

namespace App\Exports;

use App\Models\MtAccount;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use App\Http\Controllers\Admin\AgentActivityReportController;

class AgentActivityExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private $className;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        $query = app(AgentActivityReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );

        $this->className = new AgentActivityReportController();

        return $query;
    }

    public function headings(): array
    {
        return [
            'App ID',
            'Applicant Name',
            'Applicant Email',
            'MT Login',
            'Group Name',
            'Group Type',
            'Creation Date',
            'Number of Req Accounts',
            'Number of Req Apps Available',
            'Number of Traded lots',
            'Last Trade Date',
        ];
    }

    public function map($row): array
    {
        $prepared_data = [];

        $key = 0;
        $data = [];

        foreach ($row->mtGroupShareds as $mtGroupShared) {
            $app_ids = $mtGroupShared->mtGroup->mtAccounts->pluck('application_id');

            $mtAccounts = MtAccount::query()->whereHas('application.appRelation.parentApplication', function ($parentApp) use ($row) {
                $parentApp->where('id', $row->id);
            })->whereIntegerInRaw('application_id', function ($query) use ($mtGroupShared) {
                $query->from('mt_accounts')
                    ->select('application_id')
                    ->join('mt_groups', 'mt_groups.id', '=', 'mt_accounts.mt_group_id')
                    ->where('mt_groups.id', $mtGroupShared->mt_group_id)
                    ->whereNull('mt_accounts.deleted_at');
            })
            ->where('mt_group_id', $mtGroupShared->mt_group_id)
            ->pluck('login')
            ->toArray();

            if ($mtAccounts) {
                $mt_shared_data = $this->className->getDate($mtGroupShared->mtGroup->trading_platform_id, $mtAccounts);

                $apps_count = MtAccount::query()
                    ->whereHas('application.appRelation.parentApplication', function ($parentApp) use ($row) {
                        $parentApp->where('id', $row->id);
                    })
                    ->whereIntegerInRaw('application_id', $app_ids)
                    ->distinct('application_id')
                    ->count();

                $prepared_data[$row->id]['group'][$mtGroupShared->id]['registered_apps'] = $apps_count;
                $prepared_data[$row->id]['group'][$mtGroupShared->id]['registered_mt'] = count($mtAccounts);
                $prepared_data[$row->id]['group'][$mtGroupShared->id]['traded_lots'] = $mt_shared_data['total_traded_lots'] ?? 0;
                $prepared_data[$row->id]['group'][$mtGroupShared->id]['last_traded_date'] = $mt_shared_data['last_traded_date'] ?? null ;
            }

            $data[$key][] = $row->appIdentifierId();
            $data[$key][] = $row->mainUser->getUserHolderName();
            $data[$key][] = $row->mainUser->email;
            $data[$key][] = $mtGroupShared->mtAccount->login ?? '-';
            $data[$key][] = $mtGroupShared->mtGroup->name ?? '-';
            $data[$key][] = 'Shared';
            $data[$key][] = $mtGroupShared->created_at?->format('Y-m-d') ?? '-';
            $data[$key][] = $prepared_data[$row->id]['group'][$mtGroupShared->id]['registered_mt'] ?? '-';
            $data[$key][] = $prepared_data[$row->id]['group'][$mtGroupShared->id]['registered_apps'] ?? '-';
            $data[$key][] = $prepared_data[$row->id]['group'][$mtGroupShared->id]['traded_lots'] ?? '-';
            $data[$key][] = $prepared_data[$row->id]['group'][$mtGroupShared->id]['last_traded_date'] ?? '-';
            $key += 1;
        }

        foreach ($row->agentMtAccount as $mtAccount) {
            if ($mtAccount->agentMtGroup) {
                $mtAccounts = $mtAccount->agentMtGroup->mtAccounts()->pluck('login')->toArray();
                $platform = $mtAccount->agentMtGroup->trading_platform_id;
                if ($mtAccounts) {
                    $mt_group_data = $this->className->getDate($platform, $mtAccounts);
                    $prepared_data[$row->id]['account'][$mtAccount->id]['traded_lots'] = $mt_group_data['total_traded_lots'] ?? 0;
                    $prepared_data[$row->id]['account'][$mtAccount->id]['last_traded_date'] = $mt_group_data['last_traded_date'] ?? null ;
                }
            }

            $data[$key][] = $row->appIdentifierId();
            $data[$key][] = $row->mainUser->getUserHolderName();
            $data[$key][] = $row->mainUser->email;
            $data[$key][] = $mtAccount->login ?? '-';
            $data[$key][] = $mtAccount->agentMtGroup?->name ?? '-';
            $data[$key][] = 'Owned';
            $data[$key][] = $mtAccount->created_at?->format('Y-m-d') ?? '-';
            $data[$key][] = $mtAccount->agentMtGroup?->mtAccounts?->count() ?? '-';
            $data[$key][] = $mtAccount->agentMtGroup?->mtAccounts()?->distinct('application_id')?->count() ?? '-';
            $data[$key][] = $prepared_data[$row->id]['account'][$mtAccount->id]['traded_lots'] ?? '-';
            $data[$key][] = $prepared_data[$row->id]['account'][$mtAccount->id]['last_traded_date'] ?? '-';
            $key += 1;
        }

        return $data;
    }
}
