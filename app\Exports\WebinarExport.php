<?php

namespace App\Exports;

use App\Http\Controllers\Admin\WebinarController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class WebinarExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(WebinarController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Title',
            'Speaker',
            'Speaker Brief',
            'Description',
            'Agenda'
        ];
    }

    public function map($row): array
    {
        return [

            $row->title ?? '-',
            $row->influencer_name ?? '-',
            rip_tags($row->speaker_brief) ,
            rip_tags($row->description) ,
            rip_tags($row->agenda)
        ];
    }
}
