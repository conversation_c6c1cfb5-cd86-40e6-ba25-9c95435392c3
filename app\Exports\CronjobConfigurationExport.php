<?php

namespace App\Exports;

use App\Http\Controllers\Admin\CronjobConfigurationsController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class CronjobConfigurationExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(CronjobConfigurationsController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Signature',
            'Description',
            'Is Active',
            'Created Date',
            'Updated On',
        ];
    }

    public function map($row): array
    {
        return [
            $row->name ?? '-',
            $row->command ?? '-',
            $row->description ?? '-',
            $row->is_active == 1 ? 'Yes' : 'No',
            $row->created_at ?? '-',
            $row->updated_at ?? '-',
        ];
    }
}
