<?php

namespace App\Helpers\ApiCall\Src\Handlers;

use Nette\Utils\Json;

class Data
{
    protected $data;

    /**
     * @param $data
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function toArray(): array
    {
        return (array) $this->data;
    }

    /**
     * @throws \Nette\Utils\JsonException
     */
    public function toJson(): string
    {
        return Json::encode($this->data);
    }

    public function toCollection(): \Illuminate\Support\Collection
    {
        return collect($this->data);
    }

    public function toObject(): object
    {
        return (object) $this->data;
    }
}
