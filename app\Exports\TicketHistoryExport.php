<?php

namespace App\Exports;

use App\Http\Controllers\Admin\TicketController;
use App\Models\Ticket;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class TicketHistoryExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private array $statuses;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->statuses = Ticket::$statuses;
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(TicketController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Ticket No.',
            'Username',
            'Country',
            'Category',
            'Ticket description',
            'First Reply By',
            'Last Reply By',
            'Replies',
            'Re-Opened',
            'Accountability',
            'Created Date',
            'Updated On',
            'Closed By',
            'Resolved Period',
            'Within KPI',
            'First Response Time',
            'Within KPI',
            'Status',
            'Ticket Rate',
        ];
    }

    public function map($row): array
    {
        $statuses = Ticket::$statuses;

        $data[] = [
            $row->number,
            $row->user->getUserHolderName(),
            $row->user->getUserCountry(),
            $row->subject_subject,
            rip_tags($row->description),
            $row->firstAgentTicketConversation ? $row->firstAgentTicketConversation->user->getUserHolderName() : '-',
            $row->firstAgentTicketConversation ? $row->latestTicketConversation->user->getUserHolderName() : '-',
            $row->ticket_conversations_count,
            $row->opendCount(),
            $row->role_display_name,
            $row->created_at,
            $row->updated_at,
            $row?->closedBy?->full_name ?? '-' ,
            $row->getExecutionTime(),
            is_null($row->satisfyingKpiCS()) ? '-' : ($row->satisfyingKpiCS() ? __('content.yes') : __('content.no')),
            $row->getFirstResponseTime(),
            is_null($row->satisfyingKpiFRT()) ? '-' : ($row->satisfyingKpiFRT() ? __('content.yes') : __('content.no')),
            $statuses[$row->status] ? __('content.' . $statuses[$row->status]) : $row->status,
            isset($row->ticket_rate_rate) && isset($rates[$row->ticket_rate_rate]) ? $row->ticketRate::$rates[$row->ticket_rate_rate] : __('content.not_rated'),
        ] ;

        if (isset($this->requestData['include_content_in_export'])) {
            $ticketConversations = $row->ticketConversations;
            foreach ($ticketConversations as $conversation) {
                $data[] = [
                    null ,
                    $conversation->user->first_name . ' ' . $conversation->user->last_name ?? '',
                    null ,
                    null ,
                    rip_tags($conversation->description),
                    $conversation->created_at->toDateTimeString(),
                ];
            }
            $data[] = [];
        }

        return $data;
    }
}
