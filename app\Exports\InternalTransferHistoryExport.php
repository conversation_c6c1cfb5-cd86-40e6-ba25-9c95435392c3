<?php

namespace App\Exports;

use App\Http\Controllers\Admin\TransactionController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class InternalTransferHistoryExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(TransactionController::class)->internalTransferHistory(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ticket',
            'login',
            'platform',
            'currency',
            'name',
            'comment',
            'profit',
            'open_time',
        ];
    }

    public function map($row): array
    {
        return [
            $row['ticket'],
            $row['login'],
            $row['platform'],
            $row['currency'],
            $row['name'],
            $row['comment'],
            $row['profit'],
            $row['open_time'],
        ];
    }
}
