<?php

namespace App\Exports\PDF\Adapters;

use App\Exports\PDF\PDF;
use Illuminate\Support\Facades\View;
use Mpdf\Mpdf;
use Mpdf\MpdfException;

class MPDFAdapter extends PDF
{
    /**
     * @return mixed
     *
     * @throws MpdfException
     */
    protected function template(): mixed
    {
        $mpdf = new Mpdf(['tempDir' => storage_path('app/uploads')]);
        $mpdf->SetDisplayMode('fullpage');
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;
        $html = $this->loadView($this->view, ['data' => $this->data]);
        $mpdf->WriteHTML($html);

        return $mpdf->Output($this->fileName, 'D');
    }

    /**
     * @param $view
     * @param $data
     * @return string
     */
    protected function loadView($view, $data): string
    {
        return View::make($view, $data)->render();
    }
}
