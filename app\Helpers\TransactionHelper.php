<?php

namespace App\Helpers;

use App\Events\Transaction\DepositApproved;
use App\Events\Transaction\DepositRejected;
use Exception;
use App\Models\Role;
use App\Models\User;
use App\Models\Comment;
use App\Models\Currency;
use App\Mail\FirstDeposit;
use App\Mail\FailedDeposit;
use App\Models\Transaction;
use App\Mail\ApproveDeposit;
use App\Models\AppPaymentBank;
use App\Models\PaymentGateway;
use App\Models\TransactionType;
use App\Traits\ManafIntegration;
use App\Models\AppPaymentGateway;
use Illuminate\Support\Facades\Mail;
use App\Models\TransactionAttachement;
use App\Jobs\EnableTradingAccountTradeRight;
use App\Libraries\Manaf\TransferDepositions;
use App\Mail\NotifyAccountManagerOnFailedDeposits;
use App\Models\Cellxpert\Transaction as CellxpertTransaction;

class TransactionHelper
{
    use ManafIntegration;

    public static function approve($tempFundsObj, $extraComment = [])
    {
        if ($tempFundsObj) {
            $fee = $mt_ticket_no = $recCurrency = null;

            $user = $tempFundsObj->user;
            $application = $tempFundsObj->application;
            $paymentGateway = $tempFundsObj->paymentGateway;
            $paymentGatewayId = $tempFundsObj->payment_gateway_id;
            $reqAmount = $tempFundsObj->req_amount;
            $reqCurrency = $tempFundsObj->req_currency;
            $reqCurrencyCode = $tempFundsObj->reqCurrency->code;
            $applicationId = $tempFundsObj->application_id;
            $extraData = $tempFundsObj->extra_data;
            $attachment = $tempFundsObj->attachment;
            $comment = $tempFundsObj->comment;
            $executionType = $paymentGateway->deposit_type;
            $recMtAccountId = $tempFundsObj->rec_mt_account_id;
            $siteBank = $tempFundsObj?->site_bank_id;
            $extraTransactionId = $tempFundsObj->extra_transaction_id;
            $exchangeRate = $tempFundsObj->exchange_rate ?? null;
            $recAmount = $tempFundsObj->rec_amount ?? null;
            $status = null;

            $transactionType = Transaction::DEPOSIT;

            if ($user && $paymentGateway && $tempFundsObj && $tempFundsObj->status != '1') {
                $transactionTypeId = TransactionType::query()->where('key', strtolower($transactionType))->value('id');
                // check if the transaction already exists
                // check if the transaction does not have an extra transaction id
                // in this case, we will make it manual execution
                if ($extraTransactionId) {
                    $transactionExists = Transaction::query()->where([
                        'transaction_type_id' => $transactionTypeId,
                        'source' => $paymentGatewayId,
                        'extra_transaction_id' => $extraTransactionId,
                    ])->exists();

                    if ($transactionExists) {
                        $executionType = 0;
                        $extraComment[] = 'The transaction reference already exists in another transaction';
                    }
                } else {
                    $executionType = 0;
                    $extraComment[] = 'The transaction reference is missing';
                }

                $hasWallet = false;
                if ($user->can('access-app-wallet')) {
                    $hasWallet = true;
                }

                $accountCurrency = null;

                if ($application->mainAppWallet) { // for E-Wallet
                    $accountCurrency = $application->mainAppWallet->currency->code ?? null;
                } elseif ($application->appMtWallet) { // for MT Wallet
                    $accountCurrency = $application->appMtWallet->mtGroup->currency->code ?? null;
                    $recMtAccountId = $application->appMtWallet->id ?? null;
                } elseif ($recMtAccountId) {
                    $accountCurrency = $tempFundsObj->recMtAccount->mtGroup->currency->code ?? null;
                }

                if ($accountCurrency && $reqCurrencyCode) {
                    if ($accountCurrency == $reqCurrencyCode) {
                        // direct deposit
                        $recAmount = $reqAmount;
                        $recCurrency = $reqCurrency;
                        $exchangeRate = '1';
                    } else {
                        if (!$exchangeRate || !$recAmount) {
                            // convert prices
                            $pairDetails = ExchangeRateHelper::getExchangeRate([
                                'transfer_amount' => $reqAmount,
                                'from_currency' => $reqCurrencyCode,
                                'to_currency' => $accountCurrency,
                            ]);

                            if ($pairDetails['status'] == 'success') {
                                $recAmount = $pairDetails['data']['net_amount'];
                                $exchangeRate = $pairDetails['data']['exchange_rate'];
                            }
                        }
                    }

                    $recCurrency = Currency::query()->where('code', $accountCurrency)->value('id');
                } else {
                    $extraComment[] = 'The application does not have a wallet or a MT account';
                }

                if ($recAmount && $recCurrency && $exchangeRate) {
                    // get deposit fees
                    $depositFees = PaymentGateway::getPaymentDepositFees($paymentGatewayId, $user);

                    // Calculate the fees for future reference
                    $fee = $recAmount * $depositFees;

                    // calculate what ingot will get
                    $recAmount = round($recAmount - ($recAmount * $depositFees), 6);

                    $disabledAutomation = $user->application->disableApplicationFeature()->whereHas('disableType', function ($t) {
                        $t->where('key', 'disable_automation');
                    })->orderBy('created_at', 'desc')->first();

                    // Disable Automation
                    if ($disabledAutomation && $disabledAutomation->active) {
                        $executionType = 0;
                        $extraComment[] = 'Automation is disabled for this application';
                    }

                    if ($paymentGateway->require_verification) {
                        if ($tempFundsObj->app_payment_id) {
                            $verification = AppPaymentGateway::query()->where('id', $tempFundsObj->app_payment_id)->first();
                            if (!$verification || !$verification->verified) {
                                $executionType = 0;
                                $extraComment[] = 'The payment gateway is not verified';
                            }
                        } else {
                            $executionType = 0;
                            $extraComment[] = 'The financial information is not verified';
                        }
                    }

                    // deposit_type: 1 if instance, 0 if request
                    if ($executionType) {
                        if ($hasWallet) {
                            // update wallet and add the received amount
                            $appWallet = $user->application->appWallet()->first();
                            $status = Transaction::walletDeposit($appWallet, $recAmount);
                        } else {
                            // deposit to the metatrader account directly
                            $deposit = Transaction::mtDeposit($recMtAccountId, $recAmount);
                            if (array_key_exists('status', $deposit)) {
                                $status = $deposit['status'];
                            }

                            if (array_key_exists('ticket', $deposit)) {
                                $mt_ticket_no = $deposit['ticket'];
                            }
                        }

                        if ($status != 'success') {
                            $executionType = 0;
                            $extraComment[] = 'Failed to deposit the amount into wallet';
                        }
                    }

                    // update the transaction
                    $tempFundsObj->status = '1';
                    $tempFundsObj->rec_amount = $recAmount;
                    $tempFundsObj->rec_currency = $recCurrency;
                    $tempFundsObj->exchange_rate = $exchangeRate;
                } else {
                    $executionType = 0;
                    $extraComment[] = 'Failed to calculate the received amount and exchange rate';
                }

                // add the payment gateway if not added
                if ($extraData && !in_array($paymentGateway->slug, PaymentGateway::$ignored_store_fe_on_execute)) {
                    $extraData['application_id'] = $applicationId;
                    $extraData['payment_gateway_id'] = $paymentGatewayId;

                    if (array_key_exists('account_currency', $extraData)) {
                        $extraData['currency_id'] = $extraData['account_currency'];
                    }

                    if (in_array($paymentGateway->slug, PaymentGateway::$required_bank_gateways)) {
                        $appPaymentGateway = AppPaymentBank::query()->firstOrCreate($extraData);
                    } else {
                        $appPaymentGateway = AppPaymentGateway::query()->firstOrCreate($extraData);
                    }

                    $tempFundsObj->app_payment_id = $appPaymentGateway->id;
                }

                if ($executionType) {
                    $assignedRole = $user->role;
                } else {
                    $assignedRole = Role::roleAffiliateTransactionBased($transactionType, $application->affiliate_website_id);
                }

                $comment = implode(' | ', array_filter([$comment,  ($mt_ticket_no ? "MetaTrader Ticket No: {$mt_ticket_no}" : null)])) ?? null;
                $transaction = Transaction::query()->create([
                    'application_id' => $applicationId,
                    'account_manager_id' => $user->application?->getAccountManager()?->id,
                    'user_id' => $user->id,
                    'executed_by' => $user->id,
                    'executed_at' => $executionType ? date('Y-m-d H:i:s') : null,
                    'transaction_type_id' => $transactionTypeId,
                    'source' => $paymentGatewayId,
                    'req_amount' => $reqAmount,
                    'req_currency' => $reqCurrency,
                    'ip_address' => ip_address(),
                    'status' => $executionType,
                    'rec_amount' => $recAmount,
                    'rec_currency' => $recCurrency,
                    'exchange_rate' => $exchangeRate,
                    'role_id' => $assignedRole->id,
                    'app_payment_id' => $tempFundsObj->app_payment_id,
                    'extra_transaction_id' => $extraTransactionId,
                    'rec_mt_account_id' => $recMtAccountId,
                    'site_bank_id' => $siteBank,
                    'extra_details' => $tempFundsObj->extra_data,
                    'fee' => $fee,
                    'comment' => $comment,
                ]);

                // for mobile app , if the client has only one mt account , we will transfer the amount to this account
                Transaction::transferDepositToFirstRealAccount($transaction);

                if ($extraComment && count($extraComment)) {
                    foreach ($extraComment as $c) {
                        Comment::query()->create([
                            'comment' => $c,
                            'model_id' => $transaction->id,
                            'model_type' => Transaction::class,
                            'admin_user_id' => $user->id,
                            'action_type' => Comment::ACTION_TYPE_COMMENT,
                        ]);
                    }
                }

                if ($attachment) {
                    TransactionAttachement::query()->create([
                        'transaction_id' => $transaction->id,
                        'attachement' => $attachment,
                    ]);
                }

                if ($transaction->status == 1) {
                    # Create Cellxpert Transaction record
                    CellxpertTransaction::createNewTransaction($transaction);
                }

                // update the temporary funds with the transaction id
                $tempFundsObj->transaction_id = $transaction->id;
                $tempFundsObj->save();

                // delete the temporary funds
                $tempFundsObj->delete();

                try {
                    // send an emails
                    $userEmail = $user->email;
                    $userLocale = $user->language->abbreviation ?? 'en';

                    if ($executionType) {
                        if ($user->application->eligibleForManaf()) {
                            (new self())->integrationWithManaf(class: TransferDepositions::class, object: $transaction, method: 'transferDepositionByAccountType');
                        }

                        # Auto assign retention after deposit
                        LeadsAssignHelper::autoAssignRetention(
                            $application->id,
                            $user->country_id,
                            $application->app_type_id,
                            $user->citizenship_id,
                            $user->pref_lang,
                        );

                        # If first deposit && client under jsc regulation, notify CS department
                        if ($transaction->isFirstDeposit()) {
                            $cs_email = site_config('site_support_email');
                            Mail::to($cs_email)->locale('en')->send(new FirstDeposit($user, $transaction));

                            // if the client register from mobile app, enable trading accounts
                            if ($user->isMobileReferral()) {
                                EnableTradingAccountTradeRight::dispatch($user);
                            }
                        }

                        Mail::to($userEmail)->locale($userLocale)->send(
                            new ApproveDeposit(userObj:$user, lang:$userLocale)
                        );

                        DepositApproved::dispatch($transaction);
                    }

                    MailHelper::sendTransactionNotification($transaction);
                } catch (Exception $e) {
                }
            }

            return $executionType;
        }

        return 0;
    }

    public static function reject($tempFundsObj)
    {
        if ($tempFundsObj) {
            $user = $tempFundsObj->user;
            $paymentGateway = $tempFundsObj->paymentGateway;
            $extraTransactionId = ($tempFundsObj->extra_transaction_id ?? $tempFundsObj->order_id) ?? null;

            if ($user && $paymentGateway && $tempFundsObj && !$tempFundsObj->status) {
                $paymentGatewayId = $tempFundsObj->payment_gateway_id;
                $reqAmount = $tempFundsObj->req_amount;
                $reqCurrency = $tempFundsObj->req_currency;
                $applicationId = $tempFundsObj->application_id;
                $extraData = $tempFundsObj->extra_data;
                $comment = $tempFundsObj->comment;

                $tempFundsObj->status = '-1';
                $tempFundsObj->rec_amount = '0';

                // add the payment gateway if not added
                if ($extraData && !in_array($paymentGateway->slug, PaymentGateway::$ignored_store_fe_on_execute)) {
                    $extraData['application_id'] = $applicationId;
                    $extraData['payment_gateway_id'] = $paymentGatewayId;

                    if (array_key_exists('account_currency', $extraData)) {
                        $extraData['currency_id'] = $extraData['account_currency'];
                    }

                    if (in_array($paymentGateway->slug, PaymentGateway::$required_bank_gateways)) {
                        $appPaymentGateway = AppPaymentBank::query()->firstOrCreate($extraData);
                    } else {
                        $appPaymentGateway = AppPaymentGateway::query()->firstOrCreate($extraData);
                    }

                    $tempFundsObj->app_payment_id = $appPaymentGateway->id;
                }

                $transactionTypeId = TransactionType::query()->where('key', 'deposit')->value('id');

                $transaction = Transaction::query()->create([
                    'application_id' => $applicationId,
                    'account_manager_id' => $user->application?->getAccountManager()?->id,
                    'user_id' => $user->id,
                    'executed_by' => $user->id,
                    'transaction_type_id' => $transactionTypeId,
                    'source' => $paymentGatewayId,
                    'req_amount' => $reqAmount,
                    'req_currency' => $reqCurrency,
                    'ip_address' => ip_address(),
                    'status' => '-1',
                    'role_id' => $user->role->id,
                    'app_payment_id' => $tempFundsObj->app_payment_id,
                    'extra_transaction_id' => $extraTransactionId,
                    'comment' => $comment,
                ]);

                $tempFundsObj->transaction_id = $transaction->id;
                $tempFundsObj->save();

                // send an emails
                try {
                    $userLocale = $user->language->abbreviation ?? 'en';

                    // send an email to notify the user
                    Mail::to($user->email)->locale($userLocale)->send(new FailedDeposit($user, $userLocale));

                    $accountManager = User::getAccountManager($user->application_id);

                    if ($accountManager) {
                        $mail = Mail::to($accountManager->email)->locale(
                            $accountManager?->language?->abbreviation ?? 'en'
                        );

                        $mail->send(new NotifyAccountManagerOnFailedDeposits(
                            $transaction,
                            $userLocale,
                        ));
                    }
                } catch (Exception $e) {
                }

                DepositRejected::dispatch($transaction);

                $tempFundsObj->delete();
            }
        }
    }

    public static function getTotalBalance($application, $wallet_balance)
    {
        $held_balance = $application->transaction()->whereHas('transactionType', function ($t) {
            $t->where('key', 'held');
        })->sum('req_amount');

        $unheld_balance = $application->transaction()->whereHas('transactionType', function ($t) {
            $t->where('key', 'unheld');
        })->sum('req_amount');

        $totalHeld = $held_balance - $unheld_balance;

        if ($totalHeld < 0) {
            $totalHeld = 0;
        }

        return $wallet_balance - $totalHeld;
    }
}
