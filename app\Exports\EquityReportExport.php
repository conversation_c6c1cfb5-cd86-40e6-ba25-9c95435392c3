<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use App\Http\Controllers\Admin\EquityReportController;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class EquityReportExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private $has_previous_day;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->has_previous_day = isset($requestData['equity_date']) && $requestData['equity_date'] != now()->format('Y-m-d');
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(EquityReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        $headings = [
            'App. ID',
            'Username',
            'Country',
            'Client type',
            'Entity',
            'Total Deposit',
            'Total Withdrawal',
            'Login',
            'Platform',
            'Currency',
            'Group Allocation',
            'Margin',
            'Free margin',
            'Balance',
            'Equity',
            'Credit',
        ];

        if ($this->has_previous_day) {
            $date = $this->requestData['equity_date'];
            array_push(
                $headings,
                "Margin At {$date}",
                "Free margin At {$date}",
                "Balance At {$date}",
                "Equity At {$date}",
                "Credit At {$date}"
            );
        }

        return $headings ;
    }

    public function map($row): array
    {
        $data[] = [
            $row->appIdentifierId() ?? '-',
            $row->mainUser->getUserHolderName(),
            $row->mainUser->country?->name ?? '-',
            $row->applicationClassification?->classificationOption?->display_name ?? '-',
            $row->getRegulatedEntity() ?? '-',
            number_formatter($row->total_deposit) ,
            number_formatter($row->total_withdrawal),
        ];

        foreach ($row->realWithWalletMtAccounts as $mt_account) {
            $metaTraderRelation = $mt_account->metaTraderRealAccount;

            if ($this->has_previous_day) {
                $dailies = $mt_account->mtGroup?->tradingPlatform->name == 'mt4' ? 'mt4Daily' : 'mt5Daily' ;
                if ($dailies) {
                    $marginRequestedDate = $metaTraderRelation?->$dailies?->first()?->margin ?? 0  ;
                    $free_marginRequestedDate = $metaTraderRelation?->$dailies?->first()?->free_margin ?? 0  ;
                    $balanceRequestedDate = $metaTraderRelation?->$dailies?->first()?->balance ?? 0  ;
                    $equityRequestedDate = $metaTraderRelation?->$dailies?->first()?->equity ?? 0  ;
                    $creditRequestedDate = $metaTraderRelation?->$dailies?->first()?->credit ?? 0  ;
                }
            }

            $data[] = [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                $mt_account->login ?? '-',
                $mt_account->mtGroup->tradingPlatform->name ?? '-',
                $mt_account->mtGroup->currency->code ?? '-',
                $mt_account->mtGroup->mtGroupAllocation->name ?? '-',
                $metaTraderRelation->margin ?? '0',
                $metaTraderRelation->free_margin ?? '0',
                $metaTraderRelation->balance ?? '0',
                $metaTraderRelation->equity ?? '0',
                $metaTraderRelation->credit ?? '0',
                $marginRequestedDate ?? '',
                $free_marginRequestedDate ?? '',
                $balanceRequestedDate ?? '',
                $equityRequestedDate ?? '',
                $creditRequestedDate ?? '',
            ];
        }

        return $data;
    }
}
