<?php

namespace App\Exports;

use App\Http\Controllers\Admin\TicketReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class TicketExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function headings(): array
    {
        return [
            'ID Application',
            'Name',
            'Total Tickets',
            'Open Tickets Count',
            'Closed Tickets Count',
            'Last Ticket Date',
        ];
    }

    public function map($user): array
    {
        return [
            $user->application->appIdentifierId(),
            $user->getUserHolderName() ?? '-',
            $user->tickets_count,
            $user->open_tickets_count,
            $user->closed_tickets_count,
            $user->lastTicket->created_at ?? '-',
        ];
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(TicketReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }
}
