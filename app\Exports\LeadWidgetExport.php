<?php

namespace App\Exports;

use App\Http\Controllers\Admin\LeadsWidgetController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class LeadWidgetExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected $data)
    {
    }

    public function map($row): array
    {
        return [
            $row['user_name'],
            $row['email'],
            number_format($row['total_deposits'], 2, '.', '') . ' ' . trans('content.usd') ?? '-',
            $row['first_deposit_date'] ?? '-',
            $row['last_deposit_date'] ?? '-',
            number_format($row['total_withdrawals'], 2, '.', '') . ' ' . trans('content.usd') ?? '-',
            $row['num_deposits'] ?? '0',
            $row['num_withdrawals'] ?? '0',
        ];
    }

    public function headings(): array
    {
        return [
            'User Name',
            'Email',
            'Total Deposits',
            'First Deposit Date',
            'Last Deposit Date',
            'Total Withdrawals',
            'Number Of Deposits',
            'Number Of Withdrawals',
        ];
    }

    public function collection()
    {
        auth()->LoginUsingId($this->userId);

        return collect(app(LeadsWidgetController::class)->index(
            request: request()->merge($this->data),
            export: true
        ));
    }
}
