<?php

namespace App\Exports;

use App\Http\Controllers\Admin\UserTransactionController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class UserTransactionExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData, protected array $actionParameters)
    {
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(UserTransactionController::class)->accountTransactions(
            request: request()->merge($this->requestData),
            application_id: $this->actionParameters['app_id'],
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Reference ID',
            'Transaction Type',
            'Payment Method',
            'Account Number',
            'Amount (In)',
            'Amount (Out)',
            'Currency',
            'Created At',
            'Executed Date',
            'USD Amount',
            'Fee',
            'Balance after txs',
            'Status',
        ];
    }

    public function map($row): array
    {
        return [
            $row['transaction_id'] ?? '-',
            $row['transaction'],
            $row['payment_gateway'],
            $row['account_number'],
            $row['amount_in'] ?? '-',
            $row['amount_out'] ?? '-',
            $row['currency'] ?? '-',
            $row['created_at'] ?? '-',
            $row['executed_at'] ?? '-',
            $row['usd_amount'] ?? '-',
            $row['fee'] ?? '-',
            $row['wallet_balance_after_transaction'] ?? '-',
            $row['status'],
        ];
    }
}
