<?php

namespace App\Exports;

use App\Http\Controllers\Admin\MtGroupSharedController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtGroupSharedExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(MtGroupSharedController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'App. ID',
            'Username',
            'Login',
            'Group ID',
            'Group Name',
            'Group Display Name',
            'Trading Platform',
            'Reg. Accounts',
            'Reg. Av. Groups',
            'Created Date',
            'Updated On',
        ];
    }

    public function map($row): array
    {
        $appId = $username = null;
        if ($row->mtAccount) {
            $username = $row->mtAccount?->application?->mainUser?->getUserHolderName() ?? '-';
            $appId = $row->mtAccount?->application->appIdentifierId();
        } elseif ($row->application) {
            $username = $row->application?->mainUser?->getUserHolderName() ?? '-';
            $appId = $row->application?->appIdentifierId();
        }

        return [
            $appId ?? '-',
            $username ?? '-',
            $row->mtAccount->login ?? '-',
            $row->mt_group_id ?? '-',
            $row->mtGroup->name ?? '-',
            $row->mtGroup->display_name ?? '-',
            $row->mtGroup->tradingPlatform->name ?? '-',
            $row->mt_accounts_count ?? '0',
            $row->app_available_groups_count ?? '0',
            $row->created_at ?? '-',
            $row->updated_at ?? '-',
        ];
    }
}
