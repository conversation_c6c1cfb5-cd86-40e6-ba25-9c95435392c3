<?php

namespace App\Exports;

use App\Http\Controllers\Admin\GenderController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class GenderExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(GenderController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'name',
        ];
    }

    public function map($row): array
    {
        return [
            $row->name ?? '-',
        ];
    }
}
