<?php

namespace App\Enums;

enum CareerStatusesEnum: string
{
    case OPEN = 'open';
    case FILLED = 'filled';
    case DRAFT = 'draft';
    case DELETED = 'deleted';
    case CANCELED = 'canceled';
    case ON_HOLD = 'on hold';

    public function getBadge(): string
    {
        return match ($this) {
            self::FILLED => '<span class="badge badge-info">'.__('content.filled').'</span>',
            self::DELETED => '<span class="badge badge-danger">'.__('content.deleted').'</span>',
            self::CANCELED => '<span class="badge badge-danger">'.__('content.canceled').'</span>',
            self::OPEN => '<span class="badge badge-success">'.__('content.open').'</span>',
            self::DRAFT => '<span class="badge badge-dark">'.__('content.draft').'</span>',
            self::ON_HOLD => '<span class="badge badge-warning">'.__('content.on_hold').'</span>',
        };
    }

    public function isClosed(): bool
    {
        return in_array($this, [self::FILLED, self::DELETED, self::CANCELED]);
    }

    public function isOpen(): bool
    {
        return in_array($this, [self::OPEN]);
    }

    public function isPending(): bool
    {
        return in_array($this, [self::DRAFT, self::ON_HOLD]);
    }
}
