<?php

namespace App\Exports;

use App\Http\Controllers\Admin\ClosedPositionReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ClosePositionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    protected $actions = [];

    protected $reasons = [];

    protected $platform;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $response = $this->getResponse();
        $this->reasons = $response['reasons'];
        $this->actions = $response['actions'];
        $this->platform = $response['platform'];
    }

    public function headings(): array
    {
        return [
            'LOGIN',
            'PLATFORM',
            'ACQUISITION MANAGER',
            'RETENTION MANAGER',
            'TICKET',
            'CMD',
            'CLOSE TIME',
            'CLOS<PERSON> PRICE',
            'CURRENCY',
            'SYMBOL',
            'VOLUME',
            'PROFIT',
            'REASON',
        ];
    }

    public function map($row): array
    {
        $currency = $row->account?->mtAccount?->mtGroup?->currency->code ?? '-';
        $action = $this->actions[$this->platform][$row->action];
        $acquisition_manager = $row->account?->mtAccount?->application?->getAppAcquisitionManagerName() ?? '-';
        $retention_manager = $row->account?->mtAccount?->application?->getAppRetentionManagerName() ?? '-';

        return [
            $row->login,
            $this->platform,
            $acquisition_manager,
            $retention_manager,
            $row->ticket,
            $action,
            $row->close_time,
            $row->close_price,
            $currency,
            $row->symbol,
            $row->volume,
            $row->profit,
            $this->reasons[$this->platform][$row->reason],
        ];
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return $this->getResponse()['data'];
    }

    public function getResponse()
    {
        return app(ClosedPositionReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }
}
