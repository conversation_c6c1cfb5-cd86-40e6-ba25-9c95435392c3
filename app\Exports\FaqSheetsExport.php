<?php

namespace App\Exports;

use App\Models\Language;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class FaqSheetsExport implements WithMultipleSheets
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        $languages = Language::whereJsonLength('websites', '>', 0)->pluck('abbreviation')->toArray();

        foreach ($languages as $language) {
            $sheets[] = new FaqExport($this->userId, $this->requestData, $language);
        }

        return $sheets;
    }
}
