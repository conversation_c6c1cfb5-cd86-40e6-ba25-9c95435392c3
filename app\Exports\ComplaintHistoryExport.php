<?php

namespace App\Exports;

use App\Http\Controllers\Admin\ComplaintController;
use App\Models\Complaint;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ComplaintHistoryExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(ComplaintController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Complaint No.',
            'Username',
            'Country',
            'Category',
            'Complaint description',
            'First Reply By',
            'Last Reply By',
            'Replies',
            'Status',
            'Complaint Rate',
            'Date Issue',
            'First Response Time',
            'Within KPI',
            'Created Date',
        ];
    }

    public function map($data): array
    {
        $statuses = Complaint::$statuses;

        return [
            $data->number,
            $data->user->getUserHolderName(),
            $data->user->getUserCountry(),
            $data->subject_subject,
            rip_tags($data->description),
            $data->firstAgentComplaintConversation ? $data->firstAgentComplaintConversation->user->getUserHolderName() : '-',
            $data->firstAgentComplaintConversation ? $data->latestComplaintConversation->user->getUserHolderName() : '-',
            $data->complaint_conversations_count,
            $statuses[$data->status] ? __('content.' . $statuses[$data->status]) : $data->status,
            isset($data->complaint_rate_rate) && isset($rates[$data->complaint_rate_rate]) ? $data->complaintRate::$rates[$data->complaint_rate_rate] : __('content.not_rated'),
            $data->date_issue,
            $data->getFirstResponseTime(),
            is_null($data->satisfyingKpiFRT()) ? '-' : ($data->satisfyingKpiFRT() ? __('content.yes') : __('content.no')),
            $data->created_at,
        ];
    }
}
