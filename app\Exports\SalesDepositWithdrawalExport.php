<?php

namespace App\Exports;

use App\Http\Controllers\Admin\SalesDepositWithdrawalController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class SalesDepositWithdrawalExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(SalesDepositWithdrawalController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Total Deposits',
            'Total Withdrawals',
            'Number Of Deposits',
            'Number Of Withdrawals',
        ];
    }

    public function map($row): array
    {
        return [
            $row->mainUser?->full_name,
            number_formatter($row->total_withdrawals),
            number_formatter($row->total_deposits),
            $row->number_of_withdrawals ?? 0,
            $row->number_of_deposits ?? 0,
        ];
    }
}
