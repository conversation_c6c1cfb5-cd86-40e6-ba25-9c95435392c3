@extends('templates.crm.layout')
@section('content')
    <div class="card">
        <a data-action="collapse">
            <div class="card-header">
                <h4 class="card-title">{{ __('content.search_filter') }}</h4>
                <div class="heading-elements">
                    <ul class="list-inline mb-0">
                        <li><i class="la la-plus"></i></li>
                    </ul>
                </div>
            </div>
        </a>
        <div class="card-content card-filters collapse {{ collapsed_filter() }}">
            <div class="card-body">
                <form class="form-horizontal" action="{{ route('existing-users.index') }}" method="GET"
                    class="form-horizontal">
                    <div class="form-group row mb-0">
                        <div class="col-xl-3 col-lg-4 col-md-4 mb-1">
                            <input type="text" value="{{ $request->get('email') ?? '' }}" name="email"
                                placeholder="{{ __('content.email') }}" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row mb-0">
                        <div class="col-xl-12 col-lg-12 col-md-12">
                            <button type="submit" class="btn btn-md btn-outline-dark btn-loading">
                                <i class="la la-search"></i> {{ __('content.search') }}
                            </button>
                            <a class="btn btn-outline-danger btn-loading"
                                href="{{ route('internal-notifications.index') }}">
                                <i class="la la-reply"></i> {{ __('content.reset') }}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">{{ __('content.existing_users') }}</h4>
        </div>

        <div class="card-content">
            <div class="card-body">
                @forelse ($users as $user)
                    <p>{{ $user->name }}</p>
                @empty
                    <p>{{ __('content.no_results_found') }}</p>
                @endforelse
            </div>
        </div>
    </div>
@endsection
