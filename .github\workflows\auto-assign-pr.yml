name: Assign PR assignee Workflow

on:
  pull_request:
    types:
      - opened
      - reopened
    branches:
      - main
      - development
      - staging

jobs:
  call-assign-reviewers:
    uses: IngotTech/reusable-workflows/.github/workflows/assign_reviewers_workflow.yml@develop
    with:
      target-branch: ${{ github.base_ref }}
      prod-branch: 'main'
      prod-assignees: 'eaboushi'
      staging-assignees: 'eaboushi'
    secrets:
      WORKFLOW_TOKEN: ${{ secrets.GITHUB_TOKEN }}
