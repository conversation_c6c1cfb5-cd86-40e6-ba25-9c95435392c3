<?php

namespace App\Exports;

use App\Http\Controllers\Admin\SeminarController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class SeminarUserExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(SeminarController::class)->seminarUsers(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Email',
            'Mobile',
            'Country',
            'Attend',
            'Source',
            'Date',
        ];
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->first_name . ' ' . $row->last_name ?? null,
            $row->email,
            $row->mobile,
            $row->country?->name,
            $row->attend ? trans('content.yes') : trans('content.no'),
            $row->source ?? '-',
            $row->created_at,
        ];
    }
}
