<?php

namespace App\Exports;

use App\Enums\MetaTrader4Enum;
use App\Enums\MetaTrader5Enum;
use App\Http\Controllers\Admin\LiquidationReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class LiquidationExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(LiquidationReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'LOGIN',
            'PLATFORM',
            'CURRENCY',
            'TICKET',
            'SYMBOL',
            'CMD',
            'VOLUME',
            'OPEN_PRICE',
            'CLOSE_TIME',
            'CLOSE_PRICE',
            'PROFIT',
        ];
    }

    public function map($row): array
    {
        $actions = [
            'mt4' => MetaTrader4Enum::DEAL_ACTION,
            'mt5' => MetaTrader5Enum::DEAL_ACTION,
        ];

        $platform = $row->account?->mtAccount?->mtGroup?->tradingPlatform?->name;

        $action = $actions[$platform][$row->action] ?? '-';
        $volume = $row->volume ?? 0;

        return [
            $row->login,
            strtoupper($platform) ?? '-',
            $row->account?->mtAccount?->mtGroup?->currency?->code ?? '-',
            $row->ticket,
            $row->symbol,
            $actions[$platform][$row->action] ?? '-',
            $action == 'buy' ? $volume : $volume * -1,
            $row->open_price,
            $row->close_time,
            $row->close_price,
            $row->profit,
        ];
    }
}
