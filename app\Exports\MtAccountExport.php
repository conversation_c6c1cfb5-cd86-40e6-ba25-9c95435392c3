<?php

namespace App\Exports;

use App\Http\Controllers\Admin\MtAccountManagementController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtAccountExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private $mt_data = [];

    private $mt_balance_det = false;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        $titles = [
            'Account ID',
            'Application ID',
            'Application Type',
            'User name',
            'Email',
            'Portal Country',
            'Portal Citizen',
            'MT Login',
            'Platform',
            'Group',
            'Currency',
        ];

        if (isset($this->requestData['export_mt_data'])) {
            $titles = array_merge($titles, [
                'MT Country',
                'Leverage',
                'Balance',
                'Equity',
                'Margin',
                'Last MT Access',
                'Last Trade Date',
            ]);
        }

        $titles = array_merge($titles, [
            'Account Type',
            'Referral Source',
            'Acquisition Manager',
            'Retention Manager',
            'Sales Manager',
            'Referral Name',
            'CX Agent ID',
            'Regulation Entity',
            'MT Status',
            'App Status',
            'E-Wallet Balance',
            'ID Wise Status',
            'Created Date',
        ]);

        return $titles;
    }

    public function map($row): array
    {
        $mainUser = $row->application->mainUser;

        $rows = [
            $row->id ?? '-',
            $row->application->appIdentifierId() ?? '-',
            $row->application->appType->display_name ?? '-',
            $mainUser->full_name ?? '-',
            $mainUser->email ?? '-',
            $mainUser->getUserCountry() ?? '-',
            $mainUser->getUserHolderCitizen() ?? '-',
            $row->login ?? '-',
            $row->mtGroup->tradingPlatform->name ?? '-',
            $row->mtGroup->name ?? '-',
            $row->mtGroup->currency->code ?? '-',
        ];

        if (isset($this->requestData['export_mt_data'])) {
            $metaTraderRelation = $row->isReal() ? $row->metaTraderRealAccount : $row->metaTraderDemoAccount;

            $rows = array_merge($rows, [
                $metaTraderRelation->mtUser?->country ?? '-',
                $metaTraderRelation?->leverage ? '1:' .$metaTraderRelation?->leverage : '-',
                $metaTraderRelation?->balance ?? '-',
                $metaTraderRelation?->equity ?? '-',
                $metaTraderRelation?->margin ?? '-',
                $metaTraderRelation?->last_mt_access ?? '-',
                $metaTraderRelation?->last_traded_date_calculated ?? '-',
            ]);
        }

        $rows = array_merge($rows, [
            $row->mtGroup->offerAccountType->account_name ?? '-',
            $row->application->referral->name ?? '-',
            $row->application->getAppAcquisitionManagerName() ?? '-',
            $row->application->getAppRetentionManagerName() ?? '-',
            $row->application->getAppSalesManagerName() ?? '-',
            $row->application->getAppParentName() ?? '-',
            $row->application->appRelation->parentApplication->cellxpertApplication->affiliate_id ?? '-',
            $row->application->getRegulatedEntity() ?? '-',
            $row->hidden ? 'Disabled' : 'Active',
            $row->application->disabledApplication ? 'Disabled' : 'Enabled',
            $row->application->mainAppWallet?->balance ?? '-',
            $mainUser->userExtra?->documents_verification ?? '-',
            $row->created_at ?? '-',
        ]);

        return $rows;
    }
}
