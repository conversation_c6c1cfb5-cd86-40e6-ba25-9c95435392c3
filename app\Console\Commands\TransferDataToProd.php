<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class TransferDataToProd extends Command
{
    protected $signature = 'transfer:data-to-prod {start_date} {end_date}';

    protected $description = 'Transfer data from local to production based on created_at date range';

    // php artisan transfer:data-to-prod "2024-01-01 00:00:00" "2024-08-23 23:59:59"
    // php artisan transfer:data-to-prod "2023-01-01 00:00:00" "2023-12-31 23:59:59"
    // php artisan transfer:data-to-prod "2022-01-01 00:00:00" "2022-12-31 23:59:59"
    // php artisan transfer:data-to-prod "2021-01-01 00:00:00" "2021-12-31 23:59:59"
    // php artisan transfer:data-to-prod "2020-01-01 00:00:00" "2020-12-31 23:59:59"

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $startDate = $this->argument('start_date');
        $endDate = $this->argument('end_date');

        $chunkSize = 10000;
        $offset = 0;

        $totalRows = DB::table('daily_app_wallets')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $this->info("Starting data transfer... Total rows to transfer: {$totalRows}");

        $columns = DB::getSchemaBuilder()->getColumnListing('daily_app_wallets');
        $columns = array_diff($columns, ['id']);

        do {
            try {
                $rows = DB::table('daily_app_wallets')
                    ->select($columns)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->offset($offset)
                    ->limit($chunkSize)
                    ->get()
                    ->map(function ($row) {
                        $row = (array) $row;

                        if (isset($row['created_at'])) {
                            $date = Carbon::createFromFormat('Y-m-d H:i:s', $row['created_at']);

                            if (!$date) {
                                throw new Exception("Invalid datetime format: {$row['created_at']}");
                            }

                            if (strpos($row['created_at'], '00:59:59') !== false) {
                                // Subtract 1 hour from created_at
                                $row['created_at'] = $date->subHour()->format('Y-m-d H:i:s');
                            } else {
                                // Ensure created_at is correctly formatted
                                $row['created_at'] = $date->format('Y-m-d H:i:s');
                            }
                        }

                        return $row;
                    })
                    ->toArray();

                if (empty($rows)) {
                    break;
                }

                DB::connection('production')->beginTransaction();

                DB::connection('production')->table('daily_app_wallets')->insert($rows);

                DB::connection('production')->commit();

                $offset += $chunkSize;
                $this->info("Transferred {$offset}/{$totalRows} rows. " . round(($offset / $totalRows) * 100, 2) . '% completed.');

                $rows = null;
                gc_collect_cycles();
            } catch (Exception $e) {
                DB::connection('production')->rollBack();

                Log::error('Error transferring data: ' . $e->getMessage());

                $this->error('Error transferring data. Check the logs for more details.');

                return;
            }
        } while (true);

        $this->info('Data transfer complete!');
    }
}
