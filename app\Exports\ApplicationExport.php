<?php

namespace App\Exports;

use App\Models\AppType;
use App\Models\MtGroup;
use App\Models\SourceOfFundQuestion;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use App\Http\Controllers\Admin\ApplicationManagementController;

class ApplicationExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private $annual_income_question_ids;

    private $experience_question_ids;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        $this->annual_income_question_ids = SourceOfFundQuestion::query()->where('name', 'like', '%Annual Income%')->pluck('id')->toArray();
        $this->experience_question_ids = SourceOfFundQuestion::query()->where('name', 'like', '%Do you have experience trading%')->pluck('id')->toArray();

        return app(ApplicationManagementController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        $headings = [
            'App. ID',
            'CX ID',
            'Entity',
            'Name',
            'Country',
            'Citizenship',
            'Gender',
            'Professionalism',
            'Annual Income',
            'Trading Experience',
            'Email',
            'Phone',
            'First Time Deposit',
            'Age',
            'App Type',
            'Acquisition App. ID',
            'Acquisition Manager',
            'Retention App. ID',
            'Retention Manager',
            'Parent App. ID',
            'Parent Name',
            'MT Accounts',
            'Lead Source',
            'Source Name',
            'Lead Status',
            'App Status',
            'Reg. IP Address',
            'Last Login Date',
            'Reg. Date',
            'Archived Reason',
            'Disabled Reason',
            'Suspend Reason',
            'Archived Date',
            'Disabled Date',
            'Suspend Date',
        ];

        if (isset($this->requestData['include_name_parts'])) {
            $headings[] = 'First Name (AR)';
            $headings[] = 'Second Name (AR)';
            $headings[] = 'Third Name (AR)';
            $headings[] = 'Last Name (AR)';
            $headings[] = 'First Name (EN)';
            $headings[] = 'Second Name (EN)';
            $headings[] = 'Third Name (EN)';
            $headings[] = 'Last Name (EN)';
        }

        if (isset($this->requestData['include_utm_data'])) {
            $headings[] = 'UTM Term';
            $headings[] = 'UTM Source';
            $headings[] = 'UTM Medium';
            $headings[] = 'UTM Campaign';
            $headings[] = 'UTM Content';
        }

        if (isset($this->requestData['include_comments'])) {
            $headings[] = 'Comments';
        }

        return $headings;
    }

    public function map($row): array
    {
        $mainUser = $row->mainUser;

        $source_name = null;
        $lead_source = $row->referral?->name;
        if ($lead_source && in_array($lead_source, ['Other', 'Campaign'])) {
            $source_name = $row->referral_value_id;
        }

        $mt_accounts = $row->mtAccount()->whereHas('mtGroup', function ($g) {
            $g->whereIn('account_type', MtGroup::LIVE_ACCOUNT_TYPES);
        })->pluck('login')->toArray() ?? [];

        if ($row->appType->name == AppType::EMPLOYEE) {
            $app_type = $row->mainUser->role->display_name;
        } else {
            $app_type = $row->appType->display_name;
        }

        $opportunity_status = $row->appOpportunityOption?->opportunity_option_display_name ?? 'not contacted';

        $status[] = $row->getStatus() ?? null;
        if ($row->disabledApplication) {
            $status[] = 'disabled';
        }
        if ($row->suspendedApplication) {
            $status[] = 'suspended';
        }
        if ($row->archivedApplication) {
            $status[] = 'archived';
        }
        $app_status = implode(', ', $status);

        $appInfo = [
            $row->appIdentifierId(),
            $row->cellxpertApplication?->affiliate_id ?? '-',
            $row->affiliateWebsite->regulationEntity->name ?? 'All Entities',
            $mainUser?->getUserHolderName() ?? '-',
            $mainUser?->getUserCountry() ?? '-',
            $mainUser?->getUserHolderCitizen() ?? '-',
            $mainUser?->gender->name ?? '-',
            $mainUser?->userExtra->professionalism->name ?? '-',
            $mainUser?->getUserAnnualIncome($this->annual_income_question_ids) ?? '-',
            $mainUser?->getUserTradingExperience($this->experience_question_ids) ?? '-',
            $mainUser?->email ?? '-',
            $mainUser?->phone ?? '-',
            $row->firstDeposit->created_at ?? '-',
            date_diff(date_create($mainUser?->birthdate), date_create('now'))->y,
            $app_type ?? '-',
            $row->getAcquisitionManagerAppId() ?? '-',
            $row->getAppAcquisitionManagerName() ?? 'not assigned',
            $row->getRetentionManagerAppId() ?? '-',
            $row->getAppRetentionManagerName() ?? 'not assigned',
            $row->getAppParentId() ?? '-',
            $row->getAppParentName() ?? 'not assigned',
            $mt_accounts ? implode(', ', (array) $mt_accounts) : '-',
            $lead_source ?? '-',
            $source_name ?? '-',
            $opportunity_status ?? '-',
            $app_status ?? '-',
            $row->oldestLogin?->ip_address ?? 'not detected',
            $row->latestLogin?->created_at ?? 'never logged in',
            $row->created_at,
            $row->archivedApplication->comment ?? $row->archivedApplication?->loadMissing('statusApplication')->comment ?? '-',
            $row->disabledApplication->comment ?? $row->disabledApplication?->loadMissing('statusApplication')->comment ?? '-',
            $row->suspendedApplication->comment ?? $row->suspendedApplication?->loadMissing('statusApplication')->comment ?? '-',
            $row->archivedApplication->created_at ?? '-',
            $row->disabledApplication->created_at ?? '-',
            $row->suspendedApplication->created_at ?? '-',
        ];

        if (isset($this->requestData['include_name_parts'])) {
            array_push(
                $appInfo,
                $mainUser?->userExtra?->first_name_ar ?? '-',
                $mainUser?->userExtra?->second_name_ar ?? '-',
                $mainUser?->userExtra?->third_name_ar ?? '-',
                $mainUser?->userExtra?->last_name_ar ?? '-',
                $mainUser?->userExtra?->first_name_en ?? '-',
                $mainUser?->userExtra?->second_name_en ?? '-',
                $mainUser?->userExtra?->third_name_en ?? '-',
                $mainUser?->userExtra?->last_name_en ?? '-',
            );
        }

        if (isset($this->requestData['include_utm_data'])) {
            $utm_data = $row->applicationUtmData;

            $utm_term = $utm_data->term ?? '-';
            $utm_source = $utm_data->source ?? '-';
            $utm_medium = $utm_data->medium ?? '-';
            $utm_campaign = $utm_data->campaign ?? '-';
            $utm_content = $utm_data->content ?? '-';

            array_push($appInfo, $utm_term, $utm_source, $utm_medium, $utm_campaign, $utm_content);
        }

        $rowData[] = $appInfo;

        if (isset($this->requestData['include_comments'])) {
            $comments = $row->comments->pluck('comment')->toArray();
            foreach ($comments as $comment) {
                $commentData = [
                    null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
                    null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
                    null, null, null, null,
                ];

                if (isset($this->requestData['include_name_parts'])) {
                    array_push($commentData, null, null, null, null, null, null, null, null);
                }

                if (isset($this->requestData['include_utm_data'])) {
                    array_push($commentData, null, null, null, null, null);
                }

                $commentData[] = $comment;

                $rowData[] = $commentData;
            }
        }

        return $rowData;
    }
}
