<?php

namespace App\Exports;

use App\Http\Controllers\Admin\UserLoginLogController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class UserLoginLogExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(UserLoginLogController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID Application',
            'Name',
            'Browser',
            'Platform',
            'IP Address',
            'Date',
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId(),
            $row->user->full_name ?? '',
            $row->browser,
            $row->platform,
            $row->ip_address,
            $row->created_at,
        ];
    }
}
