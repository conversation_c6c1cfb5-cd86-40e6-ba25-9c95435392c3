{"__meta": {"id": "01JW8JDPSF1CEXC6VJCE8QSQ05", "datetime": "2025-05-27 12:50:48", "utime": **********.623804, "method": "GET", "uri": "/en/existing-users?email=test_it-department%40ingotbrokers.com", "ip": "::1"}, "php": {"version": "8.2.0", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748339447.983306, "end": **********.623814, "duration": 0.6405081748962402, "duration_str": "641ms", "measures": [{"label": "Booting", "start": 1748339447.983306, "relative_start": 0, "end": **********.273935, "relative_end": **********.273935, "duration": 0.*****************, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.273945, "relative_start": 0.*****************, "end": **********.623816, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.275389, "relative_start": 0.****************, "end": **********.295856, "relative_end": **********.295856, "duration": 0.020467042922973633, "duration_str": "20.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.510664, "relative_start": 0.****************, "end": **********.623587, "relative_end": **********.623587, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "1x templates.crm.admin.existing_users.index", "param_count": null, "params": [], "start": **********.511212, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/existing_users/index.blade.phptemplates.crm.admin.existing_users.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fadmin%2Fexisting_users%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.admin.existing_users.index"}, {"name": "1x templates.crm.layout", "param_count": null, "params": [], "start": **********.515653, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/layout.blade.phptemplates.crm.layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.layout"}, {"name": "1x templates.pub.meta", "param_count": null, "params": [], "start": **********.519732, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/pub/meta.blade.phptemplates.pub.meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fpub%2Fmeta.blade.php&line=1", "ajax": false, "filename": "meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.pub.meta"}, {"name": "1x templates.crm.partials.admin-header", "param_count": null, "params": [], "start": **********.558875, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/admin-header.blade.phptemplates.crm.partials.admin-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Fadmin-header.blade.php&line=1", "ajax": false, "filename": "admin-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.admin-header"}, {"name": "1x templates.crm.partials.left-side-menu", "param_count": null, "params": [], "start": **********.582207, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/left-side-menu.blade.phptemplates.crm.partials.left-side-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Fleft-side-menu.blade.php&line=1", "ajax": false, "filename": "left-side-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.left-side-menu"}, {"name": "1x templates.crm.partials.trading-hub-admin-sidebar", "param_count": null, "params": [], "start": **********.583246, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/trading-hub-admin-sidebar.blade.phptemplates.crm.partials.trading-hub-admin-sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Ftrading-hub-admin-sidebar.blade.php&line=1", "ajax": false, "filename": "trading-hub-admin-sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.trading-hub-admin-sidebar"}, {"name": "1x templates.crm.partials.alerts", "param_count": null, "params": [], "start": **********.601976, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/alerts.blade.phptemplates.crm.partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.alerts"}, {"name": "1x templates.crm.partials.modals", "param_count": null, "params": [], "start": **********.620148, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/modals.blade.phptemplates.crm.partials.modals", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Fmodals.blade.php&line=1", "ajax": false, "filename": "modals.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.modals"}, {"name": "1x components.button", "param_count": null, "params": [], "start": **********.622127, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.button"}]}, "route": {"uri": "GET en/existing-users", "middleware": "web, auth, password.expiry, localeSessionRedirect, localizationRedirect, localeViewPath, can:view-existing_users, Ingotbrokers", "controller": "App\\Http\\Controllers\\Admin\\ExistingUserController@index<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FExistingUserController.php&line=16\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/en/existing-users", "as": "existing-users.index", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FExistingUserController.php&line=16\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/ExistingUserController.php:16-34</a>"}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.21916, "accumulated_duration_str": "219ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `users` where `email` like '%<EMAIL>%' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["%<EMAIL>%"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ExistingUserController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\ExistingUserController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3014872, "duration": 0.*****************, "duration_str": "208ms", "memory": 0, "memory_str": null, "filename": "ExistingUserController.php:28", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ExistingUserController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\ExistingUserController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FExistingUserController.php&line=28", "ajax": false, "filename": "ExistingUserController.php", "line": "28"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `user_preferences` where `user_id` = 1 and `user_preferences`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/UserPreferenceRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\UserPreferenceRepository.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 742}, {"index": 19, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 124}, {"index": 20, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 28}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}], "start": **********.5133991, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "UserPreferenceRepository.php:27", "source": {"index": 16, "namespace": null, "name": "app/Repositories/UserPreferenceRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\UserPreferenceRepository.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FUserPreferenceRepository.php&line=27", "ajax": false, "filename": "UserPreferenceRepository.php", "line": "27"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where `languages`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 72}, {"index": 17, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 19, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 39}], "start": **********.516938, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:138", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FBaseRepository.php&line=138", "ajax": false, "filename": "BaseRepository.php", "line": "138"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where `languages`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 72}, {"index": 17, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 19, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 39}], "start": **********.520546, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:138", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FBaseRepository.php&line=138", "ajax": false, "filename": "BaseRepository.php", "line": "138"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `applications` where `applications`.`id` = 1 and `applications`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "D:\\projects\\ingotbrokers\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, {"index": 21, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.560418, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BuildsAdjacencyListQueries.php:26", "source": {"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "D:\\projects\\ingotbrokers\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fvendor%2Fstaudenmeir%2Flaravel-adjacency-list%2Fsrc%2FEloquent%2FTraits%2FBuildsAdjacencyListQueries.php&line=26", "ajax": false, "filename": "BuildsAdjacencyListQueries.php", "line": "26"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `affiliate_websites` where `affiliate_websites`.`id` = 3 and `affiliate_websites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.563349, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:35", "source": {"index": 21, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=35", "ajax": false, "filename": "IngotTrait.php", "line": "35"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `id` from `affiliate_websites` where (`name` = 'portal.ingotbrokers.local' or `site_name` = 'portal.ingotbrokers.local' or `crm_name` = 'portal.ingotbrokers.local' or json_contains(`aliases`, '\\\"portal.ingotbrokers.local\\\"')) and `affiliate_websites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["portal.ingotbrokers.local", "portal.ingotbrokers.local", "portal.ingotbrokers.local", "\"portal.ingotbrokers.local\""], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 78}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 72}, {"index": 22, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 41}], "start": **********.565989, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "AffiliateWebsite.php:78", "source": {"index": 17, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=78", "ajax": false, "filename": "AffiliateWebsite.php", "line": "78"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where json_contains(`websites`, '\\\"3\\\"') and (`abbreviation` in ('en', 'ar', 'es', 'fa', 'vi', 'pt')) and `languages`.`deleted_at` is null limit 9223372036854775807", "type": "query", "params": [], "bindings": ["\"3\"", "en", "ar", "es", "fa", "vi", "pt"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, {"index": 16, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.567926, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:117", "source": {"index": 15, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=117", "ajax": false, "filename": "IngotTrait.php", "line": "117"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `translations` where (`model_name` = 'Language' and `locale` = 'en') and `translations`.`object_id` in (1, 2, 7, 11, 15) and `translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["Language", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, {"index": 21, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.570215, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:117", "source": {"index": 20, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=117", "ajax": false, "filename": "IngotTrait.php", "line": "117"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `id` from `order_types` where `key` = 'vacation_request' and `order_types`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["vacation_request"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 886}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 275}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.5746992, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "UserPolicy.php:886", "source": {"index": 17, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 886}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FPolicies%2FUserPolicy.php&line=886", "ajax": false, "filename": "UserPolicy.php", "line": "886"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select exists(select * from `orders` where `order_type_id` = 22 and `user_id` = 1 and `orders`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [22, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 888}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 275}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.577103, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "UserPolicy.php:888", "source": {"index": 11, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 888}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FPolicies%2FUserPolicy.php&line=888", "ajax": false, "filename": "UserPolicy.php", "line": "888"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `app_types` where `app_types`.`id` = 1 and `app_types`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 550}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.59631, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "UserPolicy.php:550", "source": {"index": 21, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 550}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FPolicies%2FUserPolicy.php&line=550", "ajax": false, "filename": "UserPolicy.php", "line": "550"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `temporary_assigned_leads` where `from_id` = 1 and (`canceled_at` is null and (`to_date` <= '2025-05-27' and `from_date` >= '2025-05-27') or `from_date` <= '2025-05-27 12:50:48' and `to_date` >= '2025-05-27 12:50:48') and `temporary_assigned_leads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, "2025-05-27", "2025-05-27", "2025-05-27 12:50:48", "2025-05-27 12:50:48"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 89}, {"index": 17, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.599908, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MembersServiceProvider.php:89", "source": {"index": 16, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FMembersServiceProvider.php&line=89", "ajax": false, "filename": "MembersServiceProvider.php", "line": "89"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `user_2fa_devices` where `user_2fa_devices`.`user_id` = 1 and `user_2fa_devices`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "templates.crm.partials.alerts", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/alerts.blade.php", "line": 11}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.603296, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "templates.crm.partials.alerts:11", "source": {"index": 20, "namespace": "view", "name": "templates.crm.partials.alerts", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/alerts.blade.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Falerts.blade.php&line=11", "ajax": false, "filename": "alerts.blade.php", "line": "11"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `affiliate_websites` where (`name` = 'portal.ingotbrokers.local' or `site_name` = 'portal.ingotbrokers.local' or `crm_name` = 'portal.ingotbrokers.local' or json_contains(`aliases`, '\\\"portal.ingotbrokers.local\\\"')) and `affiliate_websites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["portal.ingotbrokers.local", "portal.ingotbrokers.local", "portal.ingotbrokers.local", "\"portal.ingotbrokers.local\""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 172}], "start": **********.605756, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "AffiliateWebsite.php:60", "source": {"index": 16, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=60", "ajax": false, "filename": "AffiliateWebsite.php", "line": "60"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `account_forms` left join ( SELECT  translations.object_id ,\nJSON_OBJECTAGG(translations.field_name, translations.value) as fields\nFROM translations\nWHERE translations.model_name = 'AccountForm'\nAND translations.locale = 'en'\nAND translations.value IS NOT NULL\nGROUP BY translations.object_id\n) as account_forms_trans on `account_forms_trans`.`object_id` = `account_forms`.`id` where `key` in ('pds', 'tmd', 'fsg', 'terms-and-conditions', 'privacy-policy', 'client-agreement', 'ib-agreement', 'ib-rules-and-conditions', 'client-service-agreement', 'complaint-handling-policy', 'best-execution-policy', 'referral-agreement', 'leverage-change-terms', 'cost-per-acquisition-agreement', 'addendum-to-the-ib-agreement', 'acknowledgment', 'jsc-legal-corporate-shared-docs') and (json_contains(`websites`, '[\\\"all\\\"]') or json_contains(`websites`, '\\\"3\\\"')) and (json_contains(`countries`, '[\\\"all\\\"]') or json_contains(`countries`, '\\\"109\\\"')) and `account_forms`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pds", "tmd", "fsg", "terms-and-conditions", "privacy-policy", "client-agreement", "ib-agreement", "ib-rules-and-conditions", "client-service-agreement", "complaint-handling-policy", "best-execution-policy", "referral-agreement", "leverage-change-terms", "cost-per-acquisition-agreement", "addendum-to-the-ib-agreement", "acknowledgment", "jsc-legal-corporate-shared-docs", "[\"all\"]", "\"3\"", "[\"all\"]", "\"109\""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 203}, {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 103}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.608353, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:203", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 203}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FAppServiceProvider.php&line=203", "ajax": false, "filename": "AppServiceProvider.php", "line": "203"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `app_wallets`.* from `app_wallets` inner join (select MIN(`app_wallets`.`id`) as `id_aggregate`, `app_wallets`.`application_id` from `app_wallets` where `app_wallets`.`application_id` = 1 and `app_wallets`.`application_id` is not null and `app_wallets`.`deleted_at` is null group by `app_wallets`.`application_id`) as `oldestOfMany` on `oldestOfMany`.`id_aggregate` = `app_wallets`.`id` and `oldestOfMany`.`application_id` = `app_wallets`.`application_id` where `app_wallets`.`application_id` = 1 and `app_wallets`.`application_id` is not null and `app_wallets`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 64}, {"index": 22, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.617771, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "MembersServiceProvider.php:64", "source": {"index": 21, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FMembersServiceProvider.php&line=64", "ajax": false, "filename": "MembersServiceProvider.php", "line": "64"}, "connection": "ingot-multiserver", "explain": null}]}, "models": {"data": {"App\\Models\\Language": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\AccountForm": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAccountForm.php&line=1", "ajax": false, "filename": "AccountForm.php", "line": "?"}}, "App\\Models\\AffiliateWebsite": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=1", "ajax": false, "filename": "AffiliateWebsite.php", "line": "?"}}, "App\\Models\\UserPreference": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FUserPreference.php&line=1", "ajax": false, "filename": "UserPreference.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}, "App\\Models\\OrderType": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FOrderType.php&line=1", "ajax": false, "filename": "OrderType.php", "line": "?"}}, "App\\Models\\AppType": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAppType.php&line=1", "ajax": false, "filename": "AppType.php", "line": "?"}}, "App\\Models\\AppWallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAppWallet.php&line=1", "ajax": false, "filename": "AppWallet.php", "line": "?"}}}, "count": 58, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:27 [\n    \"id\" => 1\n    \"first_name\" => \"System\"\n    \"last_name\" => \"Admin\"\n    \"email\" => \"<EMAIL>\"\n    \"gender_id\" => 1\n    \"application_id\" => 1\n    \"country_id\" => 109\n    \"citizenship_id\" => 109\n    \"zip_code\" => null\n    \"birthdate\" => \"1991-05-12\"\n    \"phone\" => \"962-788287729\"\n    \"city\" => \"Amman\"\n    \"address\" => \"Jordan\"\n    \"profile_pic\" => null\n    \"role_id\" => 1\n    \"verified\" => \"1\"\n    \"created_at\" => \"2019-02-12T12:54:33.000000Z\"\n    \"updated_at\" => \"2025-03-13T12:30:37.000000Z\"\n    \"deleted_at\" => null\n    \"pref_lang\" => 2\n    \"timezone_id\" => null\n    \"not_deleted\" => 1\n    \"google2fa_secret\" => null\n    \"timezone\" => null\n    \"role\" => array:18 [\n      \"id\" => 1\n      \"name\" => \"admin\"\n      \"display_name\" => \"System Admin\"\n      \"ip_addresses\" => []\n      \"is_department\" => 0\n      \"department_email\" => \"<EMAIL>\"\n      \"is_manager\" => 0\n      \"permissions\" => \"[\"create-affiliate_website\", \"edit-affiliate_website\", \"delete-affiliate_website\", \"view-affiliate_website\", \"create-application\", \"edit-application\", \"delete-application\", \"view-application\", \"create-manage_application\", \"edit-manage_application\", \"delete-manage_application\", \"view-manage_application\", \"export-manage_application\", \"export-pdf-manage_application\", \"enable-disable-manage_application\", \"store-disable-manage_application\", \"duplicate-manage_application\", \"save-contact-reminder-manage_application\", \"archive-manage_application\", \"store-archive-manage_application\", \"suspend-manage_application\", \"activity-manage_application\", \"security-preferences-app-manage_application\", \"create-app_payment_bank\", \"edit-app_payment_bank\", \"delete-app_payment_bank\", \"view-app_payment_bank\", \"export-app_payment_bank\", \"create-app_payment_gateway\", \"edit-app_payment_gateway\", \"delete-app_payment_gateway\", \"view-app_payment_gateway\", \"export-app_payment_gateway\", \"create-app_type\", \"edit-app_type\", \"delete-app_type\", \"view-app_type\", \"export-app_type\", \"create-app_wallet\", \"edit-app_wallet\", \"delete-app_wallet\", \"view-app_wallet\", \"create-asic_answer\", \"edit-asic_answer\", \"delete-asic_answer\", \"view-asic_answer\", \"export-asic_answer\", \"create-asic_question\", \"edit-asic_question\", \"delete-asic_question\", \"view-asic_question\", \"export-asic_question\", \"create-company\", \"edit-company\", \"delete-company\", \"view-company\", \"create-company_holder\", \"edit-company_holder\", \"delete-company_holder\", \"view-company_holder\", \"create-contact_request\", \"edit-contact_request\", \"delete-contact_request\", \"view-contact_request\", \"view-assign-contact_request\", \"create-country\", \"edit-country\", \"delete-country\", \"view-country\", \"export-country\", \"create-currency\", \"edit-currency\", \"delete-currency\", \"view-currency\", \"export-currency\", \"create-feedback\", \"edit-feedback\", \"delete-feedback\", \"view-feedback\", \"create-mt_group\", \"edit-mt_group\", \"delete-mt_group\", \"view-mt_group\", \"export-mt_group\", \"create-ib_offer\", \"edit-ib_offer\", \"delete-ib_offer\", \"view-ib_offer\", \"create-index_dividend\", \"edit-index_dividend\", \"delete-index_dividend\", \"view-index_dividend\", \"create-contract\", \"edit-contract\", \"delete-contract\", \"view-contract\", \"create-mt_account\", \"edit-mt_account\", \"delete-mt_account\", \"view-mt_account\", \"hide-mt_account\", \"disable-mt_account\", \"create-manage_mt_account\", \"edit-manage_mt_account\", \"delete-manage_mt_account\", \"view-manage_mt_account\", \"export-manage_mt_account\", \"internal-transfer-manage_mt_account\", \"internal-transfer-request-manage_mt_account\", \"create-credit-request-manage_mt_account\", \"store-credit-request-manage_mt_account\", \"create-wl-real-account-manage_mt_account\", \"create-wl-demo-account-manage_mt_account\", \"export-deals-manage_mt_account\", \"export-positions-manage_mt_account\", \"export-orders-manage_mt_account\", \"export-order-history-manage_mt_account\", \"export-commissions-manage_mt_account\", \"internal-transfer-email-request-manage_mt_account\", \"export-transactions-manage_mt_account\", \"not-synced-filter-manage_mt_account\", \"request-swap-free-mt_account\", \"request-change-leverage-mt_account\", \"create-order\", \"edit-order\", \"delete-order\", \"view-order\", \"export-order\", \"create-manage_order\", \"edit-manage_order\", \"delete-manage_order\", \"view-manage_order\", \"export-manage_order\", \"create-page\", \"edit-page\", \"delete-page\", \"view-page\", \"create-payment_gateway\", \"edit-payment_gateway\", \"delete-payment_gateway\", \"view-payment_gateway\", \"export-payment_gateway\", \"create-product_category\", \"edit-product_category\", \"delete-product_category\", \"view-product_category\", \"export-product_category\", \"create-referral\", \"edit-referral\", \"delete-referral\", \"view-referral\", \"create-role\", \"edit-role\", \"delete-role\", \"view-role\", \"export-role\", \"create-subscription\", \"edit-subscription\", \"delete-subscription\", \"view-subscription\", \"create-swap_category\", \"edit-swap_category\", \"delete-swap_category\", \"view-swap_category\", \"create-swap\", \"edit-swap\", \"delete-swap\", \"view-swap\", \"create-temporary_fund\", \"edit-temporary_fund\", \"delete-temporary_fund\", \"view-temporary_fund\", \"execute-temporary_fund\", \"generate-bill-number-temporary_fund\", \"create-transaction\", \"edit-transaction\", \"delete-transaction\", \"view-transaction\", \"export-transaction\", \"edit-after-execute-transaction\", \"create-manage_transaction\", \"edit-manage_transaction\", \"delete-manage_transaction\", \"view-manage_transaction\", \"export-manage_transaction\", \"wallet-summary-recalculate-manage_transaction\", \"toggle-visibility-transaction\", \"create-missing-transaction\", \"create-user\", \"edit-user\", \"delete-user\", \"view-user\", \"login-as-user\", \"create-emails_crud\", \"edit-emails_crud\", \"delete-emails_crud\", \"view-emails_crud\", \"view-existing_users\", \"create-user_document\", \"edit-user_document\", \"delete-user_document\", \"view-user_document\", \"id-wise-user_document\", \"create-user_eligibility\", \"edit-user_eligibility\", \"delete-user_eligibility\", \"view-user_eligibility\", \"export-user_eligibility\", \"create-language\", \"edit-language\", \"delete-language\", \"view-language\", \"create-market_report\", \"edit-market_report\", \"delete-market_report\", \"view-market_report\", \"send-market_report\", \"export-market_report\", \"send-email-market_report\", \"create-market_report_type\", \"edit-market_report_type\", \"delete-market_report_type\", \"view-market_report_type\", \"create-market_report_instrument\", \"edit-market_report_instrument\", \"delete-market_report_instrument\", \"view-market_report_instrument\", \"create-slider\", \"edit-slider\", \"delete-slider\", \"view-slider\", \"export-slider\", \"create-trading_platform\", \"edit-trading_platform\", \"delete-trading_platform\", \"view-trading_platform\", \"create-instrument\", \"edit-instrument\", \"delete-instrument\", \"view-instrument\", \"export-instrument\", \"create-permission\", \"edit-permission\", \"delete-permission\", \"view-permission\", \"export-permission\", \"create-account_form\", \"edit-account_form\", \"delete-account_form\", \"view-account_form\", \"export-account_form\", \"create-account_form_category\", \"edit-account_form_category\", \"delete-account_form_category\", \"view-account_form_category\", \"create-app_relation\", \"edit-app_relation\", \"delete-app_relation\", \"view-app_relation\", \"view-bulk-assign-app_relation\", \"create-seos\", \"edit-seos\", \"delete-seos\", \"view-seos\", \"create-user_login_log\", \"edit-user_login_log\", \"delete-user_login_log\", \"view-user_login_log\", \"export-user_login_log\", \"view-api_key_access_event\", \"view-payment_method_webhook_log\", \"view-webhook_log\", \"view-cron_job_logs\", \"view-activity_log\", \"documents-activity-activity_log\", \"view-third_party_log\", \"view-dynamic_email_history\", \"create-app_relation_country_assign\", \"edit-app_relation_country_assign\", \"delete-app_relation_country_assign\", \"view-app_relation_country_assign\", \"view-group_analytic\", \"view-application_analytic\", \"view-mt_account_analytic\", \"view-transaction_analytic\", \"view-market_reports_analytic\", \"view-ticket_analytic\", \"view-market_alert\", \"view-fraud_dashboard\", \"create-faq\", \"edit-faq\", \"delete-faq\", \"view-faq\", \"export-faq\", \"create-contact_request_subject\", \"edit-contact_request_subject\", \"delete-contact_request_subject\", \"view-contact_request_subject\", \"export-contact_request_subject\", \"create-error_log\", \"edit-error_log\", \"delete-error_log\", \"view-error_log\", \"export-error_log\", \"create-affiliate_website_config\", \"edit-affiliate_website_config\", \"delete-affiliate_website_config\", \"view-affiliate_website_config\", \"create-mt_group_spec\", \"edit-mt_group_spec\", \"delete-mt_group_spec\", \"view-mt_group_spec\", \"create-mt_group_forward\", \"edit-mt_group_forward\", \"delete-mt_group_forward\", \"view-mt_group_forward\", \"create-ticket\", \"edit-ticket\", \"delete-ticket\", \"view-ticket\", \"export-ticket\", \"create-manage_ticket\", \"edit-manage_ticket\", \"delete-manage_ticket\", \"view-manage_ticket\", \"create-corporate_event\", \"edit-corporate_event\", \"delete-corporate_event\", \"view-corporate_event\", \"view-import-corporate_event\", \"create-trading_platform_numbering\", \"edit-trading_platform_numbering\", \"delete-trading_platform_numbering\", \"view-trading_platform_numbering\", \"create-promotion_slider\", \"edit-promotion_slider\", \"delete-promotion_slider\", \"view-promotion_slider\", \"export-promotion_slider\", \"create-regulation_entity\", \"edit-regulation_entity\", \"delete-regulation_entity\", \"view-regulation_entity\", \"create-app_opportunity_option\", \"edit-app_opportunity_option\", \"delete-app_opportunity_option\", \"view-app_opportunity_option\", \"create-opportunity_option\", \"edit-opportunity_option\", \"delete-opportunity_option\", \"view-opportunity_option\", \"create-comment\", \"edit-comment\", \"delete-comment\", \"view-comment\", \"create-contact\", \"edit-contact\", \"delete-contact\", \"view-contact\", \"view-assign-contact\", \"create-trading_platform_configuration\", \"edit-trading_platform_configuration\", \"delete-trading_platform_configuration\", \"view-trading_platform_configuration\", \"create-job\", \"edit-job\", \"delete-job\", \"view-job\", \"create-site_banking_detail\", \"edit-site_banking_detail\", \"delete-site_banking_detail\", \"view-site_banking_detail\", \"create-watch_list\", \"edit-watch_list\", \"delete-watch_list\", \"view-watch_list\", \"create-translation\", \"edit-translation\", \"delete-translation\", \"view-translation\", \"create-institutional_service\", \"edit-institutional_service\", \"delete-institutional_service\", \"view-institutional_service\", \"create-institutional_contact_request\", \"edit-institutional_contact_request\", \"delete-institutional_contact_request\", \"view-institutional_contact_request\", \"view-assign-institutional_contact_request\", \"create-contact_reminder\", \"edit-contact_reminder\", \"delete-contact_reminder\", \"view-contact_reminder\", \"view-calendar-contact_reminder\", \"create-user_session\", \"edit-user_session\", \"delete-user_session\", \"view-user_session\", \"view-equity_report\", \"export-equity_report\", \"view-open_position_report\", \"export-open_position_report\", \"view-liquidation_report\", \"export-liquidation_report\", \"view-scalping_report\", \"export-scalping_report\", \"store-comment-scalping_report\", \"view-wallet_report\", \"export-wallet_report\", \"view-duplicate_application_report\", \"view-index_dividends_reports\", \"view-closed_position_report\", \"export-closed_position_report\", \"view-portal_login_report\", \"export-portal_login_report\", \"view-deposit_and_withdrawal_report\", \"export-deposit_and_withdrawal_report\", \"view-payment_method_report\", \"export-payment_method_report\", \"view-ticket_report\", \"export-ticket_report\", \"view-referral_report\", \"export-referral_report\", \"view-retention_report\", \"export-retention_report\", \"view-campaign_report\", \"view-expired_account_report\", \"export-expired_account_report\", \"view-gainers_and_losers_report\", \"export-gainers_and_losers_report\", \"kpi-retention_report\", \"view-acquisition_report\", \"daily-kpi-acquisition_report\", \"monthly-kpi-acquisition_report\", \"view-client_timeline_report\", \"export-client_timeline_report\", \"view-corporate_events_report\", \"view-risk_kpi\", \"view-mt_pending_orders\", \"export-mt_pending_orders\", \"view-mt_wallet_report\", \"export-mt_wallet_report\", \"view-agent_transaction_report\", \"export-agent_transaction_report\", \"view-aml_report\", \"export-aml_report\", \"view-app_relation_report\", \"export-app_relation_report\", \"view-mt_prices\", \"view-sales_deposit_withdrawal_report\", \"export-sales_deposit_withdrawal_report\", \"view-payment_gateway_equity\", \"export-payment_gateway_equity\", \"view-client_retention_report\", \"export-client_retention_report\", \"view-leads_widgets\", \"view-leads_report\", \"export-leads_report\", \"view-agent_activity_report\", \"export-agent_activity_report\", \"view-sales_summary\", \"export-sales_summary\", \"view-call_center\", \"view-risk_hedging_kpi\", \"view-risk_product_kpi\", \"view-deposits_and_withdrawals_summary\", \"lead-dashboard-widget-leads_report\", \"view-clients_deposit_withdrawal_report\", \"export-clients_deposit_withdrawal_report\", \"view-dashboard_kpi\", \"create-user_log\", \"edit-user_log\", \"delete-user_log\", \"view-user_log\", \"create-mt_agent_restriction\", \"edit-mt_agent_restriction\", \"delete-mt_agent_restriction\", \"view-mt_agent_restriction\", \"export-mt_agent_restriction\", \"create-app_available_group\", \"edit-app_available_group\", \"delete-app_available_group\", \"view-app_available_group\", \"create-affiliate_website_translatable_config\", \"edit-affiliate_website_translatable_config\", \"delete-affiliate_website_translatable_config\", \"view-affiliate_website_translatable_config\", \"create-faq_category\", \"edit-faq_category\", \"delete-faq_category\", \"view-faq_category\", \"create-career\", \"edit-career\", \"delete-career\", \"view-career\", \"create-payment_gateway_exception\", \"edit-payment_gateway_exception\", \"delete-payment_gateway_exception\", \"view-payment_gateway_exception\", \"export-payment_gateway_exception\", \"view-call-center\", \"click-to-call-call-center\", \"create-voip_extension\", \"edit-voip_extension\", \"delete-voip_extension\", \"view-voip_extension\", \"create-campaign-lead\", \"edit-campaign-lead\", \"delete-campaign-lead\", \"view-campaign-lead\", \"export-campaign-lead\", \"view-assign-campaign-lead\", \"view-import-campaign-lead\", \"create-competition_ticket\", \"edit-competition_ticket\", \"delete-competition_ticket\", \"view-competition_ticket\", \"export-competition_ticket\", \"create-payment_gateway_config\", \"edit-payment_gateway_config\", \"delete-payment_gateway_config\", \"view-payment_gateway_config\", \"create-partnership_lead\", \"edit-partnership_lead\", \"delete-partnership_lead\", \"view-partnership_lead\", \"create-affiliate_website_exception\", \"edit-affiliate_website_exception\", \"delete-affiliate_website_exception\", \"view-affiliate_website_exception\", \"create-mt_group_spec_swap\", \"edit-mt_group_spec_swap\", \"delete-mt_group_spec_swap\", \"view-mt_group_spec_swap\", \"create-mt_group_allocation\", \"edit-mt_group_allocation\", \"delete-mt_group_allocation\", \"view-mt_group_allocation\", \"create-educational_material_category\", \"edit-educational_material_category\", \"delete-educational_material_category\", \"view-educational_material_category\", \"create-educational_material\", \"edit-educational_material\", \"delete-educational_material\", \"view-educational_material\", \"export-educational_material\", \"create-api_key\", \"edit-api_key\", \"delete-api_key\", \"view-api_key\", \"create-api_key_access_event\", \"edit-api_key_access_event\", \"delete-api_key_access_event\", \"create-email_tags\", \"edit-email_tags\", \"delete-email_tags\", \"view-email_tags\", \"export-email_tags\", \"create-bank_trans_specs\", \"edit-bank_trans_specs\", \"delete-bank_trans_specs\", \"view-bank_trans_specs\", \"create-educational_article\", \"edit-educational_article\", \"delete-educational_article\", \"view-educational_article\", \"export-educational_article\", \"create-order_type\", \"edit-order_type\", \"delete-order_type\", \"view-order_type\", \"create-gender\", \"edit-gender\", \"delete-gender\", \"view-gender\", \"export-gender\", \"create-user_title\", \"edit-user_title\", \"delete-user_title\", \"view-user_title\", \"export-user_title\", \"create-marital_status\", \"edit-marital_status\", \"delete-marital_status\", \"view-marital_status\", \"export-marital_status\", \"create-job_position\", \"edit-job_position\", \"delete-job_position\", \"view-job_position\", \"export-job_position\", \"create-business_sector\", \"edit-business_sector\", \"delete-business_sector\", \"view-business_sector\", \"export-business_sector\", \"create-professionalism\", \"edit-professionalism\", \"delete-professionalism\", \"view-professionalism\", \"export-professionalism\", \"create-source_of_fund\", \"edit-source_of_fund\", \"delete-source_of_fund\", \"view-source_of_fund\", \"export-source_of_fund\", \"create-company_type\", \"edit-company_type\", \"delete-company_type\", \"view-company_type\", \"export-company_type\", \"create-ticket_tag\", \"edit-ticket_tag\", \"delete-ticket_tag\", \"view-ticket_tag\", \"create-webinar\", \"edit-webinar\", \"delete-webinar\", \"view-webinar\", \"export-webinar\", \"view-assign-webinar\", \"create-campaign_template\", \"edit-campaign_template\", \"delete-campaign_template\", \"view-campaign_template\", \"create-campaign\", \"edit-campaign\", \"delete-campaign\", \"view-campaign\", \"export-campaign\", \"create-tutorial_video\", \"edit-tutorial_video\", \"delete-tutorial_video\", \"view-tutorial_video\", \"export-tutorial_video\", \"create-notification\", \"edit-notification\", \"delete-notification\", \"view-notification\", \"create-application_status_tag\", \"edit-application_status_tag\", \"delete-application_status_tag\", \"view-application_status_tag\", \"create-province\", \"edit-province\", \"delete-province\", \"view-province\", \"export-province\", \"create-city\", \"edit-city\", \"delete-city\", \"view-city\", \"export-city\", \"create-mt_tags\", \"edit-mt_tags\", \"delete-mt_tags\", \"view-mt_tags\", \"create-mail_templates\", \"edit-mail_templates\", \"delete-mail_templates\", \"view-mail_templates\", \"view-call_requests\", \"storeCallRequest-call_requests\", \"create-call_requests\", \"edit-call_requests\", \"delete-call_requests\", \"create-classification_option\", \"edit-classification_option\", \"delete-classification_option\", \"view-classification_option\", \"create-app_classification\", \"edit-app_classification\", \"delete-app_classification\", \"view-app_classification\", \"create-group_tag\", \"edit-group_tag\", \"delete-group_tag\", \"view-group_tag\", \"create-withdrawal_transaction_internal_systems\", \"edit-withdrawal_transaction_internal_systems\", \"delete-withdrawal_transaction_internal_systems\", \"view-withdrawal_transaction_internal_systems\", \"create-deposit_transaction_internal_systems\", \"edit-deposit_transaction_internal_systems\", \"delete-deposit_transaction_internal_systems\", \"view-deposit_transaction_internal_systems\", \"view-view_fcm_tokens\", \"clear-tokens-view_fcm_tokens\", \"create-payment_method_webhook_log\", \"edit-payment_method_webhook_log\", \"delete-payment_method_webhook_log\", \"create-seminar\", \"edit-seminar\", \"delete-seminar\", \"view-seminar\", \"export-seminar\", \"create-newsroom\", \"edit-newsroom\", \"delete-newsroom\", \"view-newsroom\", \"export-newsroom\", \"create-transaction_type\", \"edit-transaction_type\", \"delete-transaction_type\", \"view-transaction_type\", \"create-social_information\", \"edit-social_information\", \"delete-social_information\", \"view-social_information\", \"create-document_type\", \"edit-document_type\", \"delete-document_type\", \"view-document_type\", \"create-sub_branch\", \"edit-sub_branch\", \"delete-sub_branch\", \"view-sub_branch\", \"create-contact_branch_detail\", \"edit-contact_branch_detail\", \"delete-contact_branch_detail\", \"view-contact_branch_detail\", \"create-branch\", \"edit-branch\", \"delete-branch\", \"view-branch\", \"create-whitelabel_transaction\", \"edit-whitelabel_transaction\", \"delete-whitelabel_transaction\", \"view-whitelabel_transaction\", \"transactions-history-whitelabel_transaction\", \"pending-transactions-whitelabel_transaction\", \"execute-whitelabel_transaction\", \"internal-transfer-request-whitelabel_transaction\", \"create-whitelabel_crud\", \"edit-whitelabel_crud\", \"delete-whitelabel_crud\", \"view-whitelabel_crud\", \"create-popular_instruments\", \"edit-popular_instruments\", \"delete-popular_instruments\", \"view-popular_instruments\", \"create-mt_group_shared\", \"edit-mt_group_shared\", \"delete-mt_group_shared\", \"view-mt_group_shared\", \"create-account_type\", \"edit-account_type\", \"delete-account_type\", \"view-account_type\", \"create-webhook_log\", \"edit-webhook_log\", \"delete-webhook_log\", \"create-user_archived_chats\", \"edit-user_archived_chats\", \"delete-user_archived_chats\", \"view-user_archived_chats\", \"all-chats-user_archived_chats\", \"create-flag_type\", \"edit-flag_type\", \"delete-flag_type\", \"view-flag_type\", \"export-flag_type\", \"create-new_license_confirmation\", \"edit-new_license_confirmation\", \"delete-new_license_confirmation\", \"view-new_license_confirmation\", \"create-user_term_confirmation\", \"edit-user_term_confirmation\", \"delete-user_term_confirmation\", \"view-user_term_confirmation\", \"create-cron_job_logs\", \"edit-cron_job_logs\", \"delete-cron_job_logs\", \"view-application-activity_log\", \"create-material_category\", \"edit-material_category\", \"delete-material_category\", \"view-material_category\", \"export-material_category\", \"create-material_dimension\", \"edit-material_dimension\", \"delete-material_dimension\", \"view-material_dimension\", \"create-marketing_material\", \"edit-marketing_material\", \"delete-marketing_material\", \"view-marketing_material\", \"create-internal_requests\", \"edit-internal_requests\", \"delete-internal_requests\", \"view-internal_requests\", \"create-timezone\", \"edit-timezone\", \"delete-timezone\", \"view-timezone\", \"view-commission_system_config\", \"create-commission_system_config\", \"edit-commission_system_config\", \"delete-commission_system_config\", \"create-country_entity_risk_level\", \"edit-country_entity_risk_level\", \"delete-country_entity_risk_level\", \"view-country_entity_risk_level\", \"create-affiliate_website_config_key\", \"edit-affiliate_website_config_key\", \"delete-affiliate_website_config_key\", \"view-affiliate_website_config_key\", \"edit-website-keys-affiliate_website_config_key\", \"update-website-keys-affiliate_website_config_key\", \"create-media\", \"edit-media\", \"delete-media\", \"view-media\", \"view-bulk_sms\", \"send-sms-bulk_sms\", \"create-bulk_sms\", \"edit-bulk_sms\", \"delete-bulk_sms\", \"create-offer_term\", \"edit-offer_term\", \"delete-offer_term\", \"view-offer_term\", \"create-security\", \"edit-security\", \"delete-security\", \"view-security\", \"create-symbol\", \"edit-symbol\", \"delete-symbol\", \"view-symbol\", \"create-symbol_group\", \"edit-symbol_group\", \"delete-symbol_group\", \"view-symbol_group\", \"create-offer_scenario\", \"edit-offer_scenario\", \"delete-offer_scenario\", \"view-offer_scenario\", \"clone-offer_scenario\", \"create-app_offer\", \"edit-app_offer\", \"delete-app_offer\", \"view-app_offer\", \"export-pdf-app_offer\", \"send-offer-app_offer\", \"clone-app_offer\", \"change-deduct-scalping-app_offer\", \"store-agent-category-app_offer\", \"view-agent-category-app_offer\", \"deactivate-offer-app_offer\", \"view-tree-app_offer\", \"export-app_offer\", \"create-commission_transaction\", \"edit-commission_transaction\", \"delete-commission_transaction\", \"view-commission_transaction\", \"export-commission_transaction\", \"create-application_sales_commission\", \"edit-application_sales_commission\", \"delete-application_sales_commission\", \"view-application_sales_commission\", \"create-offer_scenario_detail\", \"edit-offer_scenario_detail\", \"delete-offer_scenario_detail\", \"view-offer_scenario_detail\", \"index-details-offer_scenario_detail\", \"create-offer_scenario_groups\", \"edit-offer_scenario_groups\", \"delete-offer_scenario_groups\", \"view-offer_scenario_groups\", \"create-agent_categories\", \"edit-agent_categories\", \"delete-agent_categories\", \"view-agent_categories\", \"create-webinar-leads\", \"edit-webinar-leads\", \"delete-webinar-leads\", \"view-webinar-leads\", \"export-webinar-leads\", \"view-assign-webinar-leads\", \"view-import-webinar-leads\", \"create-seminar-leads\", \"edit-seminar-leads\", \"delete-seminar-leads\", \"view-seminar-leads\", \"export-seminar-leads\", \"view-assign-seminar-leads\", \"view-import-seminar-leads\", \"create-app_available_offer\", \"edit-app_available_offer\", \"delete-app_available_offer\", \"view-app_available_offer\", \"create-cellxpert-default-rules\", \"edit-cellxpert-default-rules\", \"delete-cellxpert-default-rules\", \"view-cellxpert-default-rules\", \"create-cellxpert-sales-rules\", \"edit-cellxpert-sales-rules\", \"delete-cellxpert-sales-rules\", \"view-cellxpert-sales-rules\", \"create-bitcoin_address\", \"edit-bitcoin_address\", \"delete-bitcoin_address\", \"view-bitcoin_address\", \"create-department\", \"edit-department\", \"delete-department\", \"view-department\", \"create-political_questions\", \"edit-political_questions\", \"delete-political_questions\", \"view-political_questions\", \"export-political_questions\", \"create-cronjob_configuration\", \"edit-cronjob_configuration\", \"delete-cronjob_configuration\", \"view-cronjob_configuration\", \"create-trading_server\", \"edit-trading_server\", \"delete-trading_server\", \"view-trading_server\", \"create-trading_server_config\", \"edit-trading_server_config\", \"delete-trading_server_config\", \"view-trading_server_config\", \"view-mt_sync_exception\", \"create-oauth_client\", \"edit-oauth_client\", \"delete-oauth_client\", \"view-oauth_client\", \"create-oauth_client_config_key\", \"edit-oauth_client_config_key\", \"delete-oauth_client_config_key\", \"view-oauth_client_config_key\", \"edit-client-keys-oauth_client_config_key\", \"update-client-keys-oauth_client_config_key\", \"create-whitelabel_trading_platform\", \"edit-whitelabel_trading_platform\", \"delete-whitelabel_trading_platform\", \"view-whitelabel_trading_platform\", \"create-exchange_rate\", \"edit-exchange_rate\", \"delete-exchange_rate\", \"view-exchange_rate\", \"view-migrate_mt_group\", \"view-migrate_app_available_group\", \"view-migrate_app_cellxpert\", \"view-migrate_application\", \"view-migrate_id_application\", \"view-migrate_sales_application\", \"view-migrate_disabled_id_application\", \"view-migrate_hide_id_mt_accounts\", \"view-migrate_delete_id_application\", \"create-flag_per\", \"edit-flag_per\", \"delete-flag_per\", \"view-flag_per\", \"create-university\", \"edit-university\", \"delete-university\", \"view-university\", \"create-university_lead\", \"edit-university_lead\", \"delete-university_lead\", \"view-university_lead\", \"export-university_lead\", \"create-crypto_internal_transaction\", \"edit-crypto_internal_transaction\", \"delete-crypto_internal_transaction\", \"view-crypto_internal_transaction\", \"create-glossary_term\", \"edit-glossary_term\", \"delete-glossary_term\", \"view-glossary_term\", \"create-promotion\", \"edit-promotion\", \"delete-promotion\", \"view-promotion\", \"enable-for-app-promotion\", \"create-user_restriction\", \"edit-user_restriction\", \"delete-user_restriction\", \"view-user_restriction\", \"view-risk_activity\", \"view-manaf_bank\", \"create-manaf_bank\", \"edit-manaf_bank\", \"delete-manaf_bank\", \"view-manaf_bank_account\", \"create-manaf_bank_account\", \"edit-manaf_bank_account\", \"delete-manaf_bank_account\", \"create-exchanger\", \"edit-exchanger\", \"delete-exchanger\", \"view-exchanger\", \"create-check_list\", \"edit-check_list\", \"delete-check_list\", \"view-check_list\", \"create-mail_notifier\", \"edit-mail_notifier\", \"delete-mail_notifier\", \"view-mail_notifier\", \"send-email-mail_notifier\", \"create-dynamic_email_templates\", \"edit-dynamic_email_templates\", \"delete-dynamic_email_templates\", \"view-dynamic_email_templates\", \"create-dynamic_email_contents\", \"edit-dynamic_email_contents\", \"delete-dynamic_email_contents\", \"view-dynamic_email_contents\", \"mail-key-tags-dynamic_email_contents\", \"send-test-email-dynamic_email_contents\", \"preview-dynamic_email_contents\", \"create-dynamic_email_tags\", \"edit-dynamic_email_tags\", \"delete-dynamic_email_tags\", \"view-dynamic_email_tags\", \"create-account_form_version\", \"edit-account_form_version\", \"delete-account_form_version\", \"view-account_form_version\", \"view-user_request\", \"internal-transfer-user_request\", \"create-university_college\", \"edit-university_college\", \"delete-university_college\", \"view-university_college\", \"create-complaint\", \"edit-complaint\", \"delete-complaint\", \"view-complaint\", \"export-complaint\", \"create-manage_complaint\", \"edit-manage_complaint\", \"delete-manage_complaint\", \"view-manage_complaint\", \"create-asic_answer_advanced_logics\", \"edit-asic_answer_advanced_logics\", \"delete-asic_answer_advanced_logics\", \"view-asic_answer_advanced_logics\", \"create-internal_notifications\", \"edit-internal_notifications\", \"delete-internal_notifications\", \"view-internal_notifications\"]\"\n      \"settings\" => \"[\"has_authorized_regions\", \"has_assigned_view_ams\", \"has_managed_roles\"]\"\n      \"website_ids\" => array:1 [\n        0 => \"all\"\n      ]\n      \"created_at\" => \"2019-02-12T12:54:33.000000Z\"\n      \"updated_at\" => \"2025-05-27T09:31:57.000000Z\"\n      \"deleted_at\" => null\n      \"authorized_regions\" => \"[\"all\"]\"\n      \"authorized_roles\" => \"[\"all\"]\"\n      \"authorized_ams\" => []\n      \"registrable\" => 0\n      \"managed_roles\" => array:1 [\n        0 => \"all\"\n      ]\n    ]\n    \"application\" => array:16 [\n      \"id\" => 1\n      \"app_type_id\" => 1\n      \"approved\" => \"2\"\n      \"completed\" => \"2\"\n      \"profile_strength\" => \"{\"profile_information\": {\"1\": \"1\"}, \"term_and_conditions\": \"0\"}\"\n      \"affiliate_website_id\" => 3\n      \"referral_id\" => 10\n      \"referral_value_id\" => \"direct\"\n      \"referral_hash\" => null\n      \"created_at\" => \"2019-02-12T12:54:33.000000Z\"\n      \"updated_at\" => \"2023-02-26T10:54:37.000000Z\"\n      \"deleted_at\" => null\n      \"profile_strength_percentage\" => 50.0\n      \"affiliate_website\" => array:10 [\n        \"id\" => 3\n        \"name\" => \"portal.ingotbrokers.local\"\n        \"crm_name\" => \"crm.ingotbrokers.com\"\n        \"site_name\" => \"www.ingotbrokers.com\"\n        \"aliases\" => array:1 [\n          0 => \"portal-fa.ingotbrokers.com\"\n        ]\n        \"iso\" => \"global\"\n        \"regulation_entity_id\" => 2\n        \"created_at\" => \"2019-05-30T10:02:04.000000Z\"\n        \"updated_at\" => \"2023-08-13T16:26:00.000000Z\"\n        \"deleted_at\" => null\n      ]\n      \"app_type\" => array:8 [\n        \"id\" => 1\n        \"name\" => \"admin\"\n        \"display_name\" => \"Admin\"\n        \"description\" => null\n        \"websites\" => \"[]\"\n        \"created_at\" => \"2019-02-12T12:54:32.000000Z\"\n        \"updated_at\" => \"2019-10-10T09:49:20.000000Z\"\n        \"deleted_at\" => null\n      ]\n      \"main_app_wallet\" => array:7 [\n        \"id\" => 55534\n        \"balance\" => \"0\"\n        \"application_id\" => 1\n        \"currency_id\" => 1\n        \"created_at\" => \"2022-11-20T08:13:01.000000Z\"\n        \"updated_at\" => \"2022-11-20T08:13:01.000000Z\"\n        \"deleted_at\" => null\n      ]\n    ]\n    \"two_factor_devices\" => []\n  ]\n]"}, "names": "web: <EMAIL>"}, "gate": {"count": 27, "messages": [{"message": "[\n  ability => view-existing_users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1910725655 data-indent-pad=\"  \"><span class=sf-dump-note>view-existing_users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view-existing_users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910725655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.298952, "xdebug_link": null}, {"message": "[\n  ability => view-calendar-contact_reminder,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-117937287 data-indent-pad=\"  \"><span class=sf-dump-note>view-calendar-contact_reminder </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">view-calendar-contact_reminder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117937287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.573227, "xdebug_link": null}, {"message": "[\n  ability => view-my-requests,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view-my-requests </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view-my-requests</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.578945, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.581613, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.58258, "xdebug_link": null}, {"message": "[\n  ability => view-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.583802, "xdebug_link": null}, {"message": "[\n  ability => view-error_log,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1311154741 data-indent-pad=\"  \"><span class=sf-dump-note>view-error_log </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-error_log</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311154741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.584414, "xdebug_link": null}, {"message": "[\n  ability => view-promotion,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-69368800 data-indent-pad=\"  \"><span class=sf-dump-note>view-promotion </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-promotion</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69368800\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.584965, "xdebug_link": null}, {"message": "[\n  ability => view-migrate_mt_group,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1500016855 data-indent-pad=\"  \"><span class=sf-dump-note>view-migrate_mt_group </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view-migrate_mt_group</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500016855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.585492, "xdebug_link": null}, {"message": "[\n  ability => view-deposit_transaction_internal_systems,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1557191602 data-indent-pad=\"  \"><span class=sf-dump-note>view-deposit_transaction_internal_systems </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"41 characters\">view-deposit_transaction_internal_systems</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557191602\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.586036, "xdebug_link": null}, {"message": "[\n  ability => view-transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1544975877 data-indent-pad=\"  \"><span class=sf-dump-note>view-transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view-transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544975877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.58655, "xdebug_link": null}, {"message": "[\n  ability => view-manage_transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1884717520 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884717520\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587091, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1263442220 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263442220\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587634, "xdebug_link": null}, {"message": "[\n  ability => view-campaign-lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-544587806 data-indent-pad=\"  \"><span class=sf-dump-note>view-campaign-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-campaign-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544587806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588142, "xdebug_link": null}, {"message": "[\n  ability => view-contact,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1063652947 data-indent-pad=\"  \"><span class=sf-dump-note>view-contact </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view-contact</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063652947\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588646, "xdebug_link": null}, {"message": "[\n  ability => view-deposits_and_withdrawals_summary,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1400034778 data-indent-pad=\"  \"><span class=sf-dump-note>view-deposits_and_withdrawals_summary </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"37 characters\">view-deposits_and_withdrawals_summary</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400034778\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.589151, "xdebug_link": null}, {"message": "[\n  ability => view-wallet_report,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view-wallet_report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-wallet_report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.589655, "xdebug_link": null}, {"message": "[\n  ability => access-account-manager-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-account-manager-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">access-account-manager-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.594834, "xdebug_link": null}, {"message": "[\n  ability => access-affiliate-links,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access-affiliate-links </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">access-affiliate-links</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.597966, "xdebug_link": null}, {"message": "[\n  ability => transactions-history-whitelabel_transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>transactions-history-whitelabel_transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"43 characters\">transactions-history-whitelabel_transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.598517, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.601337, "xdebug_link": null}, {"message": "[\n  ability => access-security-preferences,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-security-preferences </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">access-security-preferences</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602403, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.604687, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.619583, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.620521, "xdebug_link": null}, {"message": "[\n  ability => create-watch_list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create-watch_list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create-watch_list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621045, "xdebug_link": null}, {"message": "[\n  ability => create-missing-transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-991398003 data-indent-pad=\"  \"><span class=sf-dump-note>create-missing-transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">create-missing-transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991398003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.622635, "xdebug_link": null}]}, "session": {"_token": "Hkq9N6gyL5k71pOWnQhpufOpBAB19Ap0LOEPkYo4", "locale": "en", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://portal.ingotbrokers.local/_debugbar/assets/javascript?v=1740497122\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$LeRJ3FTYSs.N/cnxap60jus42Cwf/wNPWVAD4QmZvzn/nDi9zzbLm", "password_expired": "false", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://portal.ingotbrokers.local/en/existing-users?email=test_it-department%40ingotbrokers.com", "action_name": "existing-users.index", "controller_action": "App\\Http\\Controllers\\Admin\\ExistingUserController@index", "uri": "GET en/existing-users", "controller": "App\\Http\\Controllers\\Admin\\ExistingUserController@index<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FExistingUserController.php&line=16\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/en/existing-users", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FExistingUserController.php&line=16\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/ExistingUserController.php:16-34</a>", "middleware": "web, auth, password.expiry, localeSessionRedirect, localizationRedirect, localeViewPath, can:view-existing_users, Ingotbrokers", "telescope": "<a href=\"http://portal.ingotbrokers.local/_debugbar/telescope/9f02b32d-a0c1-4956-9f1a-d0312383aa16\" target=\"_blank\">View in Telescope</a>", "duration": "642ms", "peak_memory": "16MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1065202183 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"35 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065202183\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1589693558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1589693558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2007895395 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">portal.ingotbrokers.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"94 characters\">http://portal.ingotbrokers.local/en/existing-users?email=test_it-department%40ingotbrokers.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">en-AU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">ingot_brokers_session=V6oHfZ16YWAe8vSxjTTDNfF7bFJ9O0ygT6BXeqtx; XSRF-TOKEN=eyJpdiI6InloVmpJOXk5T0ZHazZ2Y2V1Znl4ZGc9PSIsInZhbHVlIjoiUXhRQnNVQURSLzFBL0JEZGxjcTZQZTNPRmNuTEdIZU5vOWN3Q21JUlFFSWxHb1JBOURKK2lyZVJlU1dUNWQ3aGhQR0granJCaVlpblJpVitpemdNcGNTSmtrSjFGWEFLemhrWnJCZGh2QkQyQWlvWkNjdCtqVXlnOFBJa1FGNk0iLCJtYWMiOiI4MmJlZGRmZmQ5ZGZiNDk2ODA3NjA2NTllZTM0Yjc2ODk0MGE2NDdkZjkxNzMzODg1YjdkNzBlN2VlOGQ5MzAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007895395\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1555337570 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ingot_brokers_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hkq9N6gyL5k71pOWnQhpufOpBAB19Ap0LOEPkYo4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555337570\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1866094600 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 09:50:48 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866094600\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-618401712 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hkq9N6gyL5k71pOWnQhpufOpBAB19Ap0LOEPkYo4</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://portal.ingotbrokers.local/_debugbar/assets/javascript?v=1740497122</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$LeRJ3FTYSs.N/cnxap60jus42Cwf/wNPWVAD4QmZvzn/nDi9zzbLm</span>\"\n  \"<span class=sf-dump-key>password_expired</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618401712\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://portal.ingotbrokers.local/en/existing-users?email=test_it-department%40ingotbrokers.com", "action_name": "existing-users.index", "controller_action": "App\\Http\\Controllers\\Admin\\ExistingUserController@index"}, "badge": null}}