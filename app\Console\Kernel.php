<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [

    ];

    /**
     * By default, multiple tasks scheduled at the same time will execute sequentially based on the order they are defined in your schedule method.
     * If you have long-running tasks, this may cause subsequent tasks to start much later than anticipated.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $defaultQueue = config('queue.connections.database.queue');
        $notifyQueue = config('queue.connections.database.queue_notify');
        $transactionQueue = config('queue.connections.database.queue_transaction');

        $queuesNames = implode(',', [$transactionQueue, $notifyQueue, $defaultQueue]);

        # --queue option, you may specify which queue or queues should be worked.
        # --stop-when-empty option, the worker will continue to process jobs until the queue is empty.
        # --backoff option, you may specify how many seconds <PERSON><PERSON> should wait before retrying a job that has encountered an exception.
        # --timeout option, you may specify the maximum number of seconds that a child process can run.

        $schedule->command("queue:work --queue=$queuesNames --stop-when-empty --backoff=3600 --timeout=21600");
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
