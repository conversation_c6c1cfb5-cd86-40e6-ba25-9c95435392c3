<?php

namespace App\Exports\PDF;

use App\Exports\ExportManger;

abstract class PDF extends ExportManger
{
    /**
     * @var string|null
     */
    protected ?string $header;

    protected ?string $body;

    protected ?string $footer;

    protected ?string $view;

    public function __construct()
    {
        parent::__construct('pdf');
    }

    /**
     * @param $header
     * @param  null  $body
     * @param  null  $footer
     * @return $this
     */
    public function fillHtml($header, $body = null, $footer = null): PDF
    {
        // fill only header when you are going to load only view or file .
        $this->header = $header;
        // fill it when you are going to load html Tags .
        $this->body = $body;
        // fill it when you are going to load html Tags .
        $this->footer = $footer;

        return $this;
    }

    /**
     * @param  string  $view
     * @return $this
     */
    public function setView(string $view): static
    {
        $this->view = $view;

        return $this;
    }

    /**
     * @return mixed
     */
    abstract protected function template(): mixed;
}
