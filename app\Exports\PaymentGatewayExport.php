<?php

namespace App\Exports;

use App\Http\Controllers\Admin\PaymentGatewayController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PaymentGatewayExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(PaymentGatewayController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'NAME',
            'DEPOSIT PROCESSING TIME',
            'WITHDRAWAL PROCESSING TIME',

        ];
    }

    public function map($data): array
    {
        return [
            $data->name,
            $data->deposit_execution_period ?? '-',
            $data->withdrawal_execution_period ?? '-'

        ];
    }
}
