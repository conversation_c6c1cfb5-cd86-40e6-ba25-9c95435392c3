<?php

namespace App\Exports;

use App\Enums\MetaTrader4Enum;
use App\Enums\MetaTrader5Enum;
use App\Http\Controllers\Admin\CommissionTransactionController;
use App\Models\CommissionTransaction;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class CommissionTransactionsExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(CommissionTransactionController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Ticket',
            'Symbol',
            'Security',
            'Offer',
            'Login',
            'Commission',
            'Client',
            'Commission Receiver',
            'Volume',
            'Entry',
            'Trading Platform',
            'Order Type',
            'Trade Type',
            'Paid',
            'Trade Time',
        ];
    }

    public function map($row): array
    {
        $entry_values = CommissionTransaction::$ENTRY_TYPE;
        $paid_values = CommissionTransaction::$COMMISSION_PAID_VALUES;
        $trade_types_values = CommissionTransaction::$TRADE_TYPE_VALUES;
        $order_type_mt4 = MetaTrader4Enum::DEAL_ACTION;
        $order_type_mt5 = MetaTrader5Enum::DEAL_ACTION;

        return [
            $row->ticket_id ?? '-',
            $row->symbol_name ?? '-',
            $row->security_name ?? '-',
            $row->IbOfferScenarioDetails?->OfferScenarioDetail?->OfferScenario?->name ?? '-',
            $row->mtAccount?->login,
            $row->commission . ' ' . $row->offer_currency ,
            $row?->mtAccount?->application?->mainUser?->getUserHolderName(),
            $row->commission_receiver,
            $row->trade_volume,
            $entry_values[$row->entry],
            $row->trading_platform_name,
            $row->trading_platform_name == 'mt5' ? $order_type_mt5[$row->order_type] : ($row->trading_platform_name == 'mt4' ? $order_type_mt4[$row->order_type] : '-'),
            $trade_types_values[$row->trade_type],
            $paid_values[$row->paid],
            $row->trade_time
        ];
    }
}
