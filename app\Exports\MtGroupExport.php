<?php

namespace App\Exports;

use App\Http\Controllers\Admin\MtGroupController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtGroupExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(MtGroupController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Group ID',
            'Owner',
            'Name',
            'Display Name',
            'Internal Name',
            'Currency',
            'Platform',
            'Trading Server',
            'Type',
            'Allocation',
            'Reg. Accounts',
            'Reg. Av. Groups',
            'Default Leverage',
            'Entity',
            'Covering Account',
            'Created Date',
            'Updated On',
            'Meta Trader Group Tag',
        ];
    }

    public function map($row): array
    {
        $group_tags = [];
        foreach ($row->groupTagCategory as $item) {
            $group_tags[] = $item->groupTag->name ?? null;
        }
        $tags = implode(', ', array_filter($group_tags));

        return [
            $row->id,
            $row->mtAccount->login ?? 'INGOT',
            $row->name,
            $row->display_name ?? '-',
            $row->internal_name ?? '-',
            $row->currency->code ?? '-',
            $row->tradingPlatform->name ?? '-',
            $row->tradingServer->display_name ?? '-',
            $row->account_type,
            $row->mtGroupAllocation->name ?? '-',
            $row->mt_accounts_count ?? 0,
            $row->app_available_groups_count ?? 0,
            $row->default_leverage . ':1',
            $row->affiliateWebsite->regulationEntity->name ?? '-',
            $row->covering_login ?? '-',
            $row->created_at,
            $row->updated_at,
            !$row->groupTagCategory->isEmpty() ? $tags : '-',
        ];
    }
}
