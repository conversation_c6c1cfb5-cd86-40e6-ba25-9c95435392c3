<?php

namespace App\Exports;

use App\Http\Controllers\Admin\PortalLoginsReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PortalLoginExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(PortalLoginsReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID Application',
            'Name',
            'Country',
            'Role',
            'Regulation Entity',
            'Total Logins',
            'Last login',
        ];
    }

    public function map($login): array
    {
        return [
            isset($login->mainUser) ? $login->mainUser->application->appIdentifierId() : '-',
            isset($login->mainUser) ? $login->mainUser->getUserHolderName() : '-',
            $login->mainUser->country->name ?? '-',
            $login->mainUser->role->display_name ?? '-',
            $login->getRegulatedEntity(),
            $login->user_login_log_count,
            $login->latestLogin->created_at ?? trans('content.never_signin'),
        ];
    }
}
