<?php

namespace App\Helpers\ThirdPartyApis\MicrosoftGraph;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\App;
use JetBrains\PhpStorm\NoReturn;
use Nette\Utils\Json;

abstract class Request
{
    protected Client $client;

    public function __construct()
    {
        $this->client = new Client(
            [
                'verify' => !App::isLocal(),
            ]
        );
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Nette\Utils\JsonException
     */
    #[NoReturn]
    public function getBearerToken()
    {
        $url = 'https://login.microsoftonline.com/'.config('azure.tenantId').'/'.config('azure.getTokenUrI');
        $token = Json::decode($this->client->post($url, [
            'form_params' => [
                'client_id' => config('azure.clientId'),
                'client_secret' => config('azure.appSecret'),
                'resource' => config('azure.resource'),
                'grant_type' => 'client_credentials',
                'scopes' => config('azure.scopes'),
            ],
            'verify' => false,
        ])->getBody()->getContents());

        return $token->access_token;
    }
}
