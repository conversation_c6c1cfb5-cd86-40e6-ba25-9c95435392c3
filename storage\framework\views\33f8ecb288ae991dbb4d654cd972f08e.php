
<?php $__env->startSection('content'); ?>

    <div class="card">
        <a data-action="collapse">
            <div class="card-header">
                <h4 class="card-title"><?php echo e(__('content.search_filter')); ?></h4>
                <div class="heading-elements">
                    <ul class="list-inline mb-0">
                        <li><i class="la la-plus"></i></li>
                    </ul>
                </div>
            </div>
        </a>
        <div class="card-content card-filters collapse <?php echo e(collapsed_filter()); ?>">
            <div class="card-body">
                <form class="form-horizontal" action="<?php echo e(route('internal-notifications.index')); ?>" method="GET"
                    class="form-horizontal">
                    <div class="form-group row mb-0">
                        <div class="col-xl-3 col-lg-4 col-md-4 mb-1">
                            <input type="text" value="<?php echo e($request->get('name') ?? ''); ?>" name="name"
                                placeholder="<?php echo e(__('content.name')); ?>" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row mb-0">
                        <div class="col-xl-12 col-lg-12 col-md-12">
                            <button type="submit" class="btn btn-md btn-outline-dark btn-loading">
                                <i class="la la-search"></i> <?php echo e(__('content.search')); ?>

                            </button>
                            <a class="btn btn-outline-danger btn-loading"
                                href="<?php echo e(route('internal-notifications.index')); ?>">
                                <i class="la la-reply"></i> <?php echo e(__('content.reset')); ?>

                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title"><?php echo e(__('content.internal_notifications')); ?></h4>
            <div class="heading-elements">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-internal_notifications')): ?>
                    <a class="btn btn-success" href="<?php echo e(route('internal-notifications.create')); ?>"><i class="la la-plus"></i>
                        <span class="d-lg-inline-block d-none"><?php echo e(__('content.create')); ?></span>
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div class="card-content">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th><?php echo e(__('content.title')); ?></th>
                                <th><?php echo e(__('content.from_date')); ?></th>
                                <th><?php echo e(__('content.to_date')); ?></th>
                                <th><?php echo e(__('content.websites')); ?></th>
                                <th><?php echo e(__('content.active')); ?></th>
                                <th><?php echo e(__('content.action')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($notifications) > 0): ?>
                                <?php $counter = ($notifications->currentpage()-1) * $notifications->perpage() + 1; ?>
                                <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($counter++); ?></td>
                                        <td><?php echo e($item->name ?? '-'); ?></td>
                                        <td><?php echo e($item->from_date ?? '-'); ?></td>
                                        <td><?php echo e($item->to_date ?? '-'); ?></td>
                                        <td>
                                            <?php $__empty_1 = true; $__currentLoopData = $item->websites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site_id): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <?php if(array_key_exists($site_id, $affiliateWebsiteArr)): ?>
                                                    <?php echo e($affiliateWebsiteArr[$site_id]); ?>

                                                    <?php echo !$loop->last ? '<br/>' : ''; ?>

                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($item::$statuses[$item->status] ?? '-'); ?></td>
                                        <td>
                                            <div class="dropdown">
                                                <a id="ActionNav" data-boundary="window" data-toggle="dropdown"
                                                    aria-haspopup="true" aria-expanded="true">
                                                    <i class="la la-ellipsis-v la-2x align-middle"></i></a>
                                                </a>
                                                <span aria-labelledby="ActionNav" class="dropdown-menu">
                                                    <a class="dropdown-item"
                                                        href="<?php echo e(route('internal-notifications.show', $item->id)); ?>"><?php echo e(__('content.show')); ?>

                                                    </a>

                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete-internal_notifications')): ?>
                                                        <a class="dropdown-item confirmation_dialog"
                                                            data-url="<?php echo e(route('internal-notifications.destroy', $item->id)); ?>"
                                                            data-method="DELETE">
                                                            <?php echo e(__('content.delete')); ?>

                                                        </a>
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7"><?php echo e(__('content.no_data_available')); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                    <?php echo $__env->make('templates.pub.paginator', ['model' => $notifications], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>


<?php echo $__env->make('templates.crm.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\projects\ingotbrokers\resources\views/templates/crm/admin/existing_user/index.blade.php ENDPATH**/ ?>