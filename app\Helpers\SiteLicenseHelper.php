<?php

namespace App\Helpers;

use App\Models\AffiliateWebsite;
use App\Models\AffiliateWebsiteException;
use App\Models\Country;

class SiteLicenseHelper
{
    /**
     * @return array $result
     */
    public static function checkCountry($countryId = null, $affiliate_name = null)
    {
        $result = $affiliateWebsiteException = [];

        if (empty($affiliate_name)) {
            $affiliate_name = request()->getHost();
        }

        $affiliateWebsite = AffiliateWebsite::query()->where(function ($s) use ($affiliate_name) {
            $s->where('name', $affiliate_name)
                ->orWhere('site_name', $affiliate_name)
                ->orWhere('crm_name', $affiliate_name)
                ->orWhereJsonContains('aliases', $affiliate_name);
        })->first();

        if ($affiliateWebsite && $countryId) {
            $countryId = (string) filter_var($countryId, FILTER_SANITIZE_NUMBER_INT);
            $affiliateWebsiteException = $affiliateWebsite
                ->affiliateWebsiteExceptions()
                ->whereJsonContains('country_ids', (string) $countryId)
                ->whereHas('redirectAffiliateWebsite')
                ->first();

            $country_code = Country::query()->where('id', $countryId)->pluck('country_code')->first();
            $result['country_code'] = $country_code;

            if (!$affiliateWebsiteException) {
                $result['status'] = true;
            } else {
                $http = config('app.https_status') ? 'https' : 'http';
                $redirectUrl = $affiliateWebsiteException->redirectAffiliateWebsite->name;
                $redirectUrl = $http . '://' . $redirectUrl;

                $result['status'] = false;
                $result['redirect_url'] = $redirectUrl;
            }
        }

        return $result;
    }

    public static function getAffiliateWebsite($countryId)
    {
        $result = [];

        $countryId = (string) $countryId;
        $affiliateWebsiteException = AffiliateWebsiteException::whereJsonContains('country_ids', (string) $countryId)->first();
        if ($affiliateWebsiteException) {
            $result['affiliate_website_id'] = $affiliateWebsiteException->redirect_affiliate_website_id;
            $result['affiliate_website_name'] = $affiliateWebsiteException->redirectAffiliateWebsite->name;
        }

        return $result;
    }

    public static function restrictedCountriesBasedOnAffiliateWebsite()
    {
        $affiliate_name = request()->getHost();

        $affiliateWebsite = AffiliateWebsite::query()->where(function ($s) use ($affiliate_name) {
            $s->where('name', $affiliate_name)
                ->orWhere('site_name', $affiliate_name)
                ->orWhere('crm_name', $affiliate_name)
                ->orWhereJsonContains('aliases', $affiliate_name);
        })->first();

        if ($affiliateWebsite) {
            $affiliateWebsiteException = $affiliateWebsite
                ->affiliateWebsiteExceptions()
                ->whereNull('redirect_affiliate_website_id')
                ->first();

            if ($affiliateWebsiteException) {
                return $affiliateWebsiteException->country_ids;
            }
        }
    }
}
