<?php

namespace App\Helpers\ApiCall\Src\Handlers;

use Illuminate\Support\Facades\URL;

class Pagination
{
    protected $nextPageUrl;

    protected $previousPageUrl;

    protected $path;

    protected $perPage;

    protected $currentPage;

    protected $totalItems;

    protected $totalPages;

    protected $countDataViewed;

    protected $data;

    public function __construct($data)
    {
        $this->perPage = $data['per_page'];
        $this->totalItems = $data['total'];
        $this->totalPages = $data['last_page'];
        $this->data = new Data($data['data']);
        $this->countDataViewed = $data['to'];

        $this->path = URL::current().'?';
        parse_str(parse_url(URL::full(), PHP_URL_QUERY), $currentParams);
        if (!empty($currentParams)) {
            foreach ($currentParams as $key => $value) {
                if ($key !== 'page') {
                    $this->path .= "{$key}={$value}&";
                }
            }
        }

        $this->currentPage = $data['current_page'];
        if ($data['next_page_url']) {
            $this->nextPageUrl = $this->path.'page='.$this->currentPage + 1;
        }
        if ($this->currentPage > 1) {
            $this->previousPageUrl = $this->path.'page='.($this->currentPage - 1);
        }

        $this->path = trim($this->path, '&?');
    }

    /**
     * @return mixed
     */
    public function nextPageUrl(): mixed
    {
        return $this->nextPageUrl;
    }

    /**
     * @return mixed
     */
    public function previousPageUrl(): mixed
    {
        return $this->previousPageUrl;
    }

    /**
     * @return mixed
     */
    public function perPage(): mixed
    {
        return $this->perPage;
    }

    /**
     * @return mixed
     */
    public function currentPage(): mixed
    {
        return $this->currentPage;
    }

    /**
     * @return mixed
     */
    public function totalItems(): mixed
    {
        return $this->totalItems;
    }

    /**
     * @return mixed
     */
    public function totalPages(): mixed
    {
        return $this->totalPages;
    }

    /**
     * @return mixed
     */
    public function data(): mixed
    {
        return $this->data;
    }

    /**
     * @return mixed
     */
    public function countDataViewed()
    {
        return $this->countDataViewed;
    }

    /**
     * @return mixed
     */
    public function path(): mixed
    {
        return $this->path;
    }

    public function url($page)
    {
        return $this->path.'?page='.$page;
    }
}
