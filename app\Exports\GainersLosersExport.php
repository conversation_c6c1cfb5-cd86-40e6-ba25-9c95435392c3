<?php

namespace App\Exports;

use App\Http\Controllers\Admin\GainersLosersReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class GainersLosersExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(GainersLosersReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Username',
            'Login',
            'Currency',
            'Platform',
            'Group',
            'Profit',
        ];
    }

    public function map($row): array
    {
        return [
            $row->mtAccount?->application?->mainUser?->full_name ?? 'N\A',
            $row->login ?? 'N\A',
            $row->mtAccount?->mtGroup?->currency?->code ?? 'N\A',
            $row->mtAccount->mtGroup?->tradingPlatform?->name ?? 'N\A',
            $row->mtAccount?->mtGroup?->name ?? 'N\A',
            $row->profit ?? 'N\A',
        ];
    }
}
