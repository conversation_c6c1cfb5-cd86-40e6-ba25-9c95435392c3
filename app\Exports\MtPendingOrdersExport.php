<?php

namespace App\Exports;

use App\Enums\MetaTrader4Enum;
use App\Enums\MetaTrader5Enum;
use App\Http\Controllers\Admin\MtPendingOrdersController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtPendingOrdersExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private array $actions;

    private array $reasons;

    private string $platform;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->actions = [
            'mt4' => MetaTrader4Enum::DEAL_ACTION,
            'mt5' => MetaTrader5Enum::ORDER_TYPES,
        ];

        $this->reasons = [
            'mt4' => MetaTrader4Enum::REASON,
            'mt5' => MetaTrader5Enum::REASON,
        ];

        $this->platform = $this->requestData['trading_platform'] == 1 ? 'mt4' : 'mt5';
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(MtPendingOrdersController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'login',
            'platform',
            'volume',
            'ticket',
            'symbol',
            'price',
            'time',
            'take_profit',
            'stop_loss',
            'reason',
            'action'
        ];
    }

    public function map($order): array
    {
        return [
            $order->login,
            $this->platform,
            $order->volume,
            $order->ticket,
            $order->symbol,
            $order->price,
            $order->time,
            $order->tp_price,
            $order->sl_price,
            $this->reasons[$this->platform][$order->reason] ?? '-',
            $this->actions[$this->platform][$order->action] ?? '-',
        ];
    }
}
