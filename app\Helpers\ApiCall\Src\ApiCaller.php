<?php

namespace App\Helpers\ApiCall\Src;

use App\Helpers\ApiCall\Src\CallersTypes\CallerTypesFactory;
use Illuminate\Support\Facades\Http;
use Exception;

class ApiCaller
{
    protected static ?self $instance = null;

    protected $request;

    protected $uri;

    private function __construct()
    {
        $this->request = $this->createRequest();
        $this->uri = Config::DOMAIN.'/'.Config::API_URI;
    }

    public static function getInstance(): self
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function getRequest(): Http|\Illuminate\Http\Client\PendingRequest
    {
        return $this->request;
    }

    /**
     * @throws Exception
     */
    public function create($path, $method): CallersTypes\DELETE|CallersTypes\GET|CallersTypes\POST|CallersTypes\PUT
    {
        $url = $this->uri.'/'.trim($path, '/');
        $caller = CallerTypesFactory::create($method, $url);

        return $caller;
    }

    protected function createRequest(): Http|\Illuminate\Http\Client\PendingRequest
    {
        return is_local_env() ? Http::withoutVerifying() : new Http();
    }

    private function __clone()
    {
    }
}
