<?php

namespace App\Exports;

use App\Http\Controllers\Admin\AppOfferController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AppOfferExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(AppOfferController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'APP.ID',
            'USERNAME',
            'CURRENCY',
            'OFFER SCENARIO',
            'REGISTERED USERS',
            'COMMISSION MODEL',
            'DEDUCT SCALPING',
            'ACCOUNT MANAGER',
            'CREATED BY',
            'CREATED AT'
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId() ?? '-',
            $row->application?->mainUser?->full_name ?? '',
            $row->currency_code ?? '-',
            $row->scenario_groups ?? '-',
            trans_choice('content.clients_number', $row->registeredUsers()),
            isset($row::$COMMISSION_MODELS[$row->commission_model]) ? trans('content.' . $row::$COMMISSION_MODELS[$row->commission_model]) : null,
            $row->deduct_scalping ? __('content.yes') : __('content.no'),
            $row->application->getAccountManager()?->mainUser?->full_name ?? '-',
            $row->createdBy?->getUserHolderName() ?? '-' ,
            $row->created_at ?? '-'
        ];
    }
}
