<?php

namespace App\Enums;

enum MetaTrader4Enum: string
{
    public const OPENED_TRADE_DATE = '1970-01-01 00:00:00';

    public const LOT_SIZE = 100;

    public const DEAL_ACTION = [
        '0' => 'buy',
        '1' => 'sell',
        '2' => 'buy limit',
        '3' => 'sell limit',
        '4' => 'buy stop',
        '5' => 'sell Stop',
        '6' => 'balance',
        '7' => 'credit',
    ];

    public const TRADE_DEAL_ACTION = [
        '0' => 'buy',
        '1' => 'sell',
        '2' => 'buy limit',
        '3' => 'sell limit',
        '4' => 'buy stop',
        '5' => 'sell Stop',
    ];

    public const REASON = [
        '0' => 'client',
        '1' => 'expert',
        '2' => 'dealer',
        '3' => 'signal',
        '4' => 'gateway',
        '5' => 'mobile',
        '6' => 'wb',
        '7' => 'api',
    ];
}
