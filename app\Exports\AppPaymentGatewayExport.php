<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use App\Http\Controllers\Admin\AppPaymentGatewayController;

class AppPaymentGatewayExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(AppPaymentGatewayController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID Application',
            'Username',
            'Payment Gateway',
            'Account Number',
            'Created At',
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId() ?? '-',
            $row->application->mainUser?->full_name ?? '-',
            $row->paymentGateway->name ?? '-',
            $row->account_number ?? '-',
            $row->created_at ?? '-',
        ];
    }
}
