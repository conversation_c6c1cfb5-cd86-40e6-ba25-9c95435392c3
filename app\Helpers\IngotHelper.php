<?php

namespace App\Helpers;

use App\Models\MtAccount;
use App\Models\Role;
use App\Models\User;
use App\Models\MtGroup;
use App\Models\AppOffer;
use App\Models\Referral;
use App\Models\Application;
use App\Models\CampaignLead;
use App\Models\MtGroupShared;
use App\Models\TrackAffiliateLink;
use App\Models\Cellxpert\Affiliate;
use App\Models\AffiliateLinkKeyword;
use App\Models\AppOfferScenarioGroup;
use Illuminate\Support\Facades\Cookie;
use App\Models\Cellxpert\Registration as CellxpertRegistration;

class IngotHelper
{
    public static function generateRegistrationUrl($app_type, $token = null)
    {
        if (empty($token)) {
            $token = Cookie::get('reg_referral') ?? null;

            // Get the cookies which have been queued for the next request
            if (Cookie::hasQueued('reg_referral')) {
                $queued = Cookie::getQueuedCookies('reg_referral');
                $token = $queued[0];
                $token = $token->getValue();
            }
        }

        $token = urlencode($token);

        $queryParams = request()->query();

        return route('open-account', ['app_type' => $app_type, 'token' => $token , ...$queryParams]);
    }

    public static function detectReferralSource($token = null, $source = null)
    {
        $referral_value = [];

        if (!empty($token)) {
            $referral_list = Referral::query()->pluck('id', 'ref_code')->toArray();
            // decrypt token to check if contains ib groups
            $referral_groups = self::decryptReferralToken($token);

            # decrypt token to check if contains app offer
            $referral_app_offer = self::decryptReferralTokenAppOffer($token);

            $sales_id = request()->get('sap_id');

            // if come from agent referral link
            if (is_array($referral_groups) && in_array('group_ids', array_keys($referral_groups))) {
                $mt_group_obj = MtGroup::query()
                    ->whereIn('id', $referral_groups['group_ids'])
                    ->with([
                        'mtAccount.mtGroup:id,account_type',
                        'oldMtGroupForward',
                    ])
                    ->first();

                // 404 page if the group inactive
                abort_if(!$mt_group_obj?->status?->value, 404);

                if ($mt_group_obj) {
                    // if group forward, overwrite the group
                    if ($mt_group_obj->oldMtGroupForward) {
                        $mt_group_obj = $mt_group_obj->oldMtGroupForward->newMtGroup()->with([
                            'mtAccount.mtGroup:id,account_type'
                        ])->first();
                    }

                    if (!app()->runningInConsole()) {
                        $site_id = get_site_id();
                        if ($site_id != $mt_group_obj->affiliate_website_id) {
                            // Load the affiliate website if it's missing
                            $mt_group_obj->loadMissing('affiliateWebsite:id,name');
                            // Get the current URL path without the hostname
                            $path = parse_url(request()->fullUrl(), PHP_URL_PATH);
                            $redirect_url = http_host_name($mt_group_obj->affiliateWebsite?->name, $path);

                            return [
                                'redirect' => true,
                                'redirect_url' => $redirect_url,
                            ];
                        }
                    }

                    if ($mt_group_obj->mtAccount) {
                        $parent_user_obj_type = $mt_group_obj->mtAccount->mtGroup->account_type;
                        if (in_array($parent_user_obj_type, ['agent', 'cash'])) {
                            $application_id = $mt_group_obj->mtAccount->application_id;
                            $referral_value = [
                                'referral' => $referral_list['ib'],
                                'referral_value' => $token,
                                'referral_type' => 'agent',
                                'group_ids' => (array) $mt_group_obj->id,
                                'parent_app_id' => $application_id,
                            ];

                            self::setTrackLinkView($application_id, $mt_group_obj->id);
                        }
                    }
                }
            } elseif (is_array($referral_groups) && in_array('mt_group_shared_id', array_keys($referral_groups))) {
                $shared_group = MtGroupShared::query()
                ->where('id', $referral_groups['mt_group_shared_id'])
                ->with([
                    'mtAccount:id,application_id',
                    'mtGroup:id,status',
                ])
                ->first();

                // 404 page if the group inactive
                abort_if(!$shared_group?->mtGroup?->status?->value, 404);

                if ($shared_group && $shared_group?->mtAccount?->application_id) {
                    $referral_value = [
                        'referral' => $referral_list['ib'],
                        'referral_value' => $token,
                        'referral_type' => 'agent',
                        'mt_group_shared_id' => $referral_groups['mt_group_shared_id'],
                        'parent_app_id' => $shared_group?->mtAccount?->application_id,
                    ];
                }
            } elseif (is_array($referral_groups) && in_array('app_id', array_keys($referral_groups))) {
                $referral_value = [
                    'referral' => $referral_list['ib'],
                    'referral_value' => $token,
                    'referral_type' => 'agent',
                    'app_id' => $referral_groups['app_id'],
                    'parent_app_id' => $referral_groups['app_id'],
                ];
                self::setTrackLinkView($referral_groups['app_id']);
            } elseif (is_string($referral_groups) && str_contains($referral_groups, 'INGOT-')) {
                $app_id = (int) abs(filter_var($referral_groups, FILTER_SANITIZE_NUMBER_INT));
                $referral_value = [
                    'referral' => $referral_list['ib'],
                    'referral_value' => $token,
                    'referral_type' => 'agent',
                    'app_id' => $app_id,
                    'parent_app_id' => $app_id,
                ];
            } elseif (is_array($referral_groups) && in_array('agent_number', array_keys($referral_groups))) {
                $mt_account = MtAccount::query()
                    ->where('login', $referral_groups['agent_number'])
                    ->first();

                $referral_value = [
                    'referral' => $referral_list['agent'],
                    'referral_value' => $token,
                    'referral_type' => 'agent',
                    'app_id' => $mt_account?->application_id,
                    'parent_app_id' => $mt_account?->application_id,
                ];
            } elseif (is_array($referral_app_offer)) {
                # check if app offer
                $referral_value = [
                    'referral' => $referral_list['ib'],
                    'referral_value' => $token,
                    'referral_type' => 'agent',
                    'parent_app_id' => $referral_app_offer['parent_app_id'],
                    'app_offer_id' => $referral_app_offer['app_offer_id'] ?? null,
                    'app_offer_scenario_group_id' => $referral_app_offer['app_offer_scenario_group_id'] ?? null,
                ];
            } elseif (array_key_exists($token, $referral_list)) {
                # check if campaign
                $referral_value = http_build_query(array_filter(request()->all()));

                if (str_contains($referral_value, 'utm_')) {
                    $referral_value = http_build_query(array_filter(request()->all(), function ($key) {
                        return strpos($key, 'utm_') === 0;
                    }, ARRAY_FILTER_USE_KEY));
                }

                $referral_value = [
                    'referral' => $referral_list[$token],
                    'referral_type' => $token,
                    'referral_value' => $referral_value ?? null,
                ];
            } elseif (request()->hasAny(['cxd', 'bta']) || str($token)->contains('cxd=') || str($token)->contains('bta=')) {
                $cx_affiliate_id = CellxpertRegistration::getAffiliateIdFromCXD($token);
                if ($cx_affiliate_id) {
                    $cx_affiliate_id = Affiliate::query()->where('affiliate_id', $cx_affiliate_id)->pluck('user_id')->first();
                    if ($cx_affiliate_id) {
                        $affiliate_app = Application::query()->where('id', $cx_affiliate_id)
                            ->withAggregate('appType', 'name')
                            ->with(['mtGroupShareds'])
                            ->first();

                        if ($affiliate_app) {
                            $referral_type = $affiliate_app->app_type_name;
                            $referral = $referral_list[$referral_type] ?? null;
                            $mt_group_shared_id = $affiliate_app->mtGroupShareds?->pluck('id')->toArray() ?? [];

                            $referral_value = [
                                'referral' => $referral,
                                'referral_value' => $token,
                                'referral_type' => $referral_type,
                                'parent_app_id' => $cx_affiliate_id,
                            ];

                            if ($mt_group_shared_id) {
                                $referral_value += ['mt_group_shared_id' => $mt_group_shared_id];
                            }
                        }
                    }
                }
            } elseif (is_array($referral_groups) && in_array('lead_id', array_keys($referral_groups))) {
                $lead_id = $referral_groups['lead_id'];
                $lead = CampaignLead::query()->where('id', $lead_id)->first();

                if ($lead && empty($lead_id->user_id)) {
                    $referral_value = [
                        'referral' => $referral_list['campaign'],
                        'referral_value' => $token,
                        'referral_type' => 'campaign',
                        'account_manager' => $lead->account_manager,
                        'lead_id' => $lead_id,
                    ];
                }
            } else {
                // if referral
                $parent_user_obj_obj = Application::query()->where('referral_hash', $token)->first();
                if ($parent_user_obj_obj) {
                    $referral_value = [
                        'referral' => $referral_list['referral'],
                        'referral_value' => $token,
                        'referral_type' => 'referral',
                        'parent_app_id' => $parent_user_obj_obj->id,
                    ];
                    self::setTrackLinkView($parent_user_obj_obj->id);
                } else {
                    // keyword
                    $keyword_account_obj = AffiliateLinkKeyword::query()->where('keyword', $token)->first();
                    if ($keyword_account_obj) {
                        $application_id = $keyword_account_obj->application_id;
                        $mt_group_id = $keyword_account_obj->group_id;
                        $referral_value = [
                            'referral' => $referral_list['referral'],
                            'referral_value' => $token,
                            'referral_type' => 'referral',
                            'parent_app_id' => $application_id,
                        ];
                        if ($mt_group_id) {
                            $referral_value['group_ids'] = (array) $mt_group_id;
                        }
                        self::setTrackLinkView($application_id);
                    }
                }
            }

            if ($source) {
                $referral_value['referral_value_id'] = $source;
            }

            if (!$referral_value) {
                $referral_value = [
                    'referral' => $referral_list['other'],
                    'referral_value' => $token,
                    'referral_type' => 'other',
                ];
            }

            if ($sales_id) {
                session()->put('sales_app_id', $sales_id);
            }
        }

        return $referral_value;
    }

    public static function decryptReferralToken($token)
    {
        $decrypted_token = base64_decode($token);
        $decrypted_token_arr = json_decode($decrypted_token, true);

        if ((is_array($decrypted_token_arr) && in_array('group_ids', array_keys($decrypted_token_arr)))
            || (is_array($decrypted_token_arr) && in_array('app_id', array_keys($decrypted_token_arr)))
            || (is_array($decrypted_token_arr) && in_array('mt_group_shared_id', array_keys($decrypted_token_arr)))
            || (is_array($decrypted_token_arr) && in_array('lead_id', array_keys($decrypted_token_arr)))
            || (is_array($decrypted_token_arr) && in_array('sap_id', array_keys($decrypted_token_arr)))
            || (is_array($decrypted_token_arr) && in_array('agent_number', array_keys($decrypted_token_arr)))
        ) {
            return $decrypted_token_arr;
        } else {
            return $token;
        }
    }

    public static function decryptReferralTokenAppOffer($token)
    {
        $decrypted_token = base64_decode($token);
        $decrypted_token_arr = json_decode($decrypted_token, true);

        if (is_array($decrypted_token_arr) && in_array('app_offer_scenario_group_id', array_keys($decrypted_token_arr))) {
            # if multiple link , app_offer_scenario_group_id
            $app_offer_scenario_group = AppOfferScenarioGroup::query()->whereHas('appOffer', function ($offer) {
                $offer->where('status', AppOffer::STATUS_APPROVED);
            })->where('id', $decrypted_token_arr['app_offer_scenario_group_id'])->first();

            return [
                'parent_app_id' => $app_offer_scenario_group?->appOffer?->application_id,
                'app_offer_scenario_group_id' => $decrypted_token_arr['app_offer_scenario_group_id'],
            ];
        } elseif (is_array($decrypted_token_arr) && in_array('app_offer_id', array_keys($decrypted_token_arr))) {
            # if single link , app_offer_id
            $app_offer = AppOffer::query()->where('status', AppOffer::STATUS_APPROVED)->where('id', $decrypted_token_arr['app_offer_id'])->first();

            return [
                'parent_app_id' => $app_offer?->application_id,
                'app_offer_id' => $decrypted_token_arr['app_offer_id'],
            ];
        } else {
            return $token;
        }
    }

    public static function roadMap()
    {
        $user = auth()->user();

        $mapItems = [
            '0' => [
                'title' => trans('content.fill_the_short_form'),
                'href' => '',
                'icon' => 'la-id-card',
                'isDone' => true,
            ],
            '4' => [
                'title' => trans('content.verify_your_profile'),
                'href' => route('verification'),
                'icon' => 'la-shield-alt',
                'isDone' => $user->can('approved-account') || $user->can('completed-account') || $user->can('pre-approved-account'),
            ],
            '1' => [
                'title' => trans('content.open_a_trading_account'),
                'href' => route('real-account'),
                'icon' => 'la-chalkboard',
                'isDone' => $user->can('has-real-mt-account'),
            ],
            '2' => [
                'title' => trans('content.deposit_fund'),
                'href' => route('deposit-fund'),
                'icon' => 'la-donate',
                'isDone' => $user->can('is-deposit'),
            ],
            '3' => [
                'title' => trans('content.start_trading'),
                'href' => '#',
                'icon' => 'la-map-marked-alt',
                'isDone' => false,
            ],
        ];

        return [
            'items' => $mapItems,
        ];
    }

    private static function setTrackLinkView($application_id, $group_id = null)
    {
        $track = TrackAffiliateLink::query()->where([
            'ip_address' => ip_address(),
            'application_id' => $application_id,
            'mt_group_id' => $group_id,
        ])->where(function ($d) {
            $d->where('created_at', '>=', date('Y-m-d'))->where('created_at', '<', date('Y-m-d', strtotime('tomorrow')));
        })->first();

        if ($track) {
            $track->increment('count');
        } else {
            TrackAffiliateLink::query()->create([
                'ip_address' => ip_address(),
                'application_id' => $application_id,
                'mt_group_id' => $group_id,
                'count' => 1,
            ]);
        }
    }

    public static function checkAccountManagerFilter($account_manager_request)
    {
        if (in_array('retail', $account_manager_request)) {
            $retail_role_id = Role::query()->where('name', 'acquisition-telesales')->value('id');
            if ($retail_role_id) {
                $retails = User::query()->where('role_id', $retail_role_id)
                    ->whereHas('application', function ($a) {
                        $a->active();
                    })->pluck('application_id')->toArray();

                return array_merge($account_manager_request, $retails);
            }
        }

        return $account_manager_request;
    }
}
