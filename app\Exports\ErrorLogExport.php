<?php

namespace App\Exports;

use App\Http\Controllers\Admin\ErrorLogController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ErrorLogExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(ErrorLogController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Model',
            'Error',
            'Ip Address',
            'Status',
            'Url',
            'Request',
            'Comment',
            'Created Date',
        ];
    }

    public function map($data): array
    {
        return [
            $data?->user?->full_name ?? '-',
            $data->table_name ?? '-',
            $data->error ?? '-',
            $data->ip_address ?? '-',
            $data->status ?? '-',
            $data->url ?? '-',
            $data->request ?? '-',
            $data->comment ?? '-',
            $data->created_at ?? '-'
        ];
    }
}
