<?php

namespace App\Exports;

use App\Http\Controllers\Admin\SeminarLeadController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class SeminarLeadsExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(SeminarLeadController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Email',
            'Mobile',
            'Country',
            'Attend',
            'Source',
            'Account Manager',
            'Seminar',
            'Date',
        ];
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->first_name . ' ' . $row->last_name ?? null,
            $row->email ?? '-',
            $row->mobile ?? '-',
            $row->country_name ?? '-',
            $row->attend ? trans('content.yes') : trans('content.no'),
            $row->source ?? '-',
            $row->inCharge?->mainUser?->getUserHolderName() ?? '-',
            $row->seminar_name ?? '-',
            $row->created_at ?? '-',
        ];
    }
}
