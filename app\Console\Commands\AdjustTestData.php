<?php

namespace App\Console\Commands;

use App\Models\Role;
use App\Models\User;
use App\Models\Contact;
use App\Models\UserOtp;
use App\Models\MtAccount;
use App\Models\SeminarUser;
use App\Models\WebinarUser;
use App\Models\CampaignLead;
use App\Models\User2FADevice;
use App\Models\ContactRequest;
use Illuminate\Console\Command;
use App\Models\AffiliateWebsite;
use App\Models\WebinarGuestUser;
use Illuminate\Support\Facades\DB;
use App\Models\PaymentGatewayConfig;
use Illuminate\Support\Facades\Hash;
use App\Models\AffiliateWebsiteConfig;

class AdjustTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'adjust:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Change user into test ones';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if (!is_local_env()) {
            $this->error('This command is only for local env');

            return;
        }

        $choices = [
            'Adjust clients emails & passwords', // 0
            'Adjust employees emails & passwords', // 1
            'Adjust affiliate websites to local', // 2
            'Truncate all MetaTrader Accounts', // 3
            'Truncate all critical data', // 4
            'Truncate All Users OTP', // 5
            'Stop AML software (IDwise & Sumsub)', // 6
            'Empty', // 7
            'Adjust email receiver', // 8
            'Stop Live Configurations', // 9
        ];

        $mode = $this->choice('What are you gonna do?', $choices, 0);

        if ($mode == $choices[0]) {
            $domain = $this->ask('Enter the domain', 'client.local');
            $password = $this->ask('Enter the application passwords', '123');

            $email = DB::raw('CONCAT(application_id, "@' . $domain . '")');

            User::query()->whereHas('role', function ($query) {
                $query->whereIn('name', Role::$client_roles);
            })
                ->whereNot('email', $email)
                ->update([
                    'password' => Hash::make($password),
                    'email' => $email,
                    'phone' => DB::raw('CONCAT("123-", LPAD(FLOOR(RAND() * ***********), 10, 0))'),
                ]);
        } elseif ($mode == $choices[1]) {
            $domain = $this->ask('Enter the domain', 'employee.local');
            $password = $this->ask('Enter the application passwords', '123');

            $email = DB::raw('CONCAT(application_id, "@' . $domain . '")');

            User::query()->whereHas('role', function ($query) {
                $query->whereNotIn('name', Role::$client_roles);
            })
                ->whereNot('email', $email)
                ->update([
                    'password' => Hash::make($password),
                    'email' => $email,
                    'phone' => DB::raw('CONCAT("123-", LPAD(FLOOR(RAND() * ***********), 10, 0))'),
                ]);
        } elseif ($mode == $choices[2]) {
            AffiliateWebsite::query()->where(DB::raw('SUBSTRING(name, -6, 6)'), '!=', '.local')->update([
                'name' => DB::raw('CONCAT(name,".local")'),
            ]);
        } elseif ($mode == $choices[3]) {
            MtAccount::query()->truncate();
        } elseif ($mode == $choices[4]) {
            CampaignLead::query()->truncate();
            Contact::query()->truncate();
            ContactRequest::query()->truncate();
            WebinarUser::query()->truncate();
            WebinarGuestUser::query()->truncate();
            SeminarUser::query()->truncate();
            PaymentGatewayConfig::query()->update([
                'value' => 'XXXX',
            ]);
            AffiliateWebsiteConfig::query()->where('key', 'mail_password')->update([
                'value' => 'XXXX',
            ]);
        } elseif ($mode == $choices[5]) {
            UserOtp::truncate();
            User2FADevice::truncate();
        } elseif ($mode == $choices[6]) {
            AffiliateWebsiteConfig::query()->whereIn('key', [
                'id_wise', 'sumsub'
            ])->update([
                'value' => 'false',
            ]);
        } elseif ($mode == $choices[7]) {
        } elseif ($mode == $choices[8]) {
            $email = $this->ask('Enter Receiver email', '<EMAIL>');
            $name = $this->ask('Enter Receiver name', 'IT Dev. Test');

            $affWebsites = AffiliateWebsite::query()->pluck('name', 'id')->toArray();

            foreach ($affWebsites as $siteId => $siteName) {
                $config = AffiliateWebsiteConfig::firstOrNew([
                    'affiliate_website_id' => $siteId,
                    'key' => 'mail_to_address',
                ]);
                $config->value = $email;
                $config->save();

                $config2 = AffiliateWebsiteConfig::firstOrNew([
                    'affiliate_website_id' => $siteId,
                    'key' => 'mail_to_name',
                ]);
                $config2->value = $name;
                $config2->save();

                $this->info("Sender email was set for the website: $siteName");
            }
        } elseif ($mode == $choices[9]) {
            AffiliateWebsiteConfig::where('key', 'captcha_enabled')->update([
                'value' => false,
            ]);

            AffiliateWebsiteConfig::query()->where('key', 'has_onboarding_otp')->update([
                'value' => false,
            ]);
        }
    }
}
