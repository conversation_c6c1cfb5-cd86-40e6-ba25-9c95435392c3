<?php

namespace App\Helpers\ApiCall\Src\CallersTypes;

use App\Helpers\ApiCall\Src\ApiCaller;
use App\Helpers\ApiCall\Src\Response\Response;

class GET extends Caller
{
    protected $params;

    public function call(): Response
    {
        $request = ApiCaller::getInstance()->getRequest();
        $res = $request->withHeaders($this->headers)->get($this->url, $this->params);

        return new Response($res);
    }

    /**
     * @param  mixed  $body
     */
    public function setParams($params): static
    {
        $this->params = $params;

        return $this;
    }
}
