<?php

namespace App\Console\Commands;

use App\Models\Application;
use App\Models\AppParentRelation;
use App\Models\AppRelation;
use Illuminate\Console\Command;

class SyncAppRelation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:app-relation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Resync App Relation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $application_id = $this->ask('Please enter the application ID or keep it empty to sync all');

        ini_set('memory_limit', '-1');

        $app_parents = AppRelation::select('parent_app_id', 'closer_app_id', 'follower_app_id');

        if ($application_id) {
            AppParentRelation::where('parent_app_id', $application_id)->delete();

            $app_parents->where(function ($r) use ($application_id) {
                $r->where('parent_app_id', $application_id)->orWhere('closer_app_id', $application_id)->orWhere('follower_app_id', $application_id);
            });
        } else {
            AppParentRelation::query()->where('id', '>', 0)->forceDelete();
        }

        $app_parents = $app_parents->get();

        $app_parents = array_unique(array_filter(collect($app_parents->toArray())->flatten()->all()));

        foreach ($app_parents as $parent_app_id) {
            $this->info("Reading user Id: $parent_app_id");

            $children_apps = Application::getRelationAccounts($parent_app_id);

            if (in_array($parent_app_id, $children_apps)) {
                $key = array_key_index($parent_app_id, $children_apps);

                if ($key || $key == 0) {
                    unset($children_apps[$key]);
                }
            }

            if (count($children_apps) > 0) {
                $children_apps = json_encode(array_values($children_apps));

                AppParentRelation::updateOrCreate([
                    'parent_app_id' => $parent_app_id,
                ], [
                    'child_app_ids' => $children_apps,
                ]);
            }
        }
    }
}
