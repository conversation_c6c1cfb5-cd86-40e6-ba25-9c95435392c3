<?php

namespace App\Exports;

use App\Http\Controllers\Admin\OrderController;
use App\Models\Order;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class OrderExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private $statuses;

    private $user;

    private $is_cs_kpi_audit;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->statuses = Order::$statuses;
        $this->user = auth()->user();
        $this->is_cs_kpi_audit = $this->user->can('is-cs-kpi-audit');
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(OrderController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        $headings = [
            'ID Application',
            'Name',
            'Email',
            'Country',
            'Order',
            'Account Manager',
            'Entity',
            'App Status',
            'Executed By',
            'Execution Time',
        ];

        if ($this->is_cs_kpi_audit) {
            $headings[] = 'Within CS KPIs';
        }

        $headings = [...$headings, 'Created Date', 'Last Update', 'Status'];

        if (isset($this->requestData['include_comments'])) {
            $headings[] = 'Comment';
        }

        return $headings;
    }

    public function map($row): array
    {
        $execution_time = null;
        if (!in_array($this->statuses[$row->status], ['pending'])) {
            $execution_time = $row->getExecutionTime();
        }

        $data = [
            $row->application?->appIdentifierId() ?? '-',
            $row->user->full_name ?? '-',
            $row->user->email ?? '-',
            $row->user->country->name ?? '-',
            $row->orderType->name ?? '-',
            $row->application?->getAppAccountManagerName() ?? __('content.not_assigned'),
            $row->application?->getRegulatedEntity() ?? '-',
            $row->application->getStatus() ?? '-',
            $row->executedBy?->full_name ?? '-',
            $execution_time ?? '-',
        ];

        if ($this->is_cs_kpi_audit) {
            $data[] = is_null($row->satisfyingKpiCS()) ? '-' : ($row->satisfyingKpiCS() ? __('content.yes') : __('content.no'));
        }

        $data = [
            ...$data,
            $row->created_at,
            $row->updated_at,
            $this->statuses[$row->status],
        ];

        if (isset($this->requestData['include_comments'])) {
            $data[] = rip_tags($row->comment) ?? '-';
        }

        return $data;
    }
}
