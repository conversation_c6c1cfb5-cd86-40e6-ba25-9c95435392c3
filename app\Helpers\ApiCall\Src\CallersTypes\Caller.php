<?php

namespace App\Helpers\ApiCall\Src\CallersTypes;

use App\Helpers\ApiCall\Src\Response\Response;

abstract class Caller
{
    protected $url;

    protected array $headers = [];

    public function __construct($url)
    {
        $this->url = $url;
    }

    abstract public function call(): Response;

    public function addHeader($key, $value): static
    {
        $this->headers[$key] = $value;

        return $this;
    }

    /**
     * @param  mixed  $urlParams
     */
    public function setUrlParams($key, $value): static
    {
        $key = ':'.trim($key, ':');
        $this->url = str_replace($key, $value, $this->url);

        return $this;
    }
}
