<?php

namespace App\Exports;

use App\Http\Controllers\Admin\TransactionController;
use App\Models\Transaction;
use App\Repositories\Interfaces\RoleRepositoryInterface;
use Illuminate\Bus\Queueable;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class TransactionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;
    use Queueable;

    private $statuses;

    private $user;

    private $can_access_payment_kpi = false;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->statuses = Transaction::$statuses;
        $this->user = auth()->user();

        $this->can_access_payment_kpi = $this->user->can('is-payment');
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(TransactionController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        $titles = [
            'Reference ID',
            'ID Application',
            'Name',
            'Country',
            'Type',
            'Req. Amount',
            'Rec. Amount',
            'USD Amount',
            'Payment Gateway',
            'Entity',
            'TxHash',
            'Executed By',
            'Executed Date',
            'Transaction Account Manager',
            'Lead Acquisition Manager',
            'Lead Retention Manager',
            'Status',
            'Bank/Exchange',
            'MetaTrader Sender',
            'MetaTrader Receiver',
            'Ex. Rate',
            'Proceed By',
            'Created By',
            'Created Date',
            'Updated On',
        ];

        if (isset($this->requestData['include_comments'])) {
            $titles[] = 'Comment';
        }

        if ($this->can_access_payment_kpi) {
            $titles[] = 'Payment Execution Time';
            $titles[] = 'Hedging Execution Time';
            $titles[] = 'Junior Execution Time';
            $titles[] = 'Senior Execution Time';
        }

        return $titles;
    }

    public function map($row): array
    {
        $data = [
            $row->id,
            $row->application->appIdentifierId(),
            $row->user->full_name,
            $row->user->country->name ?? '-',
            $row->transactionType->name ?? '-',
            $row->req_amount ? ($row->req_amount . ' ' . ($row->reqCurrency?->code ?? '')) : '-',
            $row->rec_amount ? ($row->rec_amount . ' ' . ($row->recCurrency?->code ?? '')) : '-',
            $row->usd_amount ?? '-',
            $row->source ? ($row->paymentGateway->name ?? '-') : 'internal',
            $row->application->getRegulatedEntity() ?? '-',
            $row->extra_transaction_id ?? $row->order_id,
            $row->executedBy ? ($row->executedBy->first_name . ' ' . $row->executedBy->last_name) : '-',
            $row->executed_at ? $row->executed_at : '-',
            $row->accountManager->mainUser->full_name ?? __('content.not_assigned'),
            $row->application->getAppAcquisitionManagerName() ?? __('content.not_assigned'),
            $row->application->getAppRetentionManagerName() ?? __('content.not_assigned'),
            $this->statuses[$row->status] ?? '-',
            $row->siteBank->beneficiary_bank ?? '-',
            $row->senderMtAccount->login ?? '-',
            $row->recMtAccount->login ?? '-',
            $row->exchange_rate ?? '-',
            $row->proceedBy ? ($row->proceedBy->first_name . ' ' . $row->proceedBy->last_name) : '-',
            $row->created_by() ?? '-',
            $row->created_at,
            $row->updated_at,
        ];

        if (isset($this->requestData['include_comments'])) {
            $data[] = rip_tags($row->comment);
        }

        if ($this->can_access_payment_kpi) {
            $data[] = $row->paymentTimeFrame() ?? trans('content.not_assigned_to_payment');

            $paymentRoles = resolve(RoleRepositoryInterface::class)->getAllPaymentRoles();

            $transactionKpi = $row->kpiPayment($paymentRoles);

            foreach ($transactionKpi as $kpi) {
                $data[] = $kpi;
            }
        }

        return $data;
    }
}
