<?php

$models_config = [
    0 => [
        'permission' => 'affiliate_website',
        'table' => 'affiliate_websites',
        'controller' => 'AffiliateWebsite',
        'url' => 'affiliate-websites',
        'description' => 'affiliate websites that be able to connect to the website and CRM',
        'has_crud' => true,
    ],
    1 => [
        'permission' => 'application',
        'table' => 'applications',
        'controller' => 'Application',
        'url' => 'applications',
        'description' => 'client applications data',
        'has_crud' => true,
    ],
    2 => [
        'permission' => 'app_payment_bank',
        'table' => 'app_payment_banks',
        'controller' => 'AppPaymentBank',
        'url' => 'app-payment-banks',
        'description' => 'client financial information that required a banking details',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    3 => [
        'permission' => 'app_payment_gateway',
        'table' => 'app_payment_gateways',
        'controller' => 'AppPaymentGateway',
        'url' => 'app-payment-gateways',
        'description' => 'client financial information that required a payment gateway details but not a banking details',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    4 => [
        'permission' => 'app_type',
        'table' => 'app_types',
        'controller' => 'AppType',
        'url' => 'app-types',
        'description' => 'application types for the client to create an application',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    5 => [
        'permission' => 'app_wallet',
        'table' => 'app_wallets',
        'controller' => 'AppWallet',
        'url' => 'app-wallets',
        'description' => 'user E-Wallets and their balances and currencies',
        'has_crud' => true,
    ],
    6 => [
        'permission' => 'asic_answer',
        'table' => 'asic_answers',
        'controller' => 'AsicAnswer',
        'url' => 'asic-answers',
        'description' => 'answers for ASIC regulation questions',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    7 => [
        'permission' => 'asic_question',
        'table' => 'asic_questions',
        'controller' => 'AsicQuestion',
        'url' => 'asic-questions',
        'description' => 'questions to be answered by the client for the client eligibility',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    8 => [
        'permission' => 'company',
        'table' => 'companies',
        'controller' => 'Company',
        'url' => 'companies',
        'description' => 'companies data related to the corporate applications',
        'has_crud' => true,
    ],
    9 => [
        'permission' => 'company_holder',
        'table' => 'company_holders_owners',
        'controller' => 'CompanyHoldersOwner',
        'url' => 'company-holders',
        'description' => 'company holders and owners data related to the corporate applications',
        'has_crud' => true,
    ],
    11 => [
        'permission' => 'contact_request',
        'table' => 'contact_requests',
        'controller' => 'ContactRequest',
        'url' => 'contact-requests',
        'description' => 'leads that requested to be contacted by the company via contact us form',
        'has_crud' => true,
        'other_permissions' => [
            'view-assign' => [
                'name' => 'view-assign',
                'fname' => 'viewAssignContact',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'save-assign' => [
                'name' => 'save-assign',
                'fname' => 'assignContact',
                'method' => 'put',
                'permission' => 'view-assign-contact_request',
                'params' => ['id'],
            ],
        ],
    ],
    12 => [
        'permission' => 'country',
        'table' => 'countries',
        'controller' => 'Country',
        'url' => 'countries',
        'description' => 'countries available for the clients to select country of residence and nationality',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    13 => [
        'permission' => 'currency',
        'table' => 'currencies',
        'controller' => 'Currency',
        'url' => 'currencies',
        'description' => 'currencies available for the clients to select currency of their wallet and transactions',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    14 => [
        'permission' => 'user_restriction',
        'table' => 'user_restrictions',
        'controller' => 'UserRestriction',
        'url' => 'user-restrictions',
        'description' => 'user restrictions for the clients to restrict their access to the CRM from a whitelisted IP address',
        'has_crud' => true,
    ],
    15 => [
        'permission' => 'mt_group',
        'table' => 'mt_groups',
        'controller' => 'MtGroup',
        'url' => 'mt-groups',
        'description' => 'MT4/MT5 groups for the clients to register on it',
        'has_crud' => true,
        'other_permissions' => [
            'summary' => [
                'name' => 'summary',
                'fname' => 'groupSummary',
                'method' => 'get',
                'permission' => 'view-mt_group',
                'params' => ['id'],
            ],
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'clone' => [
                'name' => 'clone',
                'fname' => 'clone',
                'method' => 'get',
                'permission' => 'create-mt_group',
                'params' => ['id'],
            ],
        ],
    ],
    16 => [
        'permission' => 'ib_offer',
        'table' => 'ib_offers',
        'controller' => 'IbOffer',
        'url' => 'ib-offers',
        'description' => 'IB offers attachments data for the partners',
        'has_crud' => true,
    ],
    17 => [
        'permission' => 'index_dividend',
        'table' => 'index_dividends',
        'controller' => 'IndexDividend',
        'url' => 'index-dividends',
        'description' => 'indexes and their distributed dividends date and amount',
        'has_crud' => true,
    ],
    18 => [
        'permission' => 'contract',
        'table' => 'contracts',
        'controller' => 'Contract',
        'url' => 'contracts',
        'description' => 'the extra contract specifications related to the trading platform instruments',
        'has_crud' => true,
    ],
    19 => [
        'permission' => 'mt_account',
        'table' => 'mt_accounts',
        'controller' => 'MtAccount',
        'url' => 'mt-accounts',
        'description' => 'trading/demo accounts for the clients',
        'has_crud' => true,
        'other_permissions' => [
            'hide' => [
                'name' => 'hide',
                'fname' => 'hide',
                'method' => 'post',
                'permission' => null,
                'params' => ['id'],
            ],
            'request-swap-free' => [
                'name' => 'request-swap-free',
                'fname' => 'requestSwapFree',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'store-swap-free' => [
                'name' => 'store-request-swap-free',
                'fname' => 'storeRequestSwapFree',
                'method' => 'post',
                'permission' => 'request-swap-free-mt_account',
                'params' => ['id'],
            ],
            'request-change-leverage' => [
                'name' => 'request-change-leverage',
                'fname' => 'requestChangeLeverage',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'store-change-leverage' => [
                'name' => 'store-request-change-leverage',
                'fname' => 'storeRequestChangeLeverage',
                'method' => 'post',
                'permission' => 'request-change-leverage-mt_account',
                'params' => ['id'],
            ],
        ],
    ],
    20 => [
        'permission' => 'order',
        'table' => 'orders',
        'controller' => 'Order',
        'url' => 'orders',
        'description' => 'orders history and requests data from the clients',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    22 => [
        'permission' => 'payment_gateway',
        'table' => 'payment_gateways',
        'controller' => 'PaymentGateway',
        'url' => 'payment-gateways',
        'description' => 'available payment gateways allowed for deposits and withdrawals',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    23 => [
        'permission' => 'product_category',
        'table' => 'product_categories',
        'controller' => 'ProductCategory',
        'url' => 'product-categories',
        'description' => 'available product categories allowed to trade like forex, stocks, commodities, etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    24 => [
        'permission' => 'referral',
        'table' => 'referrals',
        'controller' => 'Referral',
        'url' => 'referrals',
        'description' => 'referral sources of the clients',
        'has_crud' => true,
    ],
    25 => [
        'permission' => 'role',
        'table' => 'roles',
        'controller' => 'Role',
        'url' => 'roles',
        'description' => 'roles that defined for the system users',
        'has_crud' => true,
        'other_permissions' => [
            'clone' => [
                'name' => 'clone',
                'fname' => 'clone',
                'method' => 'get',
                'permission' => 'create-role',
                'params' => ['id'],
            ],
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    26 => [
        'permission' => 'sales_summary',
        'table' => 'reports',
        'controller' => 'SalesSummary',
        'url' => 'sales-summary',
        'description' => 'sales and performance regarding their clients, leads and transactions',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    27 => [
        'permission' => 'clients_deposit_withdrawal_report',
        'table' => 'reports',
        'controller' => 'ClientDepositWithdrawal',
        'url' => 'client-deposit-withdrawal-report',
        'description' => 'client deposit and withdrawal report shows total deposits and withdrawals for the clients.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    28 => [
        'permission' => 'swap_category',
        'table' => 'swap_categories',
        'controller' => 'SwapCategory',
        'url' => 'swap-categories',
        'description' => 'swaps categories on the website',
        'has_crud' => true,
    ],
    29 => [
        'permission' => 'swap',
        'table' => 'swaps',
        'controller' => 'Swap',
        'url' => 'swaps',
        'description' => 'instruments swaps to manager it on the website',
        'has_crud' => true,
    ],
    30 => [
        'permission' => 'temporary_fund',
        'table' => 'temporary_funds',
        'controller' => 'TemporaryFund',
        'url' => 'temporary-funds',
        'description' => 'temporary funds records for the clients when trying to do transactions',
        'has_crud' => true,
        'other_permissions' => [
            'execute' => [
                'name' => 'execute',
                'fname' => 'execute',
                'method' => 'post',
                'permission' => null,
                'params' => ['id', 'action'],
            ],
            'generate-bill-number' => [
                'name' => 'generate-bill-number',
                'fname' => 'generateBillNumber',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'store-generate-bill-number' => [
                'name' => 'store-generate-bill-number',
                'fname' => 'storeBillNumber',
                'method' => 'post',
                'permission' => 'generate-bill-number-temporary_fund',
                'params' => ['id'],
            ],
        ],
    ],
    31 => [
        'permission' => 'transaction',
        'table' => 'transactions',
        'controller' => 'Transaction',
        'url' => 'transactions',
        'description' => 'transactions records for the clients',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'internal-transfers' => [
                'name' => 'internal-transfers',
                'fname' => 'internalTransferHistory',
                'method' => 'get',
                'permission' => 'view-transaction',
                'params' => [],
            ],
            'export-internal-transfers' => [
                'name' => 'export-internal-transfers',
                'fname' => 'exportInternalTransfer',
                'method' => 'get',
                'permission' => 'export-transaction',
                'params' => [],
            ],
            'edit-after-execute' => [
                'name' => 'edit-after-execute',
                'fname' => 'updateTransactionId',
                'method' => 'put',
                'permission' => null,
                'params' => ['id'],
            ],
            'toggle-visibility' => [
                'name' => 'toggle-visibility',
                'fname' => 'toggleVisibility',
                'method' => 'post',
                'permission' => null,
                'params' => ['id'],
            ],
            'create-missing' => [
                'name' => 'create-missing',
                'fname' => 'createMissing',
                'method' => 'post',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    32 => [
        'permission' => 'user',
        'table' => 'users',
        'controller' => 'User',
        'url' => 'users',
        'description' => 'users data, personal information and their role',
        'has_crud' => true,
        'other_permissions' => [
            'login_as' => [
                'name' => 'login-as',
                'fname' => 'loginAs',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
        ],
    ],
    33 => [
        'permission' => 'user_document',
        'table' => 'user_documents',
        'controller' => 'UserDocument',
        'url' => 'user-documents',
        'description' => 'user documents uploaded by the clients for the KYC process',
        'has_crud' => true,
        'other_permissions' => [
            'id_wise' => [
                'name' => 'id-wise',
                'fname' => 'idWise',
                'method' => 'get',
                'permissions' => null,
                'params' => []
            ]
        ]
    ],
    34 => [
        'permission' => 'user_eligibility',
        'table' => 'user_eligibilities',
        'controller' => 'UserEligibility',
        'url' => 'user-eligibilities',
        'description' => 'answers for the user ASIC eligibility questions',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    35 => [
        'permission' => 'language',
        'table' => 'languages',
        'controller' => 'Language',
        'url' => 'languages',
        'description' => 'languages available for the clients to select language of the website and notifications',
        'has_crud' => true,
    ],
    36 => [
        'permission' => 'market_report',
        'table' => 'market_reports',
        'controller' => 'MarketReport',
        'url' => 'market-reports',
        'description' => 'market reports, news, announcements and holidays on website',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'send-email' => [
                'name' => 'send-email',
                'fname' => 'sendEmail',
                'method' => 'post',
                'permission' => null,
                'params' => ['param'],
            ]
        ],
    ],
    37 => [
        'permission' => 'market_report_type',
        'table' => 'market_report_types',
        'controller' => 'MarketReportType',
        'url' => 'market-report-types',
        'description' => 'market report types like news, announcements, holidays, etc.',
        'has_crud' => true,
    ],
    38 => [
        'permission' => 'market_report_instrument',
        'table' => 'market_report_instruments',
        'controller' => 'MarketReportInstrument',
        'url' => 'market-report-instruments',
        'description' => 'market report sub-sections for the instruments',
        'has_crud' => true,
    ],
    39 => [
        'permission' => 'slider',
        'table' => 'sliders',
        'controller' => 'Slider',
        'url' => 'sliders',
        'description' => 'main sliders on the website',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    40 => [
        'permission' => 'trading_platform',
        'table' => 'trading_platforms',
        'controller' => 'TradingPlatform',
        'url' => 'trading-platforms',
        'description' => 'trading platforms available for the clients to select the platform to trade and account opening',
        'has_crud' => true,
    ],
    41 => [
        'permission' => 'instrument',
        'table' => 'instruments',
        'controller' => 'Instrument',
        'url' => 'instruments',
        'description' => 'instruments available for the clients to trade and on website',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    42 => [
        'permission' => 'permission',
        'table' => 'permissions',
        'controller' => 'Permission',
        'url' => 'permissions',
        'description' => 'permissions for the system users',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    43 => [
        'permission' => 'account_form',
        'table' => 'account_forms',
        'controller' => 'AccountForm',
        'url' => 'account-forms',
        'description' => 'legal documents for KYC and website',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    44 => [
        'permission' => 'account_form_category',
        'table' => 'account_form_categories',
        'controller' => 'AccountFormCategory',
        'url' => 'account-form-categories',
        'description' => 'legal documents categories for KYC and website',
        'has_crud' => true,
    ],
    45 => [
        'permission' => 'app_relation',
        'table' => 'app_relations',
        'controller' => 'AppRelation',
        'url' => 'app-relations',
        'description' => 'app assigns and the relation between applications and account managers',
        'has_crud' => true,
    ],
    46 => [
        'permission' => 'seos',
        'table' => 'seos',
        'controller' => 'Seo',
        'url' => 'seos',
        'description' => 'search engine optimization (SEO) for the website and CRM pages',
        'has_crud' => true,
    ],
    47 => [
        'permission' => 'user_login_log',
        'table' => 'logs',
        'controller' => 'UserLoginLog',
        'url' => 'user-login-logs',
        'description' => 'users login logs report, type of devices and ip addresses',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    48 => [
        'permission' => 'third_party_log',
        'table' => 'logs',
        'controller' => 'ThirdPartyLog',
        'url' => 'third-party-logs',
        'description' => 'third party api requests and responses logs',
        'has_crud' => false,
    ],
    49 => [
        'permission' => 'app_relation_country_assign',
        'table' => 'app_relation_country_assigns',
        'controller' => 'AppRelationCountryAssign',
        'url' => 'app-relation-country-assigns',
        'description' => 'auto assign rules by entity and country for the account managers',
        'has_crud' => true,
    ],
    50 => [
        'permission' => 'group_analytic',
        'table' => 'analytics',
        'controller' => 'GroupAnalytic',
        'url' => 'group-analytics',
        'description' => 'metatrader group analytics and registration',
        'has_crud' => false,
    ],
    51 => [
        'permission' => 'faq',
        'table' => 'faqs',
        'controller' => 'Faq',
        'url' => 'faqs',
        'description' => 'frequently asked questions and answers that displayed on the website',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    52 => [
        'permission' => 'glossary_term',
        'table' => 'glossary_terms',
        'controller' => 'GlossaryTerm',
        'url' => 'glossary-terms',
        'description' => 'glossary terms and definitions that displayed on the website',
        'has_crud' => true,
    ],
    53 => [
        'permission' => 'contact_request_subject',
        'table' => 'contact_request_subjects',
        'controller' => 'ContactRequestSubject',
        'url' => 'contact-request-subjects',
        'description' => 'categories of the contact us and user ticket forms',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    54 => [
        'permission' => 'error_log',
        'table' => 'error_logs',
        'controller' => 'ErrorLog',
        'url' => 'error-logs',
        'description' => 'system errors and exceptions logs for IT uses',
        'has_crud' => true,
        'other_permissions' => [
            'delete-all' => [
                'name' => 'delete-all',
                'fname' => 'destroyAll',
                'method' => 'get',
                'permission' => 'delete-error_log',
                'params' => [],
            ],
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    55 => [
        'permission' => 'affiliate_website_config',
        'table' => 'affiliate_website_configs',
        'controller' => 'AffiliateWebsiteConfig',
        'url' => 'affiliate-website-configs',
        'description' => 'affiliate website and entity configs and settings',
        'has_crud' => true,
    ],
    57 => [
        'permission' => 'mt_group_spec',
        'table' => 'offer_account_types',
        'controller' => 'OfferAccountType',
        'url' => 'mt-group-specs',
        'description' => 'available account types on website. ex: standard, pro, micro, etc.',
        'has_crud' => true,
    ],
    58 => [
        'permission' => 'mt_group_forward',
        'table' => 'mt_group_forwards',
        'controller' => 'MtGroupForward',
        'url' => 'mt-group-forwards',
        'description' => 'forward metatrader group from the old group to another new group',
        'has_crud' => true,
    ],
    59 => [
        'permission' => 'ticket',
        'table' => 'tickets',
        'controller' => 'Ticket',
        'url' => 'tickets',
        'description' => 'tickets and support requests from the clients',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    60 => [
        'permission' => 'corporate_event',
        'table' => 'corporate_events',
        'controller' => 'CorporateEvent',
        'url' => 'corporate-events',
        'description' => 'corporate events and announcements on the website',
        'has_crud' => true,
        'other_permissions' => [
            'view-import' => [
                'name' => 'view-import',
                'fname' => 'viewImport',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'import' => [
                'name' => 'import',
                'fname' => 'import',
                'method' => 'put',
                'permission' => 'view-import-corporate_event',
                'params' => [],
            ],
        ],
    ],
    61 => [
        'permission' => 'trading_platform_numbering',
        'table' => 'trading_platform_numberings',
        'controller' => 'TradingPlatformNumbering',
        'url' => 'trading-platform-numberings',
        'description' => 'trading platform numbering criteria based entity and platform',
        'has_crud' => true,
    ],
    62 => [
        'permission' => 'promotion_slider',
        'table' => 'promotion_sliders',
        'controller' => 'PromotionSlider',
        'url' => 'promotion-sliders',
        'description' => 'promotion sliders on the crm dashboard based entity and country',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    63 => [
        'permission' => 'regulation_entity',
        'table' => 'regulation_entities',
        'controller' => 'RegulationEntity',
        'url' => 'regulation-entities',
        'description' => 'regulation entities and their regulations and information',
        'has_crud' => true,
    ],
    64 => [
        'permission' => 'app_opportunity_option',
        'table' => 'app_opportunity_options',
        'controller' => 'AppOpportunityOption',
        'url' => 'app-opportunity-options',
        'description' => 'status for each application. ex: new, not interested, approved, deposited, etc.',
        'has_crud' => true,
    ],
    65 => [
        'permission' => 'opportunity_option',
        'table' => 'opportunity_options',
        'controller' => 'OpportunityOption',
        'url' => 'opportunity-options',
        'description' => 'options to categorize the clients and leads. ex: new, not interested, approved, deposited, etc.',
        'has_crud' => true,
    ],
    66 => [
        'permission' => 'comment',
        'table' => 'comments',
        'controller' => 'Comment',
        'url' => 'comments',
        'description' => 'comments and notes for the clients, orders, transactions and tickets',
        'has_crud' => true,
    ],
    67 => [
        'permission' => 'contact',
        'table' => 'contacts',
        'controller' => 'Contact',
        'url' => 'contacts',
        'description' => 'private contact information for each sales person, it used instead of excel sheets',
        'has_crud' => true,
        'other_permissions' => [
            'view-assign' => [
                'name' => 'view-assign',
                'fname' => 'viewAssignContact',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'save-assign' => [
                'name' => 'save-assign',
                'fname' => 'assignContact',
                'method' => 'put',
                'permission' => 'view-assign-contact',
                'params' => ['id'],
            ],
        ],
    ],
    68 => [
        'permission' => 'trading_platform_configuration',
        'table' => 'trading_platform_configs',
        'controller' => 'TradingPlatformConfig',
        'url' => 'trading-platform-configurations',
        'description' => 'trading platform configuration and connection credentials with the trading servers',
        'has_crud' => true,
    ],
    69 => [
        'permission' => 'job',
        'table' => 'jobs',
        'controller' => 'Job',
        'url' => 'jobs',
        'description' => 'jobs and tasks that run in the background and scheduled for the future',
        'has_crud' => true,
        'other_permissions' => [
            'delete-all' => [
                'name' => 'delete-all',
                'fname' => 'destroyAll',
                'method' => 'get',
                'permission' => 'delete-job',
                'params' => [],
            ],
            'failed-jobs' => [
                'name' => 'failed-jobs',
                'fname' => 'failedJob',
                'method' => 'get',
                'permission' => 'view-job',
                'params' => [],
            ],
            'failed-jobs-details' => [
                'name' => 'failed-jobs-details',
                'fname' => 'failedJobDetails',
                'method' => 'get',
                'permission' => 'view-job',
                'params' => ['id'],
            ],
            'delete-failed-job' => [
                'name' => 'delete-failed-job',
                'fname' => 'destroyFailed',
                'method' => 'delete',
                'permission' => 'delete-job',
                'params' => ['id'],
            ],
            'delete-all-failed-job' => [
                'name' => 'delete-all-failed-job',
                'fname' => 'destroyFailedAll',
                'method' => 'get',
                'permission' => 'delete-job',
                'params' => [],
            ],
            'retry-failed-job' => [
                'name' => 'retry-failed-job',
                'fname' => 'retryFailed',
                'method' => 'get',
                'permission' => 'delete-job',
                'params' => ['id'],
            ],
            'retry-all-failed-job' => [
                'name' => 'retry-all-failed-job',
                'fname' => 'retryAllFailed',
                'method' => 'get',
                'permission' => 'view-job',
                'params' => [],
            ],
        ],
    ],
    70 => [
        'permission' => 'site_banking_detail',
        'table' => 'site_banking_details',
        'controller' => 'SiteBankingDetail',
        'url' => 'site-banking-details',
        'description' => 'banking details for the clients to deposit and withdraw',
        'has_crud' => true,
    ],
    71 => [
        'permission' => 'manage_application',
        'table' => 'applications',
        'controller' => 'ApplicationManagement',
        'url' => 'manage-applications',
        'description' => 'applications KYC data and leads data from the clients and partners',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'export-pdf' => [
                'name' => 'export-pdf',
                'fname' => 'exportPdf',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'enable-disable' => [
                'name' => 'enable-disable',
                'fname' => 'enableDisable',
                'method' => 'post',
                'permission' => null,
                'params' => ['param'],
            ],
            'store-disable' => [
                'name' => 'store-disable',
                'fname' => 'storeDisable',
                'method' => 'post',
                'permission' => null,
                'params' => [],
            ],
            'duplicate' => [
                'name' => 'duplicate',
                'fname' => 'duplicate',
                'method' => 'post',
                'permission' => null,
                'params' => ['param'],
            ],
            'verify' => [
                'name' => 'verify',
                'fname' => 'verify',
                'method' => 'post',
                'permission' => 'duplicate-manage_application',
                'params' => ['param'],
            ],
            'save-contact-reminder' => [
                'name' => 'save-contact-reminder',
                'fname' => 'saveContactReminder',
                'method' => 'post',
                'permission' => null,
                'params' => [],
            ],
            'archive' => [
                'name' => 'archive',
                'fname' => 'archive',
                'method' => 'post',
                'permission' => null,
                'params' => ['param'],
            ],
            'store-archive' => [
                'name' => 'store-archive',
                'fname' => 'storeArchive',
                'method' => 'post',
                'permission' => null,
                'params' => [],
            ],
            'assigned' => [
                'name' => 'assigned',
                'fname' => 'assignedApplication',
                'method' => 'get',
                'permission' => 'view-manage_application',
                'params' => [],
            ],
            'suspend' => [
                'name' => 'suspend',
                'fname' => 'suspend',
                'method' => 'post',
                'permission' => null,
                'params' => ['param'],
            ],
            'upload-documents-verification' => [
                'name' => 'upload-documents-verification',
                'fname' => 'uploadDocumentsVerification',
                'method' => 'get',
                'permission' => 'edit-manage_application',
                'middleware' => 'verifyDocumentsUpload',
                'params' => ['application_id'],
            ],
            'activities' => [
                'name' => 'activity',
                'fname' => 'activities',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'security-preferences-app' => [
                'name' => 'security-preferences-app',
                'fname' => 'appSecurityPreferences',
                'method' => 'post',
                'permission' => null,
                'params' => ['user_id', 'device_id'],
            ],
            'utm-data' => [
                'name' => 'utm-data',
                'fname' => 'utmData',
                'method' => 'get',
                'permission' => null,
                'params' => ['application_id'],
            ],
            'sync-manaf-app' => [
                'name' => 'sync-manaf-app',
                'fname' => 'syncManafApp',
                'method' => 'get',
                'permission' => null,
                'params' => ['application_id'],
            ],
            'sync-signed-documents' => [
                'name' => 'sync-signed-documents',
                'fname' => 'syncSignedDocuments',
                'method' => 'get',
                'permission' => null,
                'params' => ['application_id'],
            ],
            'change-password' => [
                'name' => 'change-password',
                'fname' => 'changePassword',
                'method' => 'get',
                'permission' => null,
                'params' => ['application_id'],
            ],
        ],
    ],
    72 => [
        'permission' => 'manage_order',
        'table' => 'orders',
        'controller' => 'OrderManagement',
        'url' => 'manage-orders',
        'description' => 'pending orders and requests data from the clients',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    73 => [
        'permission' => 'manage_transaction',
        'table' => 'transactions',
        'controller' => 'TransactionManagement',
        'url' => 'manage-transactions',
        'description' => 'pending transactions records for the clients',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'wallet_summary_recalculate' => [
                'name' => 'wallet-summary-recalculate',
                'fname' => 'walletSummaryRecalculate',
                'method' => 'get',
                'permission' => null,
                'params' => [
                    'app_id'
                ],
            ],
        ],
    ],
    74 => [
        'permission' => 'manage_mt_account',
        'table' => 'mt_accounts',
        'controller' => 'MtAccountManagement',
        'url' => 'manage-mt-accounts',
        'description' => 'metatrader accounts records for the clients',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'create-credit-request' => [
                'name' => 'create-credit-request',
                'fname' => 'showCreditRequestForm',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'store-credit-request' => [
                'name' => 'store-credit-request',
                'fname' => 'storeCreditRequestForm',
                'method' => 'post',
                'permission' => null,
                'params' => [],
            ],
            'export-deals' => [
                'name' => 'export-deals',
                'fname' => 'exportHistory',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'export-positions' => [
                'name' => 'export-positions',
                'fname' => 'exportPositions',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'export-orders' => [
                'name' => 'export-orders',
                'fname' => 'exportOrders',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'export-orders-history' => [
                'name' => 'export-order-history',
                'fname' => 'exportOrderHistory',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'summary' => [
                'name' => 'summary',
                'fname' => 'accountSummary',
                'method' => 'get',
                'permission' => 'view-manage_mt_account',
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'commissions' => [
                'name' => 'commissions',
                'fname' => 'accountCommissions',
                'method' => 'get',
                'permission' => 'view-manage_mt_account',
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'export-commissions' => [
                'name' => 'export-commissions',
                'fname' => 'exportCommissions',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'transactions' => [
                'name' => 'transactions',
                'fname' => 'accountTransactions',
                'method' => 'get',
                'permission' => 'view-manage_mt_account',
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'export-transactions' => [
                'name' => 'export-transactions',
                'fname' => 'exportTransactions',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'positions' => [
                'name' => 'positions',
                'fname' => 'accountPositions',
                'method' => 'get',
                'permission' => 'view-manage_mt_account',
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'history' => [
                'name' => 'history',
                'fname' => 'accountHistory',
                'method' => 'get',
                'permission' => 'view-manage_mt_account',
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'orders' => [
                'name' => 'orders',
                'fname' => 'accountOrders',
                'method' => 'get',
                'permission' => 'view-manage_mt_account',
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'orders-history' => [
                'name' => 'orders-history',
                'fname' => 'accountOrdersHistory',
                'method' => 'get',
                'permission' => 'view-manage_mt_account',
                'params' => ['id'],
                'gates' => 'access-mt-statistics',
            ],
            'not-synced-filter' => [
                'name' => 'not-synced-filter',
                'fname' => 'notSyncedFilter',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    75 => [
        'permission' => 'manage_ticket',
        'table' => 'tickets',
        'controller' => 'TicketManagement',
        'url' => 'manage-tickets',
        'description' => 'pending tickets and support requests from the clients',
        'has_crud' => true,
    ],
    76 => [
        'permission' => 'watch_list',
        'table' => 'watch_lists',
        'controller' => 'WatchList',
        'url' => 'watch-lists',
        'description' => 'watch lists / favorites for the clients, metatrader accounts',
        'has_crud' => true,
    ],
    77 => [
        'permission' => 'translation',
        'table' => 'translations',
        'controller' => 'Translation',
        'url' => 'translations',
        'description' => 'transactions history records for the clients',
        'has_crud' => true,
    ],
    78 => [
        'permission' => 'institutional_service',
        'table' => 'institutional_services',
        'controller' => 'InstitutionalService',
        'url' => 'institutional-services',
        'description' => 'institutional services categories and services that we provide to the clients',
        'has_crud' => true,
    ],
    79 => [
        'permission' => 'institutional_contact_request',
        'table' => 'institutional_contact_requests',
        'controller' => 'InstitutionalContactRequest',
        'url' => 'institutional-contact-requests',
        'description' => 'institutional contact requests from the clients',
        'has_crud' => true,
        'other_permissions' => [
            'view-assign' => [
                'name' => 'view-assign',
                'fname' => 'viewAssignInstitutionalContact',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'save-assign' => [
                'name' => 'save-assign',
                'fname' => 'assignInstitutionalContact',
                'method' => 'put',
                'permission' => 'view-assign-institutional_contact_request',
                'params' => ['id'],
            ],
        ],
    ],
    80 => [
        'permission' => 'contact_reminder',
        'table' => 'contact_reminders',
        'controller' => 'ContactReminder',
        'url' => 'contact-reminders',
        'description' => 'calendar and reminders for the contacts and clients',
        'has_crud' => true,
        'other_permissions' => [
            'view-calendar' => [
                'name' => 'view-calendar',
                'fname' => 'fullCalender',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    81 => [
        'permission' => 'application_analytic',
        'table' => 'analytics',
        'controller' => 'ApplicationAnalytic',
        'url' => 'application-analytics',
        'description' => 'applications chart and registration growth graph',
        'has_crud' => false,
    ],
    82 => [
        'permission' => 'mt_account_analytic',
        'table' => 'analytics',
        'controller' => 'MtAccountAnalytic',
        'url' => 'mt-accounts-analytics',
        'description' => 'metatrader accounts chart and registration growth graph',
        'has_crud' => false,
    ],
    83 => [
        'permission' => 'transaction_analytic',
        'table' => 'analytics',
        'controller' => 'TransactionAnalytic',
        'url' => 'transactions-analytics',
        'description' => 'transactions chart, deposits and withdrawals growth graph',
        'has_crud' => false,
    ],
    84 => [
        'permission' => 'manaf_bank',
        'table' => 'manaf_bank',
        'controller' => 'ManafBank',
        'url' => 'manaf-banks',
        'description' => 'Manaf software banks matching for integration purposes',
        'has_crud' => true,
    ],
    85 => [
        'permission' => 'manaf_bank_account',
        'table' => 'manaf_bank_account',
        'controller' => 'ManafBankAccount',
        'url' => 'manaf-bank-accounts',
        'description' => 'Manaf software bank Account matching for integration purposes',
        'has_crud' => true,
    ],
    86 => [
        'permission' => 'equity_report',
        'table' => 'reports',
        'controller' => 'EquityReport',
        'url' => 'equity-reports',
        'description' => 'metatrader equities, credit and balances reports for the clients',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    87 => [
        'permission' => 'open_position_report',
        'table' => 'reports',
        'controller' => 'OpenPositionReport',
        'url' => 'open-position-reports',
        'description' => 'metatrader open position reports for the clients',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    88 => [
        'permission' => 'liquidation_report',
        'table' => 'reports',
        'controller' => 'LiquidationReport',
        'url' => 'liquidation-reports',
        'description' => 'metatrader liquidation reports for the clients',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    90 => [
        'permission' => 'scalping_report',
        'table' => 'reports',
        'controller' => 'ScalpingReport',
        'url' => 'scalping-reports',
        'description' => 'metatrader scalping (short time execution) reports for the clients',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'update' => [
                'name' => 'store-comment',
                'fname' => 'storeComment',
                'method' => 'post',
                'permission' => null,
                'params' => ['login', 'id'],
            ],
            'resolve' => [
                'name' => 'store-resolved',
                'fname' => 'storeResolved',
                'method' => 'post',
                'permission' => 'view-scalping_report',
                'params' => ['login', 'id'],
            ],
        ],
    ],
    91 => [
        'permission' => 'university',
        'table' => 'universities',
        'controller' => 'University',
        'url' => 'universities',
        'description' => 'universities list based on the country',
        'has_crud' => true,
    ],
    92 => [
        'permission' => 'university_lead',
        'table' => 'university_leads',
        'controller' => 'UniversityLead',
        'url' => 'university-leads',
        'description' => 'registered leads and promo code from the university campaigns',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    93 => [
        'permission' => 'crypto_internal_transaction',
        'table' => 'crypto_internal_transactions',
        'controller' => 'CryptoInternalTransaction',
        'url' => 'crypto-internal-transactions',
        'description' => 'internal crypto transactions between the company branches',
        'has_crud' => true,
    ],
    94 => [
        'permission' => 'mt_agent_restriction',
        'table' => 'mt_agent_settings',
        'controller' => 'MtAgentRestriction',
        'url' => 'agent-settings',
        'description' => 'metatrader agent and their clients transfer, account opening restrictions',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    95 => [
        'permission' => 'app_available_group',
        'table' => 'app_available_groups',
        'controller' => 'AppAvailableGroup',
        'url' => 'app-available-groups',
        'description' => 'groups for the application that allowed to create metatrader accounts on it',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    96 => [
        'permission' => 'affiliate_website_translatable_config',
        'table' => 'affiliate_website_translatable_configs',
        'controller' => 'AffiliateWebsiteTranslatableConfig',
        'url' => 'affiliate-website-translatable-configs',
        'description' => 'affiliate website configs and settings (multi language)',
        'has_crud' => true,
    ],
    97 => [
        'permission' => 'faq_category',
        'table' => 'faq_categories',
        'controller' => 'FaqCategory',
        'url' => 'faq-categories',
        'description' => 'faq categories to categorize the faq on website',
        'has_crud' => true,
    ],
    98 => [
        'permission' => 'career',
        'table' => 'careers',
        'controller' => 'Career',
        'url' => 'careers',
        'description' => 'available job positions to display on the website',
        'has_crud' => true,
    ],
    99 => [
        'permission' => 'payment_gateway_exception',
        'table' => 'payment_gateway_exceptions',
        'controller' => 'PaymentGatewayException',
        'url' => 'payment-gateway-exceptions',
        'description' => 'payment gateway fee exceptions based on country and payment method',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    100 => [
        'permission' => 'call_center',
        'table' => 'reports',
        'controller' => 'Voip',
        'url' => 'voip',
        'description' => 'sales, clients calls and recordings reports',
        'has_crud' => false,
    ],
    101 => [
        'permission' => 'exchanger',
        'table' => 'exchangers',
        'controller' => 'Exchanger',
        'url' => 'exchanger',
        'description' => 'agents list that work as an exchanger for the company. this used to transfer money between agents and their clients',
        'has_crud' => true,
    ],
    102 => [
        'permission' => 'campaign-lead',
        'table' => 'campaign_leads',
        'controller' => 'CampaignLead',
        'url' => 'campaign-leads',
        'description' => 'leads generated from the marketing campaigns',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'view-assign' => [
                'name' => 'view-assign',
                'fname' => 'viewAssignCampaign',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'save-assign' => [
                'name' => 'save-assign',
                'fname' => 'assignCampaign',
                'method' => 'put',
                'permission' => 'view-assign-campaign-lead',
                'params' => ['id'],
            ],
            'view-import' => [
                'name' => 'view-import',
                'fname' => 'viewImport',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'import' => [
                'name' => 'import',
                'fname' => 'import',
                'method' => 'put',
                'permission' => 'view-import-campaign-lead',
                'params' => [],
            ],
        ],
    ],
    103 => [
        'permission' => 'check_list',
        'table' => 'check_lists',
        'controller' => 'CheckList',
        'url' => 'check-list',
        'description' => 'check list/to do list for transaction checking before execution',
        'has_crud' => true,
    ],
    104 => [
        'permission' => 'payment_gateway_config',
        'table' => 'payment_gateway_configs',
        'controller' => 'PaymentGatewayConfig',
        'url' => 'payment-gateway-config',
        'description' => 'payment gateway integration credentials',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    105 => [
        'permission' => 'partnership_lead',
        'table' => 'partnership_leads',
        'controller' => 'PartnershipLead',
        'url' => 'partnership-leads',
        'description' => 'individuals and companies that interested to be our partners',
        'has_crud' => true,
    ],
    106 => [
        'permission' => 'affiliate_website_exception',
        'table' => 'affiliate_website_exceptions',
        'controller' => 'AffiliateWebsiteException',
        'url' => 'affiliate-website-exceptions',
        'description' => 'affiliate website redirect rules. ex: clients from jordan should be redirected to the jordan crm',
        'has_crud' => true,
    ],
    107 => [
        'permission' => 'wallet_report',
        'table' => 'reports',
        'controller' => 'WalletReport',
        'url' => 'wallet-reports',
        'description' => 'e-wallet records and their balance and currency',
        'has_crud' => false,
        'other_permissions' => [
            'daily-wallet-balance' => [
                'name' => 'daily-balance',
                'fname' => 'getDailyBalance',
                'method' => 'get',
                'permission' => 'view-wallet_report',
                'params' => ['id'],
            ],
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    108 => [
        'permission' => 'mt_group_spec_swap',
        'table' => 'mt_group_spec_swaps',
        'controller' => 'MtGroupSpecSwap',
        'url' => 'mt-group-spec-swaps',
        'description' => 'metatrader group swap and rollover fees on website',
        'has_crud' => true,
    ],
    109 => [
        'permission' => 'mt_group_allocation',
        'table' => 'mt_group_allocations',
        'controller' => 'MtGroupAllocation',
        'url' => 'mt-group-allocations',
        'description' => 'metatrader group allocation for reporting purposes',
        'has_crud' => true,
    ],
    110 => [
        'permission' => 'educational_material_category',
        'table' => 'educational_material_categories',
        'controller' => 'EducationalMaterialCategory',
        'url' => 'educational-material-categories',
        'description' => 'educational material categories (subjects) to categorize the ads material for partners',
        'has_crud' => true,
    ],
    111 => [
        'permission' => 'educational_material',
        'table' => 'educational_materials',
        'controller' => 'EducationalMaterial',
        'url' => 'educational-materials',
        'description' => 'educational material content and banners for the partners to use in their marketing campaigns',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    112 => [
        'permission' => 'duplicate_application_report',
        'table' => 'reports',
        'controller' => 'DuplicateApplicationReport',
        'url' => 'duplicate-application-reports',
        'description' => 'duplicate applications report that show clients who has same user name or login from the same IP address',
        'has_crud' => false,
    ],
    113 => [
        'permission' => 'api_key',
        'table' => 'api_keys',
        'controller' => 'ApiKey',
        'url' => 'api-keys',
        'description' => 'api keys that ingot provide to the third-parties for secure access to the data',
        'has_crud' => true,
    ],
    114 => [
        'permission' => 'api_key_access_event',
        'table' => 'logs',
        'controller' => 'ApiKeyAccessEvent',
        'url' => 'api-access-logs',
        'description' => 'third-party api logs and events',
        'has_crud' => false,
    ],
    116 => [
        'permission' => 'email_tags',
        'table' => 'email_tags',
        'controller' => 'EmailTag',
        'url' => 'email-tags',
        'description' => 'predefined email tags that used when reject orders/transactions',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    117 => [
        'permission' => 'bank_trans_specs',
        'table' => 'bank_transfer_specs',
        'controller' => 'BankTransferSpecs',
        'url' => 'bank-trans-specs',
        'description' => 'bank transfer payment methods validation option when required extra data. ex: iban (optional), swift code (required) etc.',
        'has_crud' => true,
    ],
    118 => [
        'permission' => 'educational_article',
        'table' => 'educational_articles',
        'controller' => 'EducationalArticle',
        'url' => 'educational-articles',
        'description' => 'educational articles tutorials to display on the website',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    119 => [
        'permission' => 'migrate_app_cellxpert',
        'table' => 'data_migration',
        'controller' => 'MigrationCellxpert',
        'url' => 'migrate-app-cellxpert',
        'description' => 'migrate partner  application to cellxpert system',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_app_cellxpert',
                'params' => [],
            ],
        ],
    ],
    120 => [
        'permission' => 'flag_per',
        'table' => 'flag_pers',
        'controller' => 'FlagPer',
        'url' => 'flag-per',
        'description' => 'flag a payment gateway based on the country and entity',
        'has_crud' => true,
    ],
    121 => [
        'permission' => 'fraud_dashboard',
        'table' => 'analytics',
        'controller' => 'FraudManagement',
        'url' => 'fraud-dashboard',
        'description' => 'fraud dashboard and analytics for the clients',
        'has_crud' => false,
    ],
    122 => [
        'permission' => 'agent_activity_report',
        'table' => 'reports',
        'controller' => 'AgentActivityReport',
        'url' => 'agent-activity-reports',
        'description' => 'agent performance report and their groups, number of registered accounts',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    123 => [
        'permission' => 'market_reports_analytic',
        'table' => 'analytics',
        'controller' => 'MarketReportAnalytic',
        'url' => 'market-reports-analytics',
        'description' => 'market report and analysis for user requests based on language and instruments',
        'has_crud' => false,
    ],
    124 => [
        'permission' => 'promotion',
        'table' => 'promotions',
        'controller' => 'Promotion',
        'url' => 'promotion',
        'description' => 'promotions, offers and bonuses based country and agent',
        'has_crud' => true,
        'other_permissions' => [
            'enable-for-app' => [
                'name' => 'enable-for-app',
                'fname' => 'enableForApp',
                'method' => 'post',
                'permission' => null,
                'params' => ['app_id'],
            ],
            'disable-for-app' => [
                'name' => 'disable-for-app',
                'fname' => 'disableForApp',
                'method' => 'get',
                'permission' => 'enable-for-app-promotion',
                'params' => ['app_id' ,'promotion_id'],
            ],
        ],
    ],
    125 => [
        'permission' => 'order_type',
        'table' => 'order_types',
        'controller' => 'OrderType',
        'url' => 'order-types',
        'description' => 'order types for the requests the clients can make',
        'has_crud' => true,
    ],
    126 => [
        'permission' => 'gender',
        'table' => 'genders',
        'controller' => 'Gender',
        'url' => 'genders',
        'description' => 'gender list to allow clients to choose from',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    127 => [
        'permission' => 'user_title',
        'table' => 'user_titles',
        'controller' => 'UserTitle',
        'url' => 'user-titles',
        'description' => 'user titles to allow clients to choose from. ex: Mr, Mrs, Dr, Prof etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    128 => [
        'permission' => 'marital_status',
        'table' => 'marital_statuses',
        'controller' => 'MaritalStatus',
        'url' => 'marital-statuses',
        'description' => 'marital status to allow clients to choose from. ex: single, married, divorced etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    129 => [
        'permission' => 'source_of_fund',
        'table' => 'source_of_fund_questions',
        'controller' => 'SourceOfFund',
        'url' => 'source-of-funds',
        'description' => 'source of fund questions to allow clients to choose from in kyc',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    130 => [
        'permission' => 'job_position',
        'table' => 'job_positions',
        'controller' => 'JobPosition',
        'url' => 'job-positions',
        'description' => 'job positions to allow clients to choose from. ex: manager, director, developer etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    131 => [
        'permission' => 'business_sector',
        'table' => 'business_sectors',
        'controller' => 'BusinessSector',
        'url' => 'business-sectors',
        'description' => 'business sectors to allow clients to choose from. ex: finance, technology, health etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    132 => [
        'permission' => 'professionalism',
        'table' => 'professionalisms',
        'controller' => 'Professionalism',
        'url' => 'professionalisms',
        'description' => 'professionalism to allow clients to choose from. ex: employee, employer, self-employed etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    133 => [
        'permission' => 'index_dividends_reports',
        'table' => 'reports',
        'controller' => 'IndexDividendReport',
        'url' => 'index-dividends-reports',
        'description' => 'index dividends distribution dates and clients who own the index',
        'has_crud' => false,
    ],
    134 => [
        'permission' => 'trading_server',
        'table' => 'trading_servers',
        'controller' => 'TradingServer',
        'url' => 'trading-servers',
        'description' => 'trading servers available from the tse unit for the clients',
        'has_crud' => true,
    ],
    135 => [
        'permission' => 'trading_server_config',
        'table' => 'trading_server_configs',
        'controller' => 'TradingServerConfig',
        'url' => 'trading-server-configs',
        'description' => 'trading server configuration, api credentials and connections',
        'has_crud' => true,
    ],
    136 => [
        'permission' => 'oauth_client',
        'table' => 'oauth_clients',
        'controller' => 'OAuthClient',
        'url' => 'oauth-clients',
        'description' => 'new third-party (oauth) connections list',
        'has_crud' => true,
    ],
    137 => [
        'permission' => 'oauth_client_config_key',
        'table' => 'oauth_client_config_keys',
        'controller' => 'OAuthClientConfigKey',
        'url' => 'oauth-client-config-keys',
        'description' => 'new third-party (oauth) api key',
        'has_crud' => true,
        'other_permissions' => [
            'edit-client-keys' => [
                'name' => 'edit-client-keys',
                'fname' => 'editClientKeys',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'update-client-keys' => [
                'name' => 'update-client-keys',
                'fname' => 'updateClientKeys',
                'method' => 'post',
                'permission' => null,
                'params' => ['id?'],
            ],
        ],
    ],
    138 => [
        'permission' => 'department',
        'table' => 'departments',
        'controller' => 'Department',
        'url' => 'departments',
        'description' => 'departments to categorize the careers job posts on website',
        'has_crud' => true,
    ],
    139 => [
        'permission' => 'company_type',
        'table' => 'company_types',
        'controller' => 'CompanyType',
        'url' => 'company-types',
        'description' => 'company types to allow clients to choose from',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    140 => [
        'permission' => 'closed_position_report',
        'table' => 'reports',
        'controller' => 'ClosedPositionReport',
        'url' => 'closed-position-reports',
        'description' => 'metatrader closed position reports which shows the closed positions for the clients',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    141 => [
        'permission' => 'portal_login_report',
        'table' => 'reports',
        'controller' => 'PortalLoginsReport',
        'url' => 'portal-login-reports',
        'description' => 'portal login report that shows the login history for the clients and their ip address',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    142 => [
        'permission' => 'deposit_and_withdrawal_report',
        'table' => 'reports',
        'controller' => 'DepositWithdrawalReport',
        'url' => 'deposit-withdrawal-reports',
        'description' => 'deposit and withdrawal report that shows the deposit and withdrawal history for each client',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    143 => [
        'permission' => 'payment_method_report',
        'table' => 'reports',
        'controller' => 'PaymentMethodReport',
        'url' => 'payment-method-reports',
        'description' => 'payment method report that shows the payment methods specification amount and the available payment methods',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    144 => [
        'permission' => 'ticket_report',
        'table' => 'reports',
        'controller' => 'TicketReport',
        'url' => 'ticket-reports',
        'description' => 'ticket report that shows a summary for each clients and number of open and closed tickets.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    145 => [
        'permission' => 'referral_report',
        'table' => 'reports',
        'controller' => 'ReferralReport',
        'url' => 'referral-reports',
        'description' => 'referral report that shows a summary for each referral and number of referred clients.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    146 => [
        'permission' => 'ticket_tag',
        'table' => 'ticket_tags',
        'controller' => 'TicketTag',
        'url' => 'ticket-tags',
        'description' => 'predefined ticket tags that used when reject tickets',
        'has_crud' => true,
    ],
    147 => [
        'permission' => 'retention_report',
        'table' => 'reports',
        'controller' => 'RetentionReport',
        'url' => 'retention-reports',
        'description' => 'inactive 90 days clients to follow by the retention team.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'getRetentionKpi' => [
                'name' => 'kpi',
                'fname' => 'getRetentionKpi',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    148 => [
        'permission' => 'webinar',
        'table' => 'webinars',
        'controller' => 'Webinar',
        'url' => 'webinars',
        'description' => 'webinar content and schedule for the clients and display on the website.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'webinarExport',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'view-assign' => [
                'name' => 'view-assign',
                'fname' => 'viewAssign',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'save-assign' => [
                'name' => 'save-assign',
                'fname' => 'assignWebinar',
                'method' => 'post',
                'permission' => 'view-assign-webinar',
                'params' => [],
            ],
        ],
    ],
    149 => [
        'permission' => 'campaign_template',
        'table' => 'campaign_templates',
        'controller' => 'CampaignTemplate',
        'url' => 'campaign-templates',
        'description' => 'templates and design for the campaigns to use in the marketing campaigns.',
        'has_crud' => true,
    ],
    150 => [
        'permission' => 'campaign_report',
        'table' => 'reports',
        'controller' => 'CampaignReport',
        'url' => 'campaign-reports',
        'description' => 'campaign report that shows the campaign history for the clients.',
        'has_crud' => false,
    ],
    151 => [
        'permission' => 'ticket_analytic',
        'table' => 'analytics',
        'controller' => 'TicketAnalytic',
        'url' => 'ticket-analytics',
        'description' => 'ticket analytics and reports for the clients based on category.',
        'has_crud' => false,
    ],
    152 => [
        'permission' => 'campaign',
        'table' => 'campaigns',
        'controller' => 'Campaign',
        'url' => 'campaigns',
        'description' => 'manage campaigns and their content for the clients.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    153 => [
        'permission' => 'tutorial_video',
        'table' => 'tutorial_videos',
        'controller' => 'TutorialVideo',
        'url' => 'tutorial-videos',
        'description' => 'manage tutorial videos and their content for the clients.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    155 => [
        'permission' => 'expired_account_report',
        'table' => 'reports',
        'controller' => 'ExpiredAccountsReport',
        'url' => 'expired-account-reports',
        'description' => 'expired accounts report that shows the expired accounts for the clients.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    156 => [
        'permission' => 'gainers_and_losers_report',
        'table' => 'reports',
        'controller' => 'GainersLosersReport',
        'url' => 'gainers-and-losers-reports',
        'description' => 'gainers and losers report that shows the gainers and losers for the clients on metatrader.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    157 => [
        'permission' => 'market_alert',
        'table' => 'analytics',
        'controller' => 'MarketAnalysis',
        'url' => 'market-alerts',
        'description' => 'market report that includes indicators, charts, and analysis for the clients.',
        'has_crud' => false,
    ],
    158 => [
        'permission' => 'application_status_tag',
        'table' => 'application_status_tags',
        'controller' => 'ApplicationStatusTag',
        'url' => 'application-status-tags',
        'description' => 'application status tags to categorize the application status.',
        'has_crud' => true,
    ],
    159 => [
        'permission' => 'province',
        'table' => 'provinces',
        'controller' => 'Province',
        'url' => 'provinces',
        'description' => 'manage provinces for clients to choose from. ex: amman, zarqa, irbid etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    160 => [
        'permission' => 'city',
        'table' => 'cities',
        'controller' => 'City',
        'url' => 'cities',
        'description' => 'manage cities for clients to choose from. ex: Abdali, Jabal Amman, Sweifieh etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    161 => [
        'permission' => 'bulk_sms',
        'table' => 'bulk_sms',
        'controller' => 'BulkSMS',
        'url' => 'bulk-sms',
        'description' => 'bulk sms to send sms to the clients',
        'has_crud' => true,
        'other_permissions' => [
            'send-sms' => [
                'name' => 'send-sms',
                'fname' => 'sendSMS',
                'method' => 'post',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    162 => [
        'permission' => 'sales_deposit_withdrawal_report',
        'table' => 'reports',
        'controller' => 'SalesDepositWithdrawal',
        'url' => 'sales-deposit-withdrawal-report',
        'description' => 'sales deposit and withdrawal report shows total deposits and withdrawals for the sales team.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    163 => [
        'permission' => 'mt_tags',
        'table' => 'mt_tags',
        'controller' => 'MtTags',
        'url' => 'mt-tags',
        'description' => 'metatrader tags for transactions.',
        'has_crud' => true,
    ],
    164 => [
        'permission' => 'mail_templates',
        'table' => 'mail_templates',
        'controller' => 'MailTemplate',
        'url' => 'mail-templates',
        'description' => 'mail templates to use in the mail notifications.',
        'has_crud' => true,
    ],
    165 => [
        'permission' => 'call_requests',
        'table' => 'call_requests',
        'controller' => 'CallRequests',
        'url' => 'call-requests',
        'description' => 'call requests for the clients to request a call from the sales team.',
        'has_crud' => true,
        'other_permissions' => [
            'updateCallRequest' => [
                'name' => 'storeCallRequest',
                'fname' => 'storeCallRequest',
                'method' => 'post',
                'permission' => null,
                'params' => [
                    'id',
                    'status',
                ],
            ],
        ],
    ],
    166 => [
        'permission' => 'classification_option',
        'table' => 'classification_options',
        'controller' => 'ClassificationOption',
        'url' => 'classification-options',
        'description' => 'classification categories for the sales to assign the categorize the clients. ex: retail, wholesale, sophisticated.',
        'has_crud' => true,
    ],
    167 => [
        'permission' => 'app_classification',
        'table' => 'app_classifications',
        'controller' => 'AppClassification',
        'url' => 'app-classifications',
        'description' => 'users classifications to show the categorized clients.',
        'has_crud' => true,
    ],
    168 => [
        'permission' => 'acquisition_report',
        'table' => 'reports',
        'controller' => 'AcquisitionReport',
        'url' => 'acquisition-reports',
        'description' => 'acquisition report that shows the acquisition KPIs for the sales team.',
        'has_crud' => false,
        'other_permissions' => [
            'getAcquisitionDailyKpi' => [
                'name' => 'daily-kpi',
                'fname' => 'getAcquisitionDailyKpi',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'getAcquisitionMonthlyKpi' => [
                'name' => 'monthly-kpi',
                'fname' => 'getAcquisitionMonthlyKpi',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    169 => [
        'permission' => 'client_timeline_report',
        'table' => 'reports',
        'controller' => 'ClientTimelineReport',
        'url' => 'client-timeline-reports',
        'description' => 'client timeline report that shows the client\'s activity and history. ex: comments, calls, live chat etc..',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    170 => [
        'permission' => 'group_tag',
        'table' => 'group_tags',
        'controller' => 'GroupTag',
        'url' => 'group-tags',
        'description' => 'group tags to categorize the groups.',
        'has_crud' => true,
    ],
    171 => [
        'permission' => 'political_questions',
        'table' => 'kyc_questionnaire',
        'controller' => 'PoliticallyPersonQuestion',
        'url' => 'political-questions',
        'description' => 'political questions to ask the clients in kyc process. ex: politically exposed person, family member etc.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    172 => [
        'permission' => 'withdrawal_transaction_internal_systems',
        'table' => 'transaction_internal_systems',
        'controller' => 'WithdrawalTransactionInternalSystem',
        'url' => 'withdrawal-transaction-internal-systems',
        'description' => 'deposits recording maker and checker for accounting uses.',
        'has_crud' => true,
    ],
    173 => [
        'permission' => 'deposit_transaction_internal_systems',
        'table' => 'transaction_internal_systems',
        'controller' => 'DepositTransactionInternalSystem',
        'url' => 'deposit-transaction-internal-systems',
        'description' => 'withdrawals recording maker and checker for accounting uses.',
        'has_crud' => true,
    ],
    174 => [
        'permission' => 'mt_prices',
        'table' => 'reports',
        'controller' => 'MtPrices',
        'url' => 'mt-prices',
        'description' => 'live prices for a specific symbol in the metatrader 5 platform.r',
        'has_crud' => false,
    ],
    175 => [
        'permission' => 'payment_method_webhook_log',
        'table' => 'logs',
        'controller' => 'PaymentMethodWebhookLog',
        'url' => 'payment-method-webhook-logs',
        'description' => 'payment method webhook logs for the payment methods that shows the response of every payment gateway ex: paypal, stripe, skrill etc.',
        'has_crud' => false,
    ],
    176 => [
        'permission' => 'seminar',
        'table' => 'seminars',
        'controller' => 'Seminar',
        'url' => 'seminars',
        'description' => 'seminar content and schedule for the clients and display on the website.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => ['seminar_id'],
            ],
        ],
    ],
    177 => [
        'permission' => 'newsroom',
        'table' => 'newsrooms',
        'controller' => 'Newsroom',
        'url' => 'newsroom',
        'description' => 'press and news content to display on the website.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    178 => [
        'permission' => 'mail_notifier',
        'table' => 'mail_notifiers',
        'controller' => 'MailNotifier',
        'url' => 'mail-notifiers',
        'description' => 'mail notifier to send bulk email to the clients.',
        'has_crud' => true,
        'other_permissions' => [
            'send-email' => [
                'name' => 'send-email',
                'fname' => 'sendEmail',
                'method' => 'post',
                'permission' => null,
                'params' => ['param'],
            ],
        ],
    ],
    179 => [
        'permission' => 'transaction_type',
        'table' => 'transaction_types',
        'controller' => 'TransactionType',
        'url' => 'transaction-types',
        'description' => 'transaction types for the requests the clients can make. ex: deposit, withdrawal, internal transfer etc.',
        'has_crud' => true,
    ],
    180 => [
        'permission' => 'affiliate_website_config_key',
        'table' => 'affiliate_website_config_keys',
        'controller' => 'AffiliateWebsiteConfigKey',
        'url' => 'affiliate-website-config-keys',
        'description' => 'affiliate website configs and settings (single language)',
        'has_crud' => true,
        'other_permissions' => [
            'edit-website-keys' => [
                'name' => 'edit-website-keys',
                'fname' => 'editWebsiteKeys',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'update-website-keys' => [
                'name' => 'update-website-keys',
                'fname' => 'updateWebsiteKeys',
                'method' => 'post',
                'permission' => null,
                'params' => ['id?'],
            ],
        ],
    ],
    181 => [
        'permission' => 'social_information',
        'table' => 'user_extras',
        'controller' => 'SocialInformation',
        'url' => 'social-information',
        'description' => 'extra information for internal users about the clients. ex: religion and interests.',
        'has_crud' => true,
    ],
    182 => [
        'permission' => 'corporate_events_report',
        'table' => 'reports',
        'controller' => 'CorporateEventsReport',
        'url' => 'corporate-events-report',
        'description' => 'corporate events report distribution dates.',
        'has_crud' => false,
        'other_permissions' => [
            'view_details' => [
                'name' => 'corporate_events_report',
                'fname' => 'corporate_events_report',
                'method' => 'get',
                'permission' => 'corporate_events_report',
                'params' => ['id'],
            ],
        ],
    ],
    183 => [
        'permission' => 'document_type',
        'table' => 'document_types',
        'controller' => 'DocumentType',
        'url' => 'document-types',
        'description' => 'document types for the requests the clients can make. ex: passport, id card, driving license etc.',
        'has_crud' => true,
    ],
    184 => [
        'permission' => 'app_relation_report',
        'table' => 'reports',
        'controller' => 'AppRelationReport',
        'url' => 'app-relation-reports',
        'description' => 'application assign history and log for the clients and their relations.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    185 => [
        'permission' => 'sub_branch',
        'table' => 'sub_branches',
        'controller' => 'SubBranch',
        'url' => 'sub-branches',
        'description' => 'sub branches for the company. ex: amman, sydney, Nairobi etc.',
        'has_crud' => true,
    ],
    186 => [
        'permission' => 'contact_branch_detail',
        'table' => 'contact_branch_details',
        'controller' => 'ContactBranch',
        'url' => 'contact-branch-details',
        'description' => 'contact branch details for the company. ex: phone, email, address etc.',
        'has_crud' => true,
    ],
    187 => [
        'permission' => 'branch',
        'table' => 'branches',
        'controller' => 'Branch',
        'url' => 'branches',
        'description' => 'branches for the company. ex: amman, sydney, Nairobi etc.',
        'has_crud' => true,
    ],
    188 => [
        'permission' => 'whitelabel_transaction',
        'table' => 'whitelabel_transactions',
        'controller' => 'WhitelabelTransactionManagement',
        'url' => 'whitelabel',
        'description' => 'whitelabel transactions requests. ex: deposit, withdrawal, internal transfer etc.',
        'has_crud' => true,
        'other_permissions' => [
            'view_history' => [
                'name' => 'transactions-history',
                'fname' => 'transactionsHistory',
                'method' => 'get',
                'permission' => null,
                'params' => null,
            ],
            'view_pending' => [
                'name' => 'pending-transactions',
                'fname' => 'pendingTransactions',
                'method' => 'get',
                'permission' => null,
                'params' => null,
            ],
            'execute' => [
                'name' => 'execute',
                'fname' => 'submitExecuteTransaction',
                'method' => 'get',
                'permission' => null,
                'params' => ['transaction_id', 'status'],
            ],
            'internal_transfer' => [
                'name' => 'internal-transfer-request',
                'fname' => 'showInternalTransferRequestForm',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'send-internal-transfer-request' => [
                'name' => 'send-internal-transfer-request',
                'fname' => 'sendInternalTransferRequest',
                'method' => 'post',
                'permission' => 'internal-transfer-request-whitelabel_transaction',
                'params' => [],
            ],
        ],
    ],
    189 => [
        'permission' => 'risk_hedging_kpi',
        'table' => 'reports',
        'controller' => 'RiskKpi',
        'url' => 'risk-reports',
        'description' => 'risk department - hedging kpi widget.',
        'has_crud' => false,
    ],
    190 => [
        'permission' => 'risk_product_kpi',
        'table' => 'reports',
        'controller' => 'RiskKpi',
        'url' => 'risk-reports',
        'description' => 'risk department - product kpi widget.',
        'has_crud' => false,
    ],
    191 => [
        'permission' => 'mt_pending_orders',
        'table' => 'reports',
        'controller' => 'MtPendingOrders',
        'url' => 'mt-pending-orders',
        'description' => 'metatrader pending market orders for the clients.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => null,
            ],
        ],
    ],
    192 => [
        'permission' => 'mt_wallet_report',
        'table' => 'reports',
        'controller' => 'MtWalletReport',
        'url' => 'mt-wallet-reports',
        'description' => 'metatrader wallet summary report for the clients.',
        'has_crud' => false,
        'other_permissions' => [
            'download-recording' => [
                'name' => 'daily-balance',
                'fname' => 'getDailyBalance',
                'method' => 'get',
                'permission' => 'view-mt_wallet_report',
                'params' => ['id'],
            ],
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    193 => [
        'permission' => 'popular_instruments',
        'table' => 'popular_instruments',
        'controller' => 'PopularInstruments',
        'url' => 'popular-instruments',
        'description' => 'instruments to show its chart on the market analysis',
        'has_crud' => true,
    ],
    194 => [
        'permission' => 'mt_group_shared',
        'table' => 'mt_groups_shared',
        'controller' => 'MtGroupShared',
        'url' => 'mt-groups-shared',
        'description' => 'metatrader shared groups lookup. this used to connect multiple partners with same existing group.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    195 => [
        'permission' => 'account_type',
        'table' => 'account_types',
        'controller' => 'AccountType',
        'url' => 'account-types',
        'description' => 'account types used to categorize instruments for the clients. ex: standard, premium, vip etc.',
        'has_crud' => true,
    ],
    196 => [
        'permission' => 'webhook_log',
        'table' => 'logs',
        'controller' => 'WebhookLog',
        'url' => 'webhook-logs',
        'description' => 'webhook logs for third-parties that shows the response of every request ex: idwise and sumsub.',
        'has_crud' => false,
    ],
    197 => [
        'permission' => 'user_archived_chats',
        'table' => 'user_archived_chats',
        'controller' => 'UserArchivedChats',
        'url' => 'user-archived-chats',
        'description' => 'user archived live chats for the clients.',
        'has_crud' => true,
        'other_permissions' => [
            'all_chats' => [
                'name' => 'all-chats',
                'fname' => 'allChats',
                'method' => 'get',
                'permission' => null,
                'params' => null,
            ],
        ],
    ],
    198 => [
        'permission' => 'flag_type',
        'table' => 'flag_types',
        'controller' => 'FlagType',
        'url' => 'flag-types',
        'description' => 'flag users for internal users. ex: proceed transaction',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    199 => [
        'permission' => 'new_license_confirmation',
        'table' => 'new_license_confirmations',
        'controller' => 'NewLicenseConfirmation',
        'url' => 'new-license-confirmations',
        'description' => 'data for users who confirm to move from SV license to seychelles license.',
        'has_crud' => true,
    ],
    200 => [
        'permission' => 'user_term_confirmation',
        'table' => 'user_term_confirmations',
        'controller' => 'UserTermConfirmation',
        'url' => 'user-term-confirmations',
        'description' => 'confirmation data for the partners who confirm the terms and conditions.',
        'has_crud' => true,
    ],
    201 => [
        'permission' => 'cron_job_logs',
        'table' => 'logs',
        'controller' => 'CronJobLog',
        'url' => 'cron-job-logs',
        'description' => 'cron job schedule logs for the system cron jobs.',
        'has_crud' => false,
    ],
    202 => [
        'permission' => 'agent_transaction_report',
        'table' => 'reports',
        'controller' => 'AgentTransactionReport',
        'url' => 'agent-transactions-reports',
        'description' => 'agent groups, allocations and their credit',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    203 => [
        'permission' => 'activity_log',
        'table' => 'logs',
        'controller' => 'ActivityLog',
        'url' => 'activity-log',
        'description' => 'activity log for the system users.',
        'has_crud' => false,
        'other_permissions' => [
            'documents-activity' => [
                'name' => 'documents-activity',
                'fname' => 'documentsActivity',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
        ],
    ],
    204 => [
        'permission' => 'material_category',
        'table' => 'material_categories',
        'controller' => 'MaterialCategory',
        'url' => 'material-categories',
        'description' => 'categories for the marketing materials to provide them to the partners',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    205 => [
        'permission' => 'material_dimension',
        'table' => 'material_dimensions',
        'controller' => 'MaterialDimension',
        'url' => 'material-dimensions',
        'description' => 'dimensions for the marketing materials to provide them to the partners. ex: 300x250, 728x90 etc.',
        'has_crud' => true,
    ],
    206 => [
        'permission' => 'marketing_material',
        'table' => 'marketing_materials',
        'controller' => 'MarketingMaterial',
        'url' => 'marketing-materials',
        'description' => 'marketing materials to provide them to the partners',
        'has_crud' => true,
    ],
    207 => [
        'permission' => 'aml_report',
        'table' => 'reports',
        'controller' => 'AmlReport',
        'url' => 'aml-reports',
        'description' => 'aml report that shows the aml report for the clients.',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    208 => [
        'permission' => 'internal_requests',
        'table' => 'internal_requests',
        'controller' => 'InternalRequest',
        'url' => 'internal-requests',
        'description' => 'internal requests for the clients to request a call from the sales team. ex: vacation',
        'has_crud' => true,
    ],
    209 => [
        'permission' => 'timezone',
        'table' => 'timezones',
        'controller' => 'Timezone',
        'url' => 'time-zones',
        'description' => 'time zones for the clients based on the country. ex: GMT+3, GMT+2 etc.',
        'has_crud' => true,
    ],
    210 => [
        'permission' => 'commission_system_config',
        'table' => 'commission_system_configs',
        'controller' => 'CommissionSystemConfig',
        'url' => 'commission-system-config',
        'description' => 'commission system configuration and settings for the commission system uses.',
        'has_crud' => true,
    ],
    211 => [
        'permission' => 'country_entity_risk_level',
        'table' => 'country_entity_risk_levels',
        'controller' => 'CountryEntityRiskLevel',
        'url' => 'country-entity-risk-levels',
        'description' => 'categories for the countries risk level. ex: high, medium, low.',
        'has_crud' => true,
    ],
    212 => [
        'permission' => 'migrate_id_application',
        'table' => 'data_migration',
        'controller' => 'MigrationIdApplication',
        'url' => 'migrate-id-applications',
        'description' => 'application migration ids - round robin based on sales manager',
        'has_crud' => false,
        'other_permissions' => [
            'store' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_id_application',
                'params' => [],
            ],
        ],
    ],
    213 => [
        'permission' => 'migrate_sales_application',
        'table' => 'data_migration',
        'controller' => 'MigrationSalesApplication',
        'url' => 'migrate-sales-applications',
        'description' => 'application migration from sales to sales - round robin based on sales manager',
        'has_crud' => false,
        'other_permissions' => [
            'store' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_sales_application',
                'params' => [],
            ],
        ],
    ],
    214 => [
        'permission' => 'dynamic_email_templates',
        'table' => 'dynamic_email_templates',
        'controller' => 'DynamicEmailTemplate',
        'url' => 'dynamic-email-templates',
        'description' => 'used to create dynamic email banners',
        'has_crud' => true,
    ],
    215 => [
        'permission' => 'dynamic_email_contents',
        'table' => 'dynamic_email_contents',
        'controller' => 'DynamicEmailContent',
        'url' => 'dynamic-email-contents',
        'description' => 'used to create the dynamic email body',
        'has_crud' => true,
        'other_permissions' => [
            'mail-key-tags' => [
                'name' => 'mail-key-tags',
                'fname' => 'mailKeyTags',
                'method' => 'get',
                'permission' => null,
                'params' => [
                    'id',
                ],
            ],
            'send-test-email' => [
                'name' => 'send-test-email',
                'fname' => 'sendTestEmail',
                'method' => 'post',
                'params' => [],
                'permission' => null,
            ],
            'preview' => [
                'name' => 'preview',
                'fname' => 'preview',
                'method' => 'put',
                'params' => ['id'],
                'permission' => null,
            ],
        ],
    ],
    216 => [
        'permission' => 'dynamic_email_tags',
        'table' => 'dynamic_email_tags',
        'controller' => 'DynamicEmailTag',
        'url' => 'dynamic-email-tags',
        'description' => 'used to create the phrases that the end user can add to an email template to make it dynamic',
        'has_crud' => true,
    ],
    217 => [
        'permission' => 'email_history',
        'table' => 'reports',
        'controller' => 'EmailHistory',
        'url' => 'emails-history',
        'description' => 'an email history for the clients.',
        'has_crud' => false,
        'other_permissions' => [
            'show-content' => [
                'name' => 'show-content',
                'fname' => 'showEmailContent',
                'method' => 'get',
                'permission' => 'view-email_history',
                'params' => [
                    'emailId',
                ],
            ],
            'clear-cache' => [
                'name' => 'clear-cache',
                'fname' => 'clearCacheForList',
                'method' => 'get',
                'permission' => 'view-email_history',
                'params' => [
                    'applicationId',
                ],
            ],
        ],
    ],
    218 => [
        'permission' => 'account_form_version',
        'table' => 'account_form_versions',
        'controller' => 'AccountFormVersion',
        'url' => 'account-form-versions',
        'description' => 'account form versions.',
        'has_crud' => true,
        'other_permissions' => [
            'affected-users' => [
                'name' => 'affected-users',
                'fname' => 'affectedUsers',
                'method' => 'get',
                'permission' => 'view-account_form_version',
                'params' => [
                    'id',
                ],
            ],
        ],
    ],
    219 => [
        'permission' => 'user_request',
        'table' => 'user_requests',
        'controller' => 'UserRequests',
        'url' => 'user-requests',
        'description' => 'metatrader accounts records for the clients',
        'has_crud' => false,
        'other_permissions' => [
            'view-internal-transfer' => [
                'name' => 'internal-transfer',
                'fname' => 'showInternalTransferForm',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'save-internal-transfer' => [
                'name' => 'save-internal-transfer',
                'fname' => 'storeInternalTransferForm',
                'method' => 'post',
                'permission' => 'internal-transfer-user_request',
                'params' => [],
            ],
            'view-register-new-account' => [
                'name' => 'register-new-account',
                'fname' => 'showRegisterNewAccountForm',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],

            'view-instrument' => [
                'name' => 'instrument',
                'fname' => 'instrument',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'save-instrument' => [
                'name' => 'save-instrument',
                'fname' => 'storeInstrument',
                'method' => 'post',
                'permission' => 'instrument-user_request',
                'params' => [],
            ],
        ],
    ],
    220 => [
        'permission' => 'migrate_disabled_id_application',
        'table' => 'data_migration',
        'controller' => 'MigrationDisableIdApplication',
        'url' => 'migrate-disabled-id-applications',
        'description' => 'Disable bulk applications',
        'has_crud' => false,
        'other_permissions' => [
            'store' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_disabled_id_application',
                'params' => [],
            ],
        ],
    ],
    221 => [
        'permission' => 'migrate_hide_id_mt_accounts',
        'table' => 'data_migration',
        'controller' => 'MigrationHideIdMtAccounts',
        'url' => 'migrate-hide-id-accounts',
        'description' => 'hide bulk mt accounts by id',
        'has_crud' => false,
        'other_permissions' => [
            'store' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_hide_id_mt_accounts',
                'params' => [],
            ],
        ],
    ],
    222 => [
        'permission' => 'migrate_delete_id_application',
        'table' => 'data_migration',
        'controller' => 'MigrationDeleteIdApplication',
        'url' => 'migration-delete-id-applications',
        'description' => 'delete bulk applications by id',
        'has_crud' => false,
        'other_permissions' => [
            'store' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_delete_id_application',
                'params' => [],
            ],
        ],
    ],
    223 => [
        'permission' => 'whitelabel_crud',
        'table' => 'whitelabels',
        'controller' => 'Whitelabel',
        'url' => 'whitelabels',
        'description' => 'whitelabels deals',
        'has_crud' => true,
    ],
    224 => [
        'permission' => 'deposits_and_withdrawals_summary',
        'table' => 'reports',
        'controller' => 'DepositsAndWithdrawalsSummary',
        'url' => 'deposits-and-withdrawals-summary',
        'description' => 'deposits and withdrawals summary report',
        'has_crud' => false,
    ],
    225 => [
        'permission' => 'tax_invoices',
        'table' => 'tax_invoices',
        'controller' => 'TaxInvoice',
        'url' => 'tax-invoices',
        'description' => 'tax invoices and reports for the company',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'return' => [
                'name' => 'return',
                'fname' => 'return',
                'method' => 'post',
                'permission' => null,
                'params' => ['id'],
            ],
            'download' => [
                'name' => 'download',
                'fname' => 'download',
                'method' => 'get',
                'permission' => 'view-tax_invoices',
                'params' => ['id'],
            ],
            'resubmit' => [
                'name' => 'resubmit',
                'fname' => 'resubmit',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
        ],
    ],
    226 => [
        'permission' => 'cronjob_configuration',
        'table' => 'cronjob_configurations',
        'controller' => 'CronjobConfigurations',
        'url' => 'cronjob-configurations',
        'description' => 'cronjob configurations for different tasks',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    227 => [
        'permission' => 'cellxpert_deals',
        'table' => 'data_view',
        'controller' => 'CellxpertDeal',
        'url' => 'cellxpert-deals',
        'description' => 'a data view from cellxpert generated deals',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    228 => [
        'permission' => 'persian_kpis',
        'table' => 'reports',
        'controller' => 'Dashboard',
        'url' => 'reports',
        'description' => 'persian kpis team acquisition and retention.',
        'has_crud' => false,
    ],
    229 => [
        'permission' => 'application_utm_data',
        'table' => 'application_utm_data',
        'controller' => 'ApplicationUtmData',
        'url' => 'application-utm-data',
        'description' => 'UTM data for marketing.',
        'has_crud' => true,
    ],
    ####################

    ####################
    300 => [
        'permission' => 'offer_term',
        'table' => 'offer_terms',
        'controller' => 'OfferTerm',
        'url' => 'offer-terms',
        'has_crud' => true,
        'description' => 'offer terms and conditions on the agreement for the commission system uses',
    ],
    301 => [
        'permission' => 'security',
        'table' => 'securities',
        'controller' => 'Security',
        'url' => 'securities',
        'description' => 'symbols categories - securities for trading and commission system uses',
        'has_crud' => true,
    ],
    302 => [
        'permission' => 'symbol',
        'table' => 'symbols',
        'controller' => 'Symbol',
        'url' => 'symbols',
        'has_crud' => true,
        'description' => 'symbols for trading and commission system uses',
    ],
    303 => [
        'permission' => 'symbol_group',
        'table' => 'symbol_groups',
        'controller' => 'SymbolGroup',
        'url' => 'symbol-groups',
        'has_crud' => true,
        'description' => 'symbol groups - categories',
    ],
    306 => [
        'permission' => 'offer_scenario',
        'table' => 'offer_scenarios',
        'controller' => 'OfferScenario',
        'url' => 'offer-scenarios',
        'has_crud' => true,
        'description' => 'offer scenarios (specifications) to be used in the commission system',
        'other_permissions' => [
            'clone' => [
                'name' => 'clone',
                'fname' => 'clone',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
        ],
    ],
    307 => [
        'permission' => 'media',
        'table' => 'media',
        'controller' => 'Media',
        'url' => 'media',
        'description' => 'media/attachments files to upload by marketing for external uses',
        'has_crud' => true,
    ],
    308 => [
        'permission' => 'app_offer',
        'table' => 'app_offers',
        'controller' => 'AppOffer',
        'url' => 'app-offers',
        'description' => 'agent pending and signed approved offers and agreements',
        'has_crud' => true,
        'other_permissions' => [
            'export-pdf' => [
                'name' => 'export-pdf',
                'fname' => 'exportPdf',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'send-offer' => [
                'name' => 'send-offer',
                'fname' => 'sendOffer',
                'method' => 'post',
                'permission' => null,
                'params' => ['id'],
            ],
            'deactivate-offer' => [
                'name' => 'deactivate-offer',
                'fname' => 'deactivateOffer',
                'method' => 'post',
                'permission' => null,
                'params' => ['id'],
            ],
            'clone' => [
                'name' => 'clone',
                'fname' => 'clone',
                'method' => 'post',
                'permission' => null,
                'params' => null,
            ],
            'change-deduct-scalping' => [
                'name' => 'change-deduct-scalping',
                'fname' => 'changeDeductScalping',
                'method' => 'post',
                'permission' => null,
                'params' => ['id', 'deduct_scalping'],
            ],
            'store-agent-category' => [
                'name' => 'store-agent-category',
                'fname' => 'storeAgentCategory',
                'method' => 'post',
                'permission' => null,
                'params' => [],
            ],
            'view-agent-category' => [
                'name' => 'view-agent-category',
                'fname' => 'viewAgentCategory',
                'method' => 'get',
                'permission' => null,
                'params' => ['app_offer_id'],
            ],
            'view-tree' => [
                'name' => 'view-tree',
                'fname' => 'tree',
                'method' => 'get',
                'permission' => null,
                'params' => ['app_offer_id'],
            ],
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    309 => [
        'permission' => 'commission_transaction',
        'table' => 'commission_transactions',
        'controller' => 'CommissionTransaction',
        'url' => 'commission-transactions',
        'description' => 'commission transactions generated to agents and their clients trades',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    310 => [
        'permission' => 'application_sales_commission',
        'table' => 'application_sales_commissions',
        'controller' => 'ApplicationSalesCommission',
        'url' => 'application-sales-commissions',
        'description' => 'sales commissions percentage for commission system uses',
        'has_crud' => true,
    ],
    311 => [
        'permission' => 'offer_scenario_detail',
        'table' => 'offer_scenario_details',
        'controller' => 'OfferScenarioDetails',
        'url' => 'offer-scenario-details',
        'description' => 'offer scenario details for commission system uses',
        'has_crud' => true,
        'other_permissions' => [
            'index-details' => [
                'name' => 'index-details',
                'fname' => 'IndexScenarioDetails',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
        ],
    ],
    312 => [
        'permission' => 'payment_gateway_equity',
        'table' => 'reports',
        'controller' => 'PaymentRequest',
        'url' => 'payment-gateway-equities',
        'description' => 'payment gateway report to expect equity for each payment gateway depends on the clients transactions',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    313 => [
        'permission' => 'offer_scenario_groups',
        'table' => 'offer_scenario_groups',
        'controller' => 'OfferScenarioGroups',
        'url' => 'offer-scenario-groups',
        'description' => 'offer scenario groups for commission system uses',
        'has_crud' => true,
    ],
    314 => [
        'permission' => 'agent_categories',
        'table' => 'agent_categories',
        'controller' => 'AgentCategories',
        'url' => 'agent-categories',
        'description' => 'agent categories for commission system uses',
        'has_crud' => true,
    ],
    315 => [
        'permission' => 'webinar-leads',
        'table' => 'webinar_users',
        'controller' => 'WebinarLead',
        'url' => 'webinars-leads',
        'description' => 'webinar leads that registered to webinars',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'webinarLeadExport',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'view-assign' => [
                'name' => 'view-assign',
                'fname' => 'viewAssignWebinarLead',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'save-assign' => [
                'name' => 'save-assign',
                'fname' => 'assignWebinarLead',
                'method' => 'put',
                'permission' => 'view-assign-webinar-leads',
                'params' => ['id'],
            ],
            'view-import' => [
                'name' => 'view-import',
                'fname' => 'viewImport',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'import' => [
                'name' => 'import',
                'fname' => 'import',
                'method' => 'put',
                'permission' => 'view-import-webinar-leads',
                'params' => [],
            ],

        ],
    ],
    317 => [
        'permission' => 'seminar-leads',
        'table' => 'seminar_users',
        'controller' => 'SeminarLead',
        'url' => 'seminars-leads',
        'description' => 'seminar leads that registered to seminars',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'seminarLeadExport',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'view-assign' => [
                'name' => 'view-assign',
                'fname' => 'viewAssignSeminarLead',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
            'save-assign' => [
                'name' => 'save-assign',
                'fname' => 'assignSeminarLead',
                'method' => 'put',
                'permission' => 'view-assign-seminar-leads',
                'params' => ['id'],
            ],
            'view-import' => [
                'name' => 'view-import',
                'fname' => 'viewImport',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'import' => [
                'name' => 'import',
                'fname' => 'import',
                'method' => 'put',
                'permission' => 'view-import-seminar-leads',
                'params' => [],
            ],

        ],
    ],
    318 => [
        'permission' => 'app_available_offer',
        'table' => 'app_available_offers',
        'controller' => 'AppAvailableOffer',
        'url' => 'app-available-offers',
        'description' => 'available offers/groups that users can trade based on it',
        'has_crud' => true,
    ],
    319 => [
        'permission' => 'client_retention_report',
        'table' => 'reports',
        'controller' => 'ClientRetentionReport',
        'url' => 'client-retention',
        'description' => 'inactive clients report for retention',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    320 => [
        'permission' => 'cellxpert-default-rules',
        'table' => 'cellxpert_default_rules',
        'controller' => 'CellxpertDefaultRule',
        'url' => 'cellxpert-default-rules',
        'description' => 'cellxpert default rules mapping for default offer',
        'has_crud' => true,
    ],
    321 => [
        'permission' => 'cellxpert-sales-rules',
        'table' => 'cellxpert_sales_rules',
        'controller' => 'CellxpertSalesRule',
        'url' => 'cellxpert-sales-rules',
        'description' => 'cellxpert sales rules mapping between account managers',
        'has_crud' => true,
    ],
    322 => [
        'permission' => 'bitcoin_address',
        'table' => 'bitcoin_addresses',
        'controller' => 'BitcoinAddress',
        'url' => 'bitcoin_address',
        'description' => 'bitcoin addresses repository generated from ingot bitcoin wallet',
        'has_crud' => true,
    ],
    324 => [
        'permission' => 'leads_report',
        'table' => 'reports',
        'controller' => 'LeadsWidget',
        'url' => 'leads-report',
        'description' => 'summary of leads and clients transactions',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
            'lead-dashboard-widget' => [
                'name' => 'lead-dashboard-widget',
                'fname' => 'leadWidget',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    326 => [
        'permission' => 'whitelabel_trading_platform',
        'table' => 'whitelabel_trading_platforms',
        'controller' => 'WhitelabelTradingPlatform',
        'url' => 'whitelabel-trading-platforms',
        'description' => 'available trading platforms for whitelabel to transfer and account opening from crm',
        'has_crud' => true,
    ],
    328 => [
        'permission' => 'migrate_mt_group',
        'table' => 'data_migration',
        'controller' => 'MigrationMtGroup',
        'url' => 'migrate-mt-groups',
        'description' => 'metatrader groups migration to move users from group to another',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_mt_group',
                'params' => [],
            ],
        ],
    ],
    329 => [
        'permission' => 'migrate_app_available_group',
        'table' => 'data_migration',
        'controller' => 'MigrationAppAvailable',
        'url' => 'migrate-app-available-groups',
        'description' => 'allowed users groups migration from metatrader group to another',
        'has_crud' => false,
        'other_permissions' => [
            'export' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_app_available_group',
                'params' => [],
            ],
        ],
    ],
    330 => [
        'permission' => 'university_college',
        'table' => 'university_colleges',
        'controller' => 'UniversityCollege',
        'url' => 'university-colleges',
        'description' => 'The university colleges. For example: medicine, engineering etc.',
        'has_crud' => true,
    ],
    331 => [
        'permission' => 'complaint',
        'table' => 'complaints',
        'controller' => 'Complaint',
        'url' => 'complaints',
        'description' => 'Complaint history for the clients.',
        'has_crud' => true,
        'other_permissions' => [
            'export' => [
                'name' => 'export',
                'fname' => 'export',
                'method' => 'get',
                'permission' => null,
                'params' => [],
            ],
        ],
    ],
    332 => [
        'permission' => 'manage_complaint',
        'table' => 'complaints',
        'controller' => 'ComplaintManagement',
        'url' => 'manage-complaints',
        'description' => 'pending complaints and support requests from the clients',
        'has_crud' => true,
    ],
    333 => [
        'permission' => 'dashboard_kpi',
        'table' => 'reports',
        'controller' => 'DashboardKPI',
        'url' => 'dashboard-kpi',
        'description' => 'Shows kpis for sales',
        'has_crud' => false,
    ],
    334 => [
        'permission' => 'instrument_categories',
        'table' => 'instrument_categories',
        'controller' => 'InstrumentCategories',
        'url' => 'instrument-categories',
        'description' => 'manage instrument categories',
        'has_crud' => true,
    ],
    335 => [
        'permission' => 'instrument_category_tiers',
        'table' => 'instrument_category_tiers',
        'controller' => 'InstrumentCategoryTiers',
        'url' => 'instrument-category-tiers',
        'description' => 'instrument category tiers',
        'has_crud' => true
    ],
    336 => [
        'permission' => 'mt_campaign_participants',
        'table' => 'mt_campaign_participants',
        'controller' => 'MtCampaignParticipants',
        'url' => 'mt-campaign-participants',
        'description' => 'MT Campaign Participants',
        'has_crud' => true ,
        'other_permissions' => [
            'updateWinner' => [
                'name' => 'update-winner',
                'fname' => 'updateWinner',
                'method' => 'get',
                'permission' => null,
                'params' => ['id'],
            ],
        ],
    ],
    337 => [
        'permission' => 'question_dependencies',
        'table' => 'question_dependencies',
        'controller' => 'QuestionDependency',
        'url' => 'question-dependencies',
        'description' => 'Question Dependencies',
        'has_crud' => true
    ],
    338 => [
        'permission' => 'migrate_remove_referral',
        'table' => 'data_migration',
        'controller' => 'MigrationRemoveReferral',
        'url' => 'migrate-remove-referrals',
        'description' => 'bulk remove referral from the clients',
        'has_crud' => false,
        'other_permissions' => [
            'store' => [
                'name' => 'store',
                'fname' => 'store',
                'method' => 'post',
                'permission' => 'view-migrate_remove_referral',
                'params' => [],
            ],
        ],
    ],
    339 => [
        'permission' => 'internal_notifications',
        'table' => 'internal_notifications',
        'controller' => 'InternalNotification',
        'url' => 'internal-notifications',
        'description' => 'internal notifications',
        'has_crud' => true,
    ],
    340 => [
        'permission' => 'existing_users',
        'table' => 'users',
        'controller' => 'ExistingUser',
        'url' => 'existing-users',
        'description' => 'Search for users by email',
        'has_crud' => false,
    ],

    #### add new routes above 300
];

return [
    'config' => $models_config,
    'admin_menu' => [
        'administration' => [
            'list' => [
                'roles' => $models_config[25],
                'websites_config' => $models_config[55],
                'websites_translatable_config' => $models_config[96],
                'api_keys' => $models_config[113],
                'commission_system_config' => $models_config[210],
                'cronjob_configurations' => $models_config[226],
                'oauth_clients' => $models_config[136],
                'user_restrictions' => $models_config[14],
                'application_types' => $models_config[4],
                'transaction_types' => $models_config[179],
                'order_types' => $models_config[125],
                'document_types' => $models_config[183],
                'affiliate_websites' => $models_config[0],
                'regulation_entities' => $models_config[63],
                'trading_platforms' => $models_config[40],
                'trading_servers' => $models_config[134],
            ],
            'icon' => 'la la-cog',
        ],
        'auditing' => [
            'list' => [
                'errors_log' => $models_config[54],
                'queue_logs' => $models_config[69],
                'payment_gateways' => $models_config[175],
                'webhooks' => $models_config[196],
                'activity_log' => $models_config[203],
                'cron_job_logs' => $models_config[201],
                'third_party_logs' => $models_config[48],
                'api_key_access_events' => $models_config[114],
            ],
            'icon' => 'las la-cloud-meatball',
        ],
        'data_management' => [
            'list' => [
                'promotions' => $models_config[124],
                'marketing_materials' => $models_config[206],
                'media_library' => $models_config[307],
                'country_auto_assign' => $models_config[49],
                'campaign_templates' => $models_config[149],
                'instruments_and_contracts' => $models_config[41],
                'market_reports' => $models_config[36],
                'index_dividends' => $models_config[17],
                'corporate_events' => $models_config[60],
                'swaps' => $models_config[29],
                'account_forms' => $models_config[43],
                'sliders' => $models_config[39],
                'promotion_sliders' => $models_config[62],
                'faqs' => $models_config[51],
                'educational_materials' => $models_config[111],
                'tutorial_videos' => $models_config[153],
                'educational_articles' => $models_config[118],
                'ib_settings' => $models_config[94],
                'company_types' => $models_config[139],
                'seos' => $models_config[46],
                'genders' => $models_config[126],
                'referrals' => $models_config[24],
                'languages' => $models_config[35],
                'countries' => $models_config[12],
                'country_entity_risk_levels' => $models_config[211],
                'time_zones' => $models_config[209],
                'currencies' => $models_config[13],
                'user_titles' => $models_config[127],
                'marital_statuses' => $models_config[128],
                'job_positions' => $models_config[130],
                'business_sectors' => $models_config[131],
                'professionalism' => $models_config[132],
                'payment_gateways' => $models_config[22],
                'websites_banking_details' => $models_config[70],
                'product_categories' => $models_config[23],
                'opportunity_options' => $models_config[65],
                'contact_request_categories' => $models_config[53],
                'institutional_services' => $models_config[78],
                'source_of_funds_questions' => $models_config[129],
                'eligibility_questions' => $models_config[7],
                'webinars' => $models_config[148],
                'campaigns' => $models_config[152],
                'classification_options' => $models_config[166],
                'application_status_tags' => $models_config[158],
                'mt_tags' => $models_config[163],
                'group_tags' => $models_config[170],
                'email_tags' => $models_config[116],
                'ticket_tags' => $models_config[146],
                'political_questions' => $models_config[171],
                'sales_mail_template' => $models_config[164],
                'securities' => $models_config[301],
                'symbol_groups' => $models_config[303],
                'symbols' => $models_config[302],
                'seminars' => $models_config[176],
                'newsroom' => $models_config[177],
                'mail_notifiers' => $models_config[178],
                'popular_instruments' => $models_config[193],
                'branches' => $models_config[187],
                'whitelabels' => $models_config[223],
                'internal_requests' => $models_config[208],
                'sales_commissions' => $models_config[310],
                'account_types' => $models_config[57],
                'bulk_sms' => $models_config[161],
                'flag_types' => $models_config[198],
                'bitcoin_wallet_repo' => $models_config[322],
                'cellxpert_default_rules' => $models_config[320],
                'cellxpert_sales_rules' => $models_config[321],
                'departments' => $models_config[138],
                'all_flag_per' => $models_config[120],
                'universities' => $models_config[91],
                'check_list' => $models_config[103],
                'glossary_terms' => $models_config[52],
                'exchangers' => $models_config[101],
                'manaf_bank_lookups' => $models_config[84],
                'manaf_bank_account_lookups' => $models_config[85],
                'dynamic_email_contents' => $models_config[215],
                'internal_notifications' => $models_config[339],
            ],
            'icon' => 'las la-database',
        ],
        'data_migration' => [
            'list' => [
                'metatrader_groups' => $models_config[328],
                'app_available_groups' => $models_config[329],
                'cellxpert_application' => $models_config[119],
                'bulk_assign_sales' => $models_config[213],
                'bulk_assign_app_ids' => $models_config[212],
                'bulk_delete_app_ids' => $models_config[222],
                'bulk_disable_apps' => $models_config[220],
                'bulk_hide_mt_accounts' => $models_config[221],
                'bulk_remove_referrals' => $models_config[338],
            ],
            'icon' => 'las la-exchange-alt',
        ],
        'data_view' => [
            'list' => [
                'cellxpert_deals' => $models_config[227],
            ],
            'icon' => 'las la-binoculars',
        ],
        'transaction_internal_system' => [
            'list' => [
                'deposit' => $models_config[173],
                'withdraw' => $models_config[172],
                'crypto' => $models_config[93],
            ],
            'icon' => 'las la-money-check-alt',
        ],
        'users_history' => [
            'list' => [
                'transactions_history' => $models_config[31],
                'orders_history' => $models_config[20],
                'tickets_history' => $models_config[59],
                'complaint_history' => $models_config[331],
                'calls_history' => $models_config[100],
                'user_login_logs' => $models_config[47],
            ],
            'icon' => 'la la-history',
        ],
        'pending_requests' => [
            'list' => [
                'pending_transactions' => $models_config[73],
                'pending_orders' => $models_config[72],
                'pending_tickets' => $models_config[75],
                'pending_complaints' => $models_config[332],
                'call_requests' => $models_config[165],
            ],
            'icon' => 'la la-stopwatch',
        ],
        'user_management' => [
            'list' => [
                'user_applications' => $models_config[71],
                'metatrader_accounts' => $models_config[74],
                'metatrader_groups' => $models_config[15],
                'app_offers' => $models_config[308],
            ],
            'icon' => 'la la-users-cog',
        ],
        'potential_leads' => [
            'list' => [
                'campaign_leads' => $models_config[102],
                'university_leads' => $models_config[92],
                'webinar_leads' => $models_config[315],
                'seminar_leads' => $models_config[317],
                'partnership_leads' => $models_config[105],
                'existing_users' => $models_config[340],

            ],
            'icon' => 'las la-user-plus',
        ],
        'contacts' => [
            'list' => [
                'my_contacts' => $models_config[67],
                'contact_us' => $models_config[11],
                'institutional_contact_requests' => $models_config[79],
                'careers' => $models_config[98],
            ],
            'icon' => 'las la-toolbox',
        ],
        'graphs_diagrams' => [
            'list' => [
                'deposits_and_withdrawals' => $models_config[224],
                'user_applications' => $models_config[81],
                'tickets' => $models_config[151],
                'user_transactions' => $models_config[83],
                'metatrader_accounts' => $models_config[82],
                'metatrader_groups' => $models_config[50],
                'market_reports' => $models_config[123],
                'kpi_dashboard' => $models_config[333],
            ],
            'icon' => 'las la-chart-bar',
        ],
        'analytics_reports' => [
            'list' => [
                'transactions_and_wallets_report' => $models_config[107],
                'mt_wallets_report' => $models_config[192],
                'deposit_and_withdrawal_report' => $models_config[142],
                'client_summary' => $models_config[147],
                'client_timeline' => $models_config[169],
                'clients_deposit_withdrawal_report' => $models_config[27],
                'agent_transaction_report' => $models_config[202],
                'agent_activity_report' => $models_config[122],
                'sales_deposit_withdrawal_report' => $models_config[162],
                'sales_summary' => $models_config[26],
                'payment_gateway_equity' => $models_config[312],
                'payment_method_report' => $models_config[143],
                'equity_report' => $models_config[86],
                'open_position_report' => $models_config[87],
                'closed_position_report' => $models_config[140],
                'liquidation_report' => $models_config[88],
                'scalping_report' => $models_config[90],
                'similar_apps' => $models_config[112],
                'portal_login_report' => $models_config[141],
                'ticket_report' => $models_config[144],
                'referral_report' => $models_config[145],
                'campaign_report' => $models_config[150],
                'expired_account_report' => $models_config[155],
                'gainers_and_losers_report' => $models_config[156],
                'index_dividends_reports' => $models_config[133],
                'mt_pending_orders' => $models_config[191],
                'corporate_events_report' => $models_config[182],
                'market_alerts' => $models_config[157],
                'commission_transaction' => $models_config[309],
                'aml_report' => $models_config[207],
                'app_relation_report' => $models_config[184],
                'symbol_prices' => $models_config[174],
                'client_retention_report' => $models_config[319],
                'leads_widget_report' => $models_config[324],
                'tax_invoices' => $models_config[225],
                'mt_campaign_participants' => $models_config[336],
            ],
            'icon' => 'las la-file-invoice-dollar',
        ],
    ],
];
