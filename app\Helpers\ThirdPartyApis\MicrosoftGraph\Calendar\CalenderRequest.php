<?php

namespace App\Helpers\ThirdPartyApis\MicrosoftGraph\Calendar;

use App\Helpers\ThirdPartyApis\MicrosoftGraph\Request;
use Carbon\CarbonInterval;
use JsonException;

class CalenderRequest extends Request
{
    /**
     * Get the available times for sales employee from Outlook calendar
     *
     * @param    $email
     * @param  array  $date
     * @param  string  $time_zone
     * @return array
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws JsonException
     * @throws \Nette\Utils\JsonException
     */
    public function getAvailableTimes($email, array $date, $time_zone = 'Asia/Amman'): array
    {
        // generate a token to access the API
        $token = $this->getBearerToken();

        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer '.$token,
            'Host' => 'graph.microsoft.com',
        ];
        $url = 'https://graph.microsoft.com/v1.0/users/'.$email.'/calendar/getSchedule';
        // execute the API to receive the available times related to the employee on outlook calendar
        $response = json_decode($this->client->post($url, [
            'headers' => $headers,
            'body' => '{
                "schedules": [
                    "'.$email.'"
                ],
                "startTime": {
                    "dateTime": "'.$date['start'].'",
                    "timeZone": "'.config('app.timezone').'"
                },
                "endTime": {
                    "dateTime": "'.$date['end'].'",
                    "timeZone": "'.config('app.timezone').'"
                },
                "availabilityViewInterval": "15"
            }',
        ])->getBody()->getContents(), false, 512, JSON_THROW_ON_ERROR);

        // create an array after getting the availability string
        $value = $response->value[0]->availabilityView;

        // split the string of available and busy times to array - response example "00120200000"
        // 0 means free - 2 means busy - 1 means not sure
        $availability = str_split($value);

        // create an array of date range based on 15 minutes interval
        $time_list = CarbonInterval::minutes(15)->toPeriod('08:30:00', '16:45:00');

        // push the generated 15 minutes interval times to an array
        $times_interval_periods = [];
        foreach ($time_list as $time) {
            $new_time = $time->setTimezone(config('app.timezone'))->format('H:i');
            if ($time_zone != config('app.timezone')) {
                $new_time = $time->setTimezone($time_zone)->format('H:i');
            }
            $times_interval_periods[] = $new_time;
        }

        // attach the availability API response to the time generated by CarbonInterval
        $times_dropdown = [];
        foreach ($times_interval_periods as $key => $value) {
            $times_dropdown[] = [
                'time' => $value,
                'status' => $availability[$key],
            ];
        }

        // Filter the times array to get the available ones only
        //  $filter_available_times = array_filter($times_dropdown, function ($var) {
        //      return ($var['status'] == '0');
        //  });

        return $times_dropdown;
    }

    /**
     * Set the event on the outlook calendar for the employee
     *
     * @param    $email
     * @param  array  $date
     * @param    $client
     * @return array
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws JsonException
     * @throws \Nette\Utils\JsonException
     */
    public function setCalendarEvent($email, array $date, $client)
    {
        // generate a token to access the API
        $token = $this->getBearerToken();

        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer '.$token,
            'Host' => 'graph.microsoft.com',
        ];
        $url = 'https://graph.microsoft.com/v1.0/users/'.$email.'/events';
        // execute the API to receive the available times related to the employee on outlook calendar
        $response = json_decode($this->client->post($url, [
            'headers' => $headers,
            'body' => '{
                      "subject": "Call Request",
                      "body": {
                        "contentType": "HTML",
                        "content": "'.$client->mainUser->first_name.' '.$client->mainUser->last_name.' requested a call with the Application ID: '.$client->id.'."
                      },
                      "start": {
                            "dateTime": "'.$date['start'].'",
                            "timeZone": "Jordan Standard Time"
                      },
                      "end": {
                            "dateTime": "'.$date['end'].'",
                            "timeZone": "Jordan Standard Time"
                      },
                      "location":{
                          "displayName":"Call request"
                      },
                      "attendees": [
                        {
                          "emailAddress": {
                            "address":"Email",
                            "name": "User"
                          },
                          "type": "required"
                        }
                      ],
                      "allowNewTimeProposals": true
                    }',
        ])->getBody()->getContents(), false, 512, JSON_THROW_ON_ERROR);

        return $response;
    }
}
