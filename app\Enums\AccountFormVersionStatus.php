<?php

namespace App\Enums;

enum AccountFormVersionStatus: string
{
    case Pending = 'pending';
    case Released = 'released';
    case Canceled = 'canceled';

    public function getBadge(): string
    {
        return match ($this) {
            self::Pending => '<span class="badge badge-warning">' . __('content.pending') . '</span>',
            self::Released => '<span class="badge badge-success">' . __('content.approved') . '</span>',
            self::Canceled => '<span class="badge badge-danger">' . __('content.rejected') . '</span>',
        };
    }
}
