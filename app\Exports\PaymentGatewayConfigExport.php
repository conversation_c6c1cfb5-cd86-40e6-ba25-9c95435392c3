<?php

namespace App\Exports;

use App\Http\Controllers\Admin\PaymentGatewayConfigController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PaymentGatewayConfigExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(PaymentGatewayConfigController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Website Name',
            'Payment Method',
            'Key',
            'Value',
            'Created Date',
            'Updated Date',
        ];
    }

    public function map($data): array
    {
        return [
            $data->affiliatewebsite?->name ?? '-',
            $data->paymentGateway->name ?? '-',
            $data->key ?? '-',
            $data->value ?? '-',
            $data->created_at ?? '-',
            $data->updated_at ?? '-',
        ];
    }
}
