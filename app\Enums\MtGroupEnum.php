<?php

namespace App\Enums;

enum MtGroupEnum: int
{
    case Active = 1;
    case Inactive = 0;

    public static function toSelectArray(): array
    {
        return [
            self::Active->value => self::Active->getLabel(),
            self::Inactive->value => self::Inactive->getLabel(),
        ];
    }

    public function getBadge(): string
    {
        return match ($this) {
            self::Active => '<span class="badge badge-success">Active</span>',
            self::Inactive => '<span class="badge badge-danger">Inactive</span>',
        };
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Active => __('content.active'),
            self::Inactive => __('content.in_active'),
        };
    }
}
