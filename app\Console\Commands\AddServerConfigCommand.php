<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TradingServer;
use App\Models\TradingPlatform;
use App\Models\TradingServerConfig;
use App\Models\AffiliateWebsite;

class AddServerConfigCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:config-server';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create or update a trading server with default configurations for all affiliate websites';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $name = $this->ask('Enter the server name', 'mt5_real_4');
        $displayName = $this->ask('Enter the display name', $name);
        $type = $this->choice('Select the server type', ['real', 'demo'], str_contains($name, 'real') ? 0 : 1);
        $prefix = $this->ask('Enter the prefix', 5);
        $limit = $this->ask('Enter the account limit', 25000);
        $platformName = $this->choice('Select the trading platform', ['mt4', 'mt5'], str_contains($name, 'mt5') ? 1 : 0);

        // Retrieve the trading platform ID
        $tradingPlatformId = TradingPlatform::query()
            ->where('name', $platformName)
            ->value('id');

        if (!$tradingPlatformId) {
            $this->error("Trading platform '{$platformName}' not found.");

            return;
        }

        // Create or update the trading server
        $server = TradingServer::query()->updateOrCreate([
            'name' => $name,
            'trading_platform_id' => $tradingPlatformId,
            'type' => $type,
        ], [
            'display_name' => $displayName,
            'prefix' => $prefix,
            'limit' => $limit,
        ]);

        $this->info("Trading server '{$name}' has been successfully created or updated (ID: {$server->id}).");

        // Get existing configuration keys based on platform and type
        $configKeys = TradingServerConfig::query()
            ->whereHas('tradingServer', function ($query) use ($tradingPlatformId, $type) {
                $query->where('trading_platform_id', $tradingPlatformId)
                      ->where('type', $type);
            })
            ->pluck('key')
            ->unique()
            ->toArray();

        if (empty($configKeys)) {
            $this->warn("No configuration keys found for platform '{$platformName}' and type '{$type}'.");

            return;
        }

        // Retrieve all affiliate website IDs
        $affiliateWebsiteIds = AffiliateWebsite::query()->pluck('id')->toArray();

        // Create or update configurations for each affiliate website
        foreach ($affiliateWebsiteIds as $websiteId) {
            foreach ($configKeys as $key) {
                $config = TradingServerConfig::query()->updateOrCreate([
                    'trading_server_id' => $server->id,
                    'affiliate_website_id' => $websiteId,
                    'key' => $key,
                ], [
                    'value' => '',
                ]);

                $action = $config->wasRecentlyCreated ? 'Created' : 'Updated';
                $this->line("{$action} config for server ID {$server->id}, website ID {$websiteId}, key '{$key}'.");
            }
        }

        $this->info('Server configuration process completed successfully.');
    }
}
