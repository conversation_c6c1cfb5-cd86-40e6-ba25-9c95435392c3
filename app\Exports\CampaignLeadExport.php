<?php

namespace App\Exports;

use App\Http\Controllers\Admin\CampaignLeadController;
use App\Models\CampaignLead;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class CampaignLeadExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(CampaignLeadController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Email',
            'Phone',
            'Birthdate',
            'City',
            'Referral',
            'Campaign name',
            'Date',
            'Application Type',
            'Status',
            'UTM Term',
            'UTM Medium',
            'UTM Source',
            'UTM Content',
            'UTM Campaign',
            'Created Date',
        ];
    }

    public function map($row): array
    {
        return [
            $row->name ?? '-',
            $row->email ?? '-',
            $row->phone ?? '-',
            $row->birthdate ?? '-',
            $row->country_name ?? '-',
            $row->referral_name ?? '-',
            $row->campaign_name ?? '-',
            $row->created_at ?? '-',
            $row->user_id ? trans('content.register') : trans('content.guest'),
            CampaignLead::$statues[$row->status] ?? '-',
            $row->term ?? '-',
            $row->medium ?? '-',
            $row->source ?? '-',
            $row->content ?? '-',
            $row->campaign ?? '-',
            $row->created_at ?? '-',
        ];
    }
}
