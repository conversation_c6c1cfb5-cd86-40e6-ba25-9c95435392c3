<?php

namespace App\Exports;

use App\Http\Controllers\Admin\MtAccountManagementController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtAccountCommissionExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData, protected array $actionParameters)
    {
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->accountCommissions(
            request: request()->merge($this->requestData),
            login_id: $this->actionParameters['login_id'],
            export: true,
        );
    }

    public function headings(): array
    {
        return [
            'Ticket',
            'Username',
            'Date',
            'Login',
            'Agent Name',
            'Agent Commission Profile',
            'Symbol',
            'Rebate Rate',
            'Volume',
            'Profit',
            'Rebate Profit',
            'Agent Account',
        ];
    }

    public function map($row): array
    {
        return [
            $row['ticket'],
            $row['user_name'],
            $row['date'],
            $row['login'],
            $row['agent_profile'],
            $row['group'],
            $row['symbol'],
            $row['rebate_rate'],
            $row['volume'],
            $row['profit'],
            $row['rebate_profit'],
            $row['agent_account'],
        ];
    }
}
