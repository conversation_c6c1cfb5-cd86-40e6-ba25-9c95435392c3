<?php

namespace App\Exports;

use App\Enums\MetaTrader4Enum;
use App\Enums\MetaTrader5Enum;
use App\Http\Controllers\Admin\MtAccountManagementController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtAccountHistoryExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private string $platform;

    private array $reasons;

    private array $actions;

    private string $type;

    public function __construct(protected string $userId, protected array $requestData, protected array $actionParameters)
    {
        $exportCollection = $this->allCollection();

        $this->platform = $exportCollection['platform'];

        if ($this->platform == 'mt5') {
            $this->reasons = MetaTrader5Enum::REASON;
            $this->actions = MetaTrader5Enum::DEAL_ACTION;
        } else {
            $this->reasons = MetaTrader4Enum::REASON;
            $this->actions = MetaTrader4Enum::DEAL_ACTION;
        }
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->accountHistory(
            request: request()->merge($this->requestData),
            login_id: $this->actionParameters['login_id'],
            export: true
        )['data'];
    }

    public function allCollection()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->accountHistory(
            request: request()->merge($this->requestData),
            login_id: $this->actionParameters['login_id'],
            export: true
        );
    }

    public function headings(): array
    {
        $headers = [];
        if ($this->platform == 'mt5') {
            $headers = [
                'Deal',
                'Open Time',
                'Type',
                'Symbol',
                'Volume',
                'Open Price',
                'S/L',
                'T/P',
                'Close Time',
                'Close Price',
                'Reason',
                'Commission',
                'Swap',
                'Entry',
                'Profit',
                'Comment',
            ];
        } elseif ($this->platform == 'mt4') {
            $headers = [
                'Ticket',
                'Symbol',
                'Action',
                'Volume',
                'Reason',
                'Close Time',
                'Close Price',
                'Profit',
            ];
        }

        return $headers;
    }

    public function map($row): array
    {
        $data = [];

        if ($this->platform == 'mt5') {
            foreach ($row as $trans) {
                $deal_type = isset($trans['deal_type']) ? $trans['deal_type'] : null;

                if ($deal_type == 'transaction') {
                    $data[] = [
                        $trans['deal'] ?? '-',
                        $trans['time'] ?? '-',
                        $this->actions[$trans['action']],
                        '',
                        '',
                        '',
                        '',
                        '',
                        '',
                        '',
                        '',
                        '',
                        '',
                        '',
                        number_format($trans['profit'], 2, '.', '') ?? '-',
                        $trans['comment'] ?? '-',
                    ];
                } else {
                    $data[] = [
                        $trans[array_key_first($trans)]['position_id'] ?? '-',
                        $trans[array_key_first($trans)]['time'] ?? '-',
                        $this->actions[$trans[array_key_first($trans)]['action']] ?? '-',
                        $trans[array_key_first($trans)]['symbol'] ?? '-',
                        number_format($trans[array_key_first($trans)]['volume'], 2, '.', '') ?? '-',
                        number_format($trans[array_key_first($trans)]['price'], 2, '.', '') ?? '-',
                        number_format($trans[array_key_first($trans)]['price_sl'], 2, '.', '') ?? '-',
                        number_format($trans[array_key_first($trans)]['price_tp'], 2, '.', '') ?? '-',
                        $trans[array_key_last($trans)]['time'] ?? '-',
                        $trans[array_key_last($trans)]['price'] ?? '-',
                        $this->reasons[$trans[array_key_first($trans)]['reason']] ?? '-',
                        number_format($trans[array_key_first($trans)]['commission'], 2, '.', '') ?? '-',
                        number_format($trans[array_key_first($trans)]['storage'], 2, '.', '') ?? '-',
                        $trans[array_key_first($trans)]['entry'] ?? '-',
                        number_format($trans[array_key_last($trans)]['profit'], 2, '.', '') ?? '-',
                        $trans[array_key_last($trans)]['comment'] ?? '-',
                    ];
                }
            }
        } else {
            $data = [
                $row->ticket,
                $row->symbol,
                $this->actions[$row->action],
                $row->volume,
                $this->reasons[$row->reason],
                $row->close_time,
                number_format($row->close_price, 2, '.', ''),
                number_format($row->profit, 2, '.', ''),
            ];
        }

        return $data;
    }
}
