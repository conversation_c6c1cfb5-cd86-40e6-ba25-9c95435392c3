<?php

namespace App\Console\Commands;

use App\Models\Application;
use App\Models\Comment;
use App\Models\Currency;
use App\Models\MtAccount;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Webinar;
use hisorange\BrowserDetect\Exceptions\Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use ReflectionClass;

class SendTestMailTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:test-mail-templates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $email = $this->ask('Enter Receiver email', '<EMAIL>');

        $classes = [

        ];

        /**
         * Please make sure you have a records for webinar and transactions tables .
         */
        foreach ($classes as $c) {
            $classNameSpace = "\\App\\Mail\\$c";
            $reflector = new ReflectionClass($classNameSpace);

            if ($reflector->getConstructor()) {
                $constructor = $reflector->getConstructor();
                $parameters = $constructor->getParameters();
                $callArgs = $this->reflectParameters($parameters);
                $reflector = $reflector->newInstanceArgs($callArgs);
            }

            $reflector = app()->instance('\Illuminate\Contracts\Mail\Mailable', $reflector);

            Mail::to($email)->send($reflector);

            $this->info("$classNameSpace mail has been sent to $email");
        }
    }

    private function reflectParameters($parameters): array
    {
        $paramUserName = ['userObj', 'user', 'user_approve', 'account_referral', 'agent', 'userFrom', 'userTo', 'auth_user', 'userObject'];

        $user = User::query()->whereHas('userExtra')->with('userExtra')->first();

        $order = Order::find(47754);

        $amount = 10;

        $dummy = [
            'marginCallData',
        ];
        $currency = Currency::first();
        $ticket = Ticket::first();
        $comment = Comment::first();
        $webinar = Webinar::first();
        $comment->accountable_person_name = 'test';

        $account_data = [
            'source' => host_name(),
            'name' => 'test',
            'login' => 'test',
            'metatrader_password' => 'test',
            'investor_password' => 'test',
            'user_name' => 'test',
            'password' => 'test',
        ];

        $email_data = [
            'login' => 1,
            'password' => 'pass',
            'inv_password' => 'dummy',
            'server_name' => 'dummy',
            'mt_group_name' => 'dummy',
            'platform_name' => 'dummy',
        ];

        $data = [
            'transfer_from' => 'test',
            'transfer_to' => 'test',
            'transfer_amount' => 'test',
        ];

        $request_data = [
            'name' => 'name',
            'email' => 'email',
            'phone' => 'phone',
            'message' => 'message',
        ];

        $userData = [
            'name' => 'asdfcs',
            'source' => 'sdfds',
        ];

        $ticket->userData = $userData;

        $mtAccountCred = [
            'login' => 'login',
            'master_password' => 'master_password',
            'inv_password' => 'inv_password',
        ];

        $support_email = '<EMAIL>';
        $params = [];
        $app = Application::query()->first();

        $transaction = Transaction::query()
            ->whereHas('paymentGateway')
            ->whereHas('application')
            ->with(['appWallet.application.mainUser', 'application', 'paymentGateway', 'recCurrency'])->first();

        $failed_trans = Transaction::query()->whereHas('transactionType', function ($t) {
            $t->key('deposit');
        })->where('status', '-1')->with(['application', 'paymentGateway', 'recCurrency'])
            ->first();

        $wallet_trasns = Transaction::query()->where('id', 138051)
            ->whereHas('application')
            ->with(['application', 'paymentGateway', 'recCurrency'])
            ->first();

        $mt = MtAccount::query()->first();

        $match = [
            'data' => $data,
            'wallet_trasns' => $wallet_trasns,
            'failed_trans' => $failed_trans,
            'webinar' => $webinar,
            'webinar_data' => $webinar,
            'mtAccountCred' => $mtAccountCred,
            'user_name' => $user->first_name,
            'admin_name' => $user->first_name,
            'amount' => $amount,
            'account_data' => $account_data,
            'currency' => $currency->currency,
            'support_email' => $support_email,
            'order' => $order,
            'new_order' => $order,
            'report_link' => 'set any link here',
            'request' => \request(),
            'lang' => 'en',
            'source' => host_name(),
            'ticket' => $ticket,
            'ticketData' => $ticket,
            'email_data' => $email_data,
            'users' => [$user],
            'newComment' => $comment,
            'request_data' => $request_data,
            'login' => 'login',
            'userData' => $userData,
            'login_number' => 'login_number',
            'new_mam_login' => 10,
            'account_password' => 10,
            'exception' => new Exception('for test only '),
            'events' => [
                'EURUSD' => 'Dividends',
                'EURGBP' => 'Earnings',
            ],
        ];

        foreach ($dummy as $element) {
            $match[$element] = ['application_url' => 'xxxx', 'username' => 'dummy'];
        }

        foreach (['user_email', 'email'] as $element) {
            $match[$element] = $user->email;
        }

        foreach ($paramUserName as $element) {
            $match[$element] = $user;
        }

        foreach (['new_data', 'old_data', 'object'] as $element) {
            $match[$element] = ['icon' => ''];
        }

        foreach (['application', 'app'] as $element) {
            $match[$element] = $app;
        }

        foreach (['clientApplications'] as $element) {
            $apps = $app->toArray();
            $apps['application_url'] = 'localhost';
            $apps['username'] = 'user';
            $match[$element] = [$apps];
        }

        foreach (['transaction', 'trans'] as $element) {
            $match[$element] = $transaction;
        }

        foreach (['mt_account', 'mam_account', 'client_account'] as $element) {
            $match[$element] = $mt;
        }

        foreach ([
            'categories_of_clients',
            'reminder_arr',
            'accountsData',
            'tranactions',
            'bigDepositLoginArr',
            'applications',
            'mtPositionsArr',
            'accountsData',
            'upcomingEvents',
            'orders',
            'equitysummary',
            'mtLiquidationArr',
            'leads',
            'mtDealsArr',
            'silent_clients',
        ] as $element) {
            $match[$element] = [];
        }

        foreach ($parameters as $parameter) {
            $params[$parameter->name] = $match[$parameter->name] ?? 'xXxXxXx';
            if ($parameter->isDefaultValueAvailable()) {
                $params[$parameter->name] = $parameter->getDefaultValue();
            }
        }

        return $params;
    }
}
