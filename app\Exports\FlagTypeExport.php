<?php

namespace App\Exports;

use App\Http\Controllers\Admin\FlagTypeController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class FlagTypeExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(FlagTypeController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'user name',
            'title',
            'comment',
            'commenter',
            'created date',
            'last update date',
        ];
    }

    public function map($data): array
    {
        return [
            $data->commentable->mainUser->full_name ?? '-',
            $data->title ?? '-',
            $data->comment ?? '-',
            $data->commenter->full_name ?? '-',
            $data->created_at ?? '-',
            $data->updated_at ?? '-'
        ];
    }
}
