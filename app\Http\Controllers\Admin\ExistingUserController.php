<?php
namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class ExistingUserController extends Controller
{
    private $user;

    public function __construct()
    {
        $this->user = auth()->user();
    }

    public function index(Request $request)
    {
        $users = User::query();

        if ($request->filled('email')) {
            $users->where('email', 'like', '%' . $request->get('email') . '%');
        }

        $users = $users->latest('id')->with([
            'application.user',
            'country',
            'role',
        ])->paginate();


        return view('templates.crm.admin.existing_users.index', compact('users', 'request'));
    }
}
