name: Analyze and Create Release

on:
  workflow_dispatch:
  push:
    branches:
      - main
    #paths-ignore:
    #  - '.github/**'

jobs:
  updateVersions:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.vers.outputs.version }}
      
    steps:
      - name: Increment Version
        uses: MCKanpolat/auto-semver-action@1.0.11
        id: vers
        with:
          releaseType: patch
          incrementPerCommit: false
          github_token: ${{ secrets.GITHUB_TOKEN }}

 # analyze:
 #   needs: [updateVersions]
 #   name: Analyze
 #   runs-on: sonar-cli
    
 #   steps:
 #     - uses: actions/checkout@v4
 #       with:
 #         fetch-depth: 0 
 #     - uses: sonarsource/sonarqube-scan-action@master
 #       env:
 #         SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
 #         SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
 #     - uses: sonarsource/sonarqube-quality-gate-action@master
 #       timeout-minutes: 5
 #       env:
 #         SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  releaseGithub:
    needs: [updateVersions]
    runs-on: ubuntu-latest
    steps:
      - name: Create Github Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.updateVersions.outputs.version }}
          release_name: Release ${{ needs.updateVersions.outputs.version }}
          body: |
            Auto Release ${{ needs.updateVersions.outputs.version }}
          draft: false
          prerelease: false
