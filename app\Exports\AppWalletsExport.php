<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use App\Http\Controllers\Admin\WalletReportController;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AppWalletsExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    protected $total_transactions = [];

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(WalletReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        $headings = [
            'App ID',
            'Username',
            'Entity',
            'Email',
            'Client Type',
            'Current Currency',
            'Current Balance',
        ];

        if (array_key_exists('wallet_at', $this->requestData)) {
            array_push(
                $headings,
                'Balance at selected date',
                'Currency at selected date',
            );
        }

        array_push(
            $headings,
            'First Deposit Amount',
            'Acquisition Manager',
            'Retention Manager',
            'Created Date',
            'Updated Date',
        );

        return $headings;
    }

    public function map($wallet): array
    {
        $data = [
            $wallet->application->appIdentifierId(),
            $wallet->application->mainUser->full_name ?? '-',
            $wallet->application->getRegulatedEntity() ?? '-',
            $wallet->application->mainUser->email ?? '-',
            $wallet->application->applicationClassification?->classificationOption?->display_name ?? '-',
            $wallet->currency->code ?? '-',
            $wallet->balance,
        ];

        if (array_key_exists('wallet_at', $this->requestData)) {
            $walletDailiesAt = $wallet->walletDailiesAt;
            array_push(
                $data,
                $walletDailiesAt?->balance ?? 'N/A',
                $walletDailiesAt?->currency?->code ?? 'N/A',
            );
        }

        array_push(
            $data,
            number_format($wallet->application->firstDeposit->rec_amount ?? 0, 4, '.', ''),
            $wallet->application->getAppAcquisitionManagerName() ?? '-',
            $wallet->application->getAppRetentionManagerName() ?? '-',
            $wallet->created_at->toDateTimeString(),
            $wallet->updated_at->toDateTimeString()
        );

        return $data;
    }
}
