<?php

namespace App\Helpers\ThirdPartyApis\MicrosoftGraph\OutlookMail;

use App\Helpers\ThirdPartyApis\MicrosoftGraph\Request;
use JetBrains\PhpStorm\NoReturn;
use Nette\Utils\Json;

class OutlookRequest extends Request
{
    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Nette\Utils\JsonException
     */
    #[NoReturn]
    public function sendMessage(array $body, $sender)
    {
        $token = 'Bearer '.$this->getBearerToken();

        $headers = [
            'Authorization' => $token,
            'Content-Type' => 'application/json',
        ];

        $url = 'https://graph.microsoft.com/v1.0/users/'.$sender.'/sendMail';

        $this->client->post($url, [
            'body' => Json::encode($body),
            'headers' => $headers,
            'verify' => false,
        ])->getBody()->getContents();
    }
}
