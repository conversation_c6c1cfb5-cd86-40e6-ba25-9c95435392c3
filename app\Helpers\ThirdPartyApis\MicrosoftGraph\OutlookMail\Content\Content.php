<?php

namespace App\Helpers\ThirdPartyApis\MicrosoftGraph\OutlookMail\Content;

abstract class Content
{
    protected $contentType;

    protected $content;

    abstract public function __construct($content);

    /**
     * @return mixed
     */
    public function getContentType()
    {
        return $this->contentType;
    }

    /**
     * @return mixed
     */
    public function getContent()
    {
        return $this->content;
    }
}
