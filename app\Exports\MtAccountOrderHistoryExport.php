<?php

namespace App\Exports;

use App\Enums\MetaTrader5Enum;
use App\Http\Controllers\Admin\MtAccountManagementController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtAccountOrderHistoryExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private array $states;

    private array $types;

    private array $reasons;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->reasons = MetaTrader5Enum::REASON;
        $this->types = MetaTrader5Enum::ORDER_TYPES;
        $this->states = MetaTrader5Enum::STATES;
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->accountOrdersHistory(
            request: request()->merge($this->requestData),
            login_id: request()->route('login_id'),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Order',
            'Symbol',
            'Reason',
            'Time',
            'Type',
            'Volume',
            'Price',
            'Current Price',
            'State',
        ];
    }

    public function map($row): array
    {
        return [
            $row->order,
            $row->symbol,
            $this->reasons[$row->reason] ?? '-',
            $row->time_setup,
            $this->types[$row->type] ?? '-',
            $row->volume_initial ? $row->volume_initial / 10000 : '-',
            $row->order_price,
            $row->current_price,
            $this->states[$row->state] ?? '-',
        ];
    }
}
