<?php

namespace App\Exports;

/**
 * manager for export methods ex: pdf , excel , docx ... etc
 */
abstract class ExportManger
{
    protected string $fileName;

    protected array|object $data = [];

    public function __construct(protected string $extension)
    {
    }

    /**
     * @param  array|object  $data
     * @return $this
     */
    public function setData(array|object $data): static
    {
        $this->data = $data;

        return $this;
    }

    /**
     * @param  string  $name
     * @return $this
     */
    public function setFileName(string $name): static
    {
        $this->fileName = str_replace([' ', '_'], '-', $name).'.'."{$this->extension}";

        return $this;
    }

    /**
     * @return mixed
     */
    public function render(): mixed
    {
        return $this->template();
    }

    /**
     * @return mixed
     */
    abstract protected function template(): mixed;
}
