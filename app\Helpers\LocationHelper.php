<?php

namespace App\Helpers;

use App\Models\ErrorLog;
use Exception;
use Illuminate\Support\Facades\Cache;

class LocationHelper
{
    /**
     * Get the location data based on a given IP address
     * https://app.ipgeolocation.io/
     *
     * @param [string] $ip
     * @return mixed
     */
    public static function get($ip)
    {
        try {
            $ip = trim($ip);

            if (Cache::has('geo_information-'.$ip)) {
                $result = Cache::get('geo_information-'.$ip);
            } else {
                $apiKey = config('services.ip_geolocation.api_key');
                $url = 'https://api.ipgeolocation.io/ipgeo?apiKey='.$apiKey.'&ip='.$ip;
                $cURL = curl_init();

                if (is_local_env()) {
                    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, !is_local_env());
                }

                curl_setopt($cURL, CURLOPT_URL, $url);
                curl_setopt($cURL, CURLOPT_HTTPGET, true);
                curl_setopt($cURL, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($cURL, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Accept: application/json',
                ]);

                $result = curl_exec($cURL);

                curl_close($cURL);

                $result = json_decode($result ?? []);

                // Cache 3 hours
                Cache::put('geo_information-'.$ip, $result, 86400);
            }

            return $result;
        } catch (Exception $e) {
            ErrorLog::query()->create([
                'user_id' => auth()?->user()?->id ?? null,
                'application_id' => auth()?->user()?->application_id ?? null,
                'table_name' => 'LocationHelper > get',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }

        return '';
    }
}
