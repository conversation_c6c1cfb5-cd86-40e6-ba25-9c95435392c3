<?php

namespace App\Exports;

use App\Http\Controllers\Admin\DepositWithdrawalReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class DepositWithdrawalExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(DepositWithdrawalReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'App ID',
            'Username',
            'Country',
            'Ref. ID',
            'Transaction Type',
            'Payment Method',
            'Amount',
            'Currency',
            'USD Amount',
            'Current Wallet Balance',
            'Acquisition Manager',
            'Retention Manager',
            'Referral Name',
            'Created At',
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId(),
            $row->user->getUserHolderName(),
            $row->user->country->name ?? '-',
            $row->id ?? '-',
            $row->transaction_type_name ?? '-',
            $row->payment_gateway_name ?? '-',
            $row->rec_amount ?? '-',
            $row->rec_currency_code ?? '-',
            $row->usd_amount ?? '-',
            $row->application->mainAppWallet->balance ?? '-',
            $row->application->getAppAcquisitionManagerName() ?? '-',
            $row->application->getAppRetentionManagerName() ?? '-',
            $row->application->getAppParentName() ?? '-',
            $row->created_at ?? '-',
        ];
    }
}
