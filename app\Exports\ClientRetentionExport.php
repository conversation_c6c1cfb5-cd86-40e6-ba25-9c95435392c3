<?php

namespace App\Exports;

use App\Http\Controllers\Admin\ClientRetentionReportController;
use App\Models\Transaction;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ClientRetentionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(ClientRetentionReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'App ID',
            'Phone Number',
            'Email',
            'Age',
            'Birthdate',
            'Last Deposit',
            'Last Withdraw',
            'Entity',
            'Name',
            'Total_deposits',
            'Total Withdrawals',
            'Last Deposit Date',
            'Application Created Date',
            'Portal Logins',
            'Last Login',
            'Total Tickets',
        ];
    }

    public function map($row): array
    {
        return [
            $row->id ?? '-',
            $row->mainUser->phone ?? '-',
            $row->mainUser->email ?? '-',
            Carbon::parse($row->mainUser?->birthdate)->age ? Carbon::parse($row->mainUser?->birthdate)->age . ' ' . trans('content.years_old') : '-',
            $row->mainUser?->birthdate ?? '-',
            $row->lastDepositDetails() ?? '-',
            $row->lastWithdrawalDetails() ?? '-',
            $row->getRegulatedEntity() ?? '-',
            $row->mainUser?->getUserHolderName() ?? '-',
            $row->totalTransactions(Transaction::DEPOSIT, Transaction::DEPOSIT_CORRECTION) .' '. $row?->mainAppWallet?->currency?->code,
            $row->totalTransactions(Transaction::WITHDRAW, Transaction::WITHDRAWAL_CORRECTION) .' '. $row?->mainAppWallet?->currency?->code ,
            $row->lastDeposit?->created_at->format('Y/m/d') ?? '-',
            $row->created_at->diffForHumans() ?? '-',
            $row->user_login_log_count . ' ' . trans('content.times') ?? '-',
            $row->latest_login_created_at ? Carbon::parse($row?->latest_login_created_at)->diffForHumans() : '-',
            $row->tickets_count ?? '-',
        ];
    }
}
