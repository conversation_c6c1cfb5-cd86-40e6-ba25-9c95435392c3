<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use App\Http\Controllers\Admin\MtAgentRestrictionController;

class MtAgentRestrictionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAgentRestrictionController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'App. ID',
            'Entity',
            'IB Name',
            'Client deposit',
            'Client withdrawal',
            'Client transfer',
            'Client Acc Creation',
            'Client transfer to IB wallet',
            'Agent internal transfer',
            'Agent transfer to client MT ',
            'Agent transfer to client wallet',
            'Exceptions',
            'Country',
            'Citizenship',
            'Email',
            'Acquisition Manager',
            'Parent Name',
            'Last Login Date',
            'Reg. Date'
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId() ?? '-',
            $row->application->getRegulatedEntity() ?? 'All Entities',
            $row->application->mainUser?->full_name,
            $row->client_can_deposit ? 'No' : 'Yes',
            $row->client_can_withdraw ? 'No' : 'Yes',
            $row->client_can_fund_transfer ? 'No' : 'Yes',
            $row->client_can_open_trading_account ? 'No' : 'Yes',
            $row->Client_can_fund_transfer_to_agent_wallet ? 'Yes' : 'No',
            $row->ib_can_fund_transfer ? 'No' : 'Yes',
            $row->ib_can_fund_transfer_to_client_mt ? 'Yes' : 'No',
            $row->ib_can_fund_transfer_to_client_wallet ? 'Yes' : 'No',
            $row->exception_app_ids ? count($row->exception_app_ids) : 0,
            $row->application->mainUser?->country?->name ?? '-',
            $row->application->mainUser?->userCitizenship->name ?? '-',
            $row->application->mainUser?->email ?? '-',
            $row->application->getAppAcquisitionManagerName() ?? '-',
            $row->application->getAppParentName() ?? '-',
            $row->application->latestLogin->created_at ?? 'Never logged in',
            $row->application->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
