<?php

namespace App\Exports;

use App\Http\Controllers\Admin\SourceOfFundController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class SourceOfFundQuestionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(SourceOfFundController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Answers',
            'Type',
            'Website',
            'Created Date',
            'Updated Date'
        ];
    }

    public function map($data): array
    {
        return [
            $data->name,
            implode(', ', (array) $data->answers->pluck('name')->toArray()) ?? '-',
            $data->type,
            $data->affiliateWebsite->name ?? '-',
            $data->created_at,
            $data->updated_at
        ];
    }
}
