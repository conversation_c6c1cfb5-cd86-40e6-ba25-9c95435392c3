<?php

namespace App\Exports;

use App\Http\Controllers\Admin\MtAccountManagementController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtAccountTransactionExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private string $platform;

    private array $actions;

    private array $reasons;

    public function __construct(protected string $userId, protected array $requestData, protected array $actionParameters)
    {
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->accountTransactions(
            request: request()->merge($this->requestData),
            login_id: $this->actionParameters['login_id'],
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Ticket',
            'Time',
            'Transaction',
            'Amount',
        ];
    }

    public function map($row): array
    {
        return [
            $row->ticket,
            $row->time,
            $row->comment,
            $row->profit,
        ];
    }
}
