<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class EquityExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected $exportCollection, protected array $request)
    {
    }

    public function headings(): array
    {
        return [
            'Username',
            'Login',
            'Platform',
            'Currency',
            'Margin',
            'Margin Free',
            'Equity',
            'Balance',
            'Credit',
            'Wallet Balance',
            'Wallet Currency',
        ];
    }

    public function map($account): array
    {
        return [
            $account['USER_NAME'],
            $account['LOGIN'],
            $account['PLATFORM'],
            $account['CURRENCY'],
            $account['MARGIN'],
            $account['MARGIN_FREE'],
            $account['EQUITY'],
            $account['BALANCE'],
            $account['CREDIT'],
            $account['WALLET_BALANCE'],
            $account['WALLET_CURRENCY'],
        ];
    }

    public function collection()
    {
        return $this->exportCollection;
    }
}
