<?php

namespace App\Exports;

use App\Http\Controllers\Admin\ScalpingReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ScalpingExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function headings(): array
    {
        return [
            'login',
            'platform',
            'ticket',
            'position_id',
            'symbol',
            'open_time',
            'open_price',
            'close_time',
            'close_price',
            'volume',
            'profit',
            'action',
            'period',
        ];
    }

    public function map($row): array
    {
        return [
            $row['login'],
            $row['platform'],
            $row['ticket'],
            $row['position_id'],
            $row['symbol'],
            $row['open_time'],
            $row['open_price'],
            $row['close_time'],
            $row['close_price'],
            $row['volume'],
            $row['profit'],
            $row['action'],
            $row['period'],
        ];
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(ScalpingReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }
}
