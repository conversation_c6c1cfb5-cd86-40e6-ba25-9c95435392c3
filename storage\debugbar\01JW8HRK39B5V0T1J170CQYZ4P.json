{"__meta": {"id": "01JW8HRK39B5V0T1J170CQYZ4P", "datetime": "2025-05-27 12:39:16", "utime": **********.713248, "method": "GET", "uri": "/en/login", "ip": "::1"}, "php": {"version": "8.2.0", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[12:39:16] LOG.warning: urlencode(): Passing null to parameter #1 ($string) of type string is deprecated in D:\\projects\\ingotbrokers\\app\\Helpers\\IngotHelper.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.633071, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.217979, "end": **********.713262, "duration": 0.4952831268310547, "duration_str": "495ms", "measures": [{"label": "Booting", "start": **********.217979, "relative_start": 0, "end": **********.595139, "relative_end": **********.595139, "duration": 0.*****************, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.59515, "relative_start": 0.*****************, "end": **********.713264, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.599178, "relative_start": 0.****************, "end": **********.60251, "relative_end": **********.60251, "duration": 0.003331899642944336, "duration_str": "3.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.612508, "relative_start": 0.****************, "end": **********.712547, "relative_end": **********.712547, "duration": 0.*****************, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "11MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 5, "nb_templates": 5, "templates": [{"name": "1x templates.site.global.auth.login", "param_count": null, "params": [], "start": **********.613775, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/site/global/auth/login.blade.phptemplates.site.global.auth.login", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fsite%2Fglobal%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.site.global.auth.login"}, {"name": "1x components.auth-login", "param_count": null, "params": [], "start": **********.626389, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/components/auth-login.blade.phpcomponents.auth-login", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Fcomponents%2Fauth-login.blade.php&line=1", "ajax": false, "filename": "auth-login.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.auth-login"}, {"name": "1x templates.site.layout", "param_count": null, "params": [], "start": **********.648173, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/site/layout.blade.phptemplates.site.layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fsite%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.site.layout"}, {"name": "1x templates.site.base", "param_count": null, "params": [], "start": **********.659805, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/site/base.blade.phptemplates.site.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fsite%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.site.base"}, {"name": "1x templates.pub.meta", "param_count": null, "params": [], "start": **********.664959, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/pub/meta.blade.phptemplates.pub.meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fpub%2Fmeta.blade.php&line=1", "ajax": false, "filename": "meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.pub.meta"}]}, "route": {"uri": "GET en/login", "middleware": "web, localeSessionRedirect, localizationRedirect, localeViewPath, setUtmDataToCookie, guest", "template": "site", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "/en", "as": "login", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/LoginController.php:51-54</a>"}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0071200000000000005, "accumulated_duration_str": "7.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `iso` from `affiliate_websites` where (`name` = 'portal.ingotbrokers.local' or `site_name` = 'portal.ingotbrokers.local' or `crm_name` = 'portal.ingotbrokers.local' or json_contains(`aliases`, '\\\"portal.ingotbrokers.local\\\"')) and `affiliate_websites`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["portal.ingotbrokers.local", "portal.ingotbrokers.local", "portal.ingotbrokers.local", "\"portal.ingotbrokers.local\""], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 320}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6072268, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Helper.php:320", "source": {"index": 14, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHelpers%2FHelper.php&line=320", "ajax": false, "filename": "Helper.php", "line": "320"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `affiliate_websites` where (`name` = 'portal.ingotbrokers.local' or `site_name` = 'portal.ingotbrokers.local' or `crm_name` = 'portal.ingotbrokers.local' or json_contains(`aliases`, '\\\"portal.ingotbrokers.local\\\"')) and `affiliate_websites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["portal.ingotbrokers.local", "portal.ingotbrokers.local", "portal.ingotbrokers.local", "\"portal.ingotbrokers.local\""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 285}], "start": **********.6344578, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "AffiliateWebsite.php:60", "source": {"index": 16, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=60", "ajax": false, "filename": "AffiliateWebsite.php", "line": "60"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `regulation_entities` left join ( SELECT  translations.object_id ,\nJSON_OBJECTAGG(translations.field_name, translations.value) as fields\nFROM translations\nWHERE translations.model_name = 'RegulationEntity'\nAND translations.locale = 'en'\nAND translations.value IS NOT NULL\nGROUP BY translations.object_id\n) as regulation_entities_trans on `regulation_entities_trans`.`object_id` = `regulation_entities`.`id` where exists (select * from `affiliate_websites` where `regulation_entities`.`id` = `affiliate_websites`.`regulation_entity_id` and `iso` = 'global' and `affiliate_websites`.`deleted_at` is null) and `regulation_entities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["global"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 122}, {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 92}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.639508, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:122", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FAppServiceProvider.php&line=122", "ajax": false, "filename": "AppServiceProvider.php", "line": "122"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `regulation_entities` left join ( SELECT  translations.object_id ,\nJSON_OBJECTAGG(translations.field_name, translations.value) as fields\nFROM translations\nWHERE translations.model_name = 'RegulationEntity'\nAND translations.locale = 'en'\nAND translations.value IS NOT NULL\nGROUP BY translations.object_id\n) as regulation_entities_trans on `regulation_entities_trans`.`object_id` = `regulation_entities`.`id` where exists (select * from `affiliate_websites` where `regulation_entities`.`id` = `affiliate_websites`.`regulation_entity_id` and `affiliate_websites`.`deleted_at` is null) and `regulation_entities`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 127}, {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 92}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.642658, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:127", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FAppServiceProvider.php&line=127", "ajax": false, "filename": "AppServiceProvider.php", "line": "127"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `affiliate_websites` where `affiliate_websites`.`regulation_entity_id` in (1, 2, 3, 6, 7) and `affiliate_websites`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 127}, {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.645628, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:127", "source": {"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FAppServiceProvider.php&line=127", "ajax": false, "filename": "AppServiceProvider.php", "line": "127"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where json_contains(`websites`, '\\\"3\\\"') and (`abbreviation` in ('en', 'ar', 'es', 'fa', 'vi', 'pt')) and `languages`.`deleted_at` is null limit 9223372036854775807", "type": "query", "params": [], "bindings": ["\"3\"", "en", "ar", "es", "fa", "vi", "pt"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, {"index": 16, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.650015, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:117", "source": {"index": 15, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=117", "ajax": false, "filename": "IngotTrait.php", "line": "117"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `translations` where (`model_name` = 'Language' and `locale` = 'en') and `translations`.`object_id` in (1, 2, 7, 11, 15) and `translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["Language", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, {"index": 21, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.652765, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:117", "source": {"index": 20, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=117", "ajax": false, "filename": "IngotTrait.php", "line": "117"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where `languages`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 72}, {"index": 17, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 19, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 39}], "start": **********.66136, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:138", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FBaseRepository.php&line=138", "ajax": false, "filename": "BaseRepository.php", "line": "138"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where `languages`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 72}, {"index": 17, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 19, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 39}], "start": **********.6661022, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:138", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FBaseRepository.php&line=138", "ajax": false, "filename": "BaseRepository.php", "line": "138"}, "connection": "ingot-multiserver", "explain": null}]}, "models": {"data": {"App\\Models\\Language": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\AffiliateWebsite": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=1", "ajax": false, "filename": "AffiliateWebsite.php", "line": "?"}}, "App\\Models\\RegulationEntity": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRegulationEntity.php&line=1", "ajax": false, "filename": "RegulationEntity.php", "line": "?"}}}, "count": 51, "is_counter": true}, "livewire": {"data": {"auth-login #vIFbnXA0Y3Y0bA5irSI9": "array:5 [\n  \"data\" => array:3 [\n    \"email\" => null\n    \"password\" => null\n    \"remember\" => false\n  ]\n  \"name\" => \"auth-login\"\n  \"view\" => \"components.auth-login\"\n  \"component\" => \"App\\Http\\Components\\AuthLogin\"\n  \"id\" => \"vIFbnXA0Y3Y0bA5irSI9\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jNoKQ33cbT4o6hD2U5Bvwk9jmj6mhdAJHkwgkJRj", "locale": "en"}, "request": {"data": {"status": "200 OK", "full_url": "http://portal.ingotbrokers.local/en/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "uri": "GET en/login", "template": "site", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "/en", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/LoginController.php:51-54</a>", "middleware": "web, localeSessionRedirect, localizationRedirect, localeViewPath, setUtmDataToCookie", "telescope": "<a href=\"http://portal.ingotbrokers.local/_debugbar/telescope/9f02af0d-da65-4cb8-82d9-82717913e594\" target=\"_blank\">View in Telescope</a>", "duration": "495ms", "peak_memory": "12MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-379231757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-379231757\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-11956037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-11956037\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-980723353 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">portal.ingotbrokers.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-AU,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980723353\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1440982174 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1440982174\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-224921982 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 09:39:16 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224921982\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2144646879 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jNoKQ33cbT4o6hD2U5Bvwk9jmj6mhdAJHkwgkJRj</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144646879\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://portal.ingotbrokers.local/en/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm"}, "badge": null}}