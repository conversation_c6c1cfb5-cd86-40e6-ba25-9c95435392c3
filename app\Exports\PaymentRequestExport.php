<?php

namespace App\Exports;

use App\Http\Controllers\Admin\PaymentRequestController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PaymentRequestExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(PaymentRequestController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Payment Method',
            'Total Deposits',
            'Total Withdrawals',
            'Equity'
        ];
    }

    public function map($row): array
    {
        return [
            $row->name,
            number_format($row->total_deposits_sum_usd_amount, 2),
            number_format($row->total_withdrawals_sum_usd_amount, 2),
            number_format($row->total_deposits_sum_usd_amount - $row->total_withdrawals_sum_usd_amount, 2)
        ];
    }
}
