<?php

namespace App\Exports;

use App\Http\Controllers\Admin\AppRelationReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AppRelationHistoryExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(AppRelationReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID Application',
            'Name',
            'Email',
            'Country',
            'App Type',
            'Entity',
            'Reg. Date',
            'Referral',
            'Acquisition Manager',
            'Retention Manager',
            'Assigned Date',
            'Assigned By',
            'Comment',
        ];
    }

    public function map($row): array
    {
        $appRelationInfo = [];

        // Initialize application relation info with primary application data.
        $primaryApplicationInfo = [
            $row->appIdentifierId() ?? null,
            $row->mainUser?->getUserHolderName() ?? null,
            $row->mainUser?->email ?? null,
            $row->mainUser?->getUserCountry() ?? null,
            $row->appType->display_name ?? null,
            $row->getRegulatedEntity() ?? null,
            $row->created_at?->format('Y-m-d H:i:s') ?? null,
        ];

        // Process application relation logs.
        foreach ($row->appRelationLogs as $index => $log) {
            $logInfo = [
                $log->parentApplication->mainUser->full_name ?? null,
                $log->closerApplication->mainUser->full_name ?? null,
                $log->followerApplication->mainUser->full_name ?? null,
                $log->created_at?->format('Y-m-d H:i:s') ?? null,
                $log->executedBy?->full_name ?? 'System',
                $log->comment ?? null,
            ];

            // Merge the first log info with the primary application info.
            if ($index === 0) {
                $appRelationInfo[] = array_merge($primaryApplicationInfo, $logInfo);
            } else {
                $appRelationInfo[] = array_merge(array_pad([], count($primaryApplicationInfo), null), $logInfo);
            }
        }

        // add empty line
        $appRelationInfo[] = [];

        return $appRelationInfo;
    }
}
