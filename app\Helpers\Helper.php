<?php

use App\Models\User;
use App\Models\Country;
use App\Models\ErrorLog;
use App\Models\Language;
use Illuminate\Support\Str;
use App\Helpers\LocationHelper;
use App\Models\AffiliateWebsite;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use App\Enums\UserPreferencesKeysEnum;
use App\Models\AffiliateWebsiteConfig;
use App\Models\CommissionSystemConfig;
use Illuminate\Support\Facades\Storage;
use Illuminate\Contracts\Auth\Authenticatable;
use App\Models\AffiliateWebsiteTranslatableConfig;
use Illuminate\Contracts\View\Factory as ViewFactory;
use App\Repositories\Interfaces\LanguageRepositoryInterface;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use App\Repositories\Interfaces\UserPreferenceRepositoryInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile as SymfonyUploadedFile;
use hisorange\BrowserDetect\Parser as Browser;

if (!function_exists('arrays_are_equal')) {
    function arrays_are_equal($array1, $array2)
    {
        array_multisort($array1);
        array_multisort($array2);

        return (serialize($array1) === serialize($array2));
    }
}

if (!function_exists('array_keys_exists')) {
    function array_keys_exists(array $needle, array $haystacks)
    {
        foreach ($needle as $item) {
            if (array_key_exists($item, $haystacks)) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('array_key_index')) {
    function array_key_index($needle, array $haystack)
    {
        if (($key = array_search($needle, $haystack)) !== false) {
            return $key;
        }
    }
}

if (!function_exists('mysql_escape')) {
    function mysql_escape($inp)
    {
        if (is_array($inp)) {
            return array_map(__METHOD__, $inp);
        }

        if (!empty($inp) && is_string($inp)) {
            return str_replace(['\\', "\0", "\n", "\r", "'", '"', "\x1a"], ['\\\\', '\\0', '\\n', '\\r', "\\'", '\\"', '\\Z'], $inp);
        }

        return $inp;
    }
}

if (!function_exists('asset_local')) {
    function asset_local($path, $root = null): mixed
    {
        if ($root === null) {
            $root = http_host_name();
        }

        return app('url')->assetFrom($root, $path);
    }
}

if (!function_exists('file_upload')) {
    function file_upload(string $pathPrefix, UploadedFile $file): ?string
    {
        /**
         * @param $file
         * @param $pathPrefix
         * @return string
         * when app run on local env
         */
        $callLocalStorage = function ($file, $pathPrefix) {
            $file_name = $file->hashName();
            $pathPrefix = trim($pathPrefix, '/') . '/';
            $file->move(public_path($pathPrefix), $file_name);

            return $file_name;
        };

        /**
         * @param $file
         * @param $pathPrefix
         * @return string|null
         * when app run on production env
         */
        $callProductionStorage = function ($file, $pathPrefix) {
            $name = $file->hashName();
            $filePath = trim($pathPrefix, '/') . '/' . $name;

            $upload = Storage::put($filePath, fopen($file->getRealPath(), 'r+'));

            return $name;
        };

        if (config('filesystems.default') == 's3') {
            return $callProductionStorage($file, $pathPrefix);
        }

        return $callLocalStorage($file, $pathPrefix);
    }
}

if (!function_exists('string_mask')) {
    function string_mask(?string $text, $delimiter = '*'): string
    {
        if (is_null($text)) {
            return '';
        }

        // Remove all spaces from the text
        $text = str_replace(' ', '', $text);

        // Check if the provided text is not empty
        if (!empty($text)) {
            // Replace special characters like '#', '!', '@', '$', '%', etc. with '*'
            $specialCharacters = ['#', '!', '@', '$', '%'];
            $text = str_replace($specialCharacters, $delimiter, $text);

            // Replace characters with '*' except the first and last two characters
            $text = preg_replace('/(?!^.?)[0-9A-Za-z@-](?!(.){0,2}$)/', $delimiter, $text);
        }

        // Return the masked text or the original text if empty
        return $text;
    }
}

if (!function_exists('append_attributes')) {
    function append_attributes($obj, $extra = [])
    {
        if (!empty($obj)) {
            foreach ($extra as $key => $value) {
                $obj->$key = $value;
            }
        }

        return $obj;
    }
}

if (!function_exists('random_password')) {
    function random_password($length = 8)
    {
        // Define the characters we're going to use to generate the password
        $uppercaseLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercaseLetters = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $specialChars = '!@#$?';

        // Combine all the characters together
        $allChars = $uppercaseLetters . $lowercaseLetters . $numbers . $specialChars;

        // String to hold our generated password
        $password = '';

        // Ensure we have at least one character from each group by picking one at random
        $password .= $uppercaseLetters[random_int(0, strlen($uppercaseLetters) - 1)];
        $password .= $lowercaseLetters[random_int(0, strlen($lowercaseLetters) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $specialChars[random_int(0, strlen($specialChars) - 1)];

        // Fill the rest of the length with random choices from all characters
        for ($i = strlen($password); $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to ensure it's random
        return str_shuffle($password);
    }
}

if (!function_exists('site_config')) {
    function site_config($key = null, $affiliateName = null)
    {
        if (!$affiliateName) {
            $affiliateName = host_name();
        }

        $affiliateName = str_ireplace(['http://', 'https://'], '', $affiliateName);

        return AffiliateWebsiteConfig::getCurrentAffiliateConfig($key, $affiliateName);
    }
}

if (!function_exists('trans_config')) {
    function trans_config($key = null, $affiliateName = null, $lang = null)
    {
        if (!$affiliateName) {
            $affiliateName = host_name();
        }

        $affiliateName = str_ireplace(['http://', 'https://'], '', $affiliateName);

        return AffiliateWebsiteTranslatableConfig::getCurrentAffiliateConfig($key, $affiliateName, $lang);
    }
}

if (!function_exists('current_affiliate_config_key')) {
    function current_affiliate_config_key($key, $iso)
    {
        $value = AffiliateWebsiteConfig::getCurrentAffiliateWebsiteKey($key, $iso);
        if ($key == 'id_wise') {
            $value = match ($value) {
                'true' => true,
                default => false,
            };
        }

        return $value;
    }
}

if (!function_exists('domain_iso')) {
    function domain_iso($host = null)
    {
        if (!$host) {
            $host = request()->getHost();
        }

        return AffiliateWebsite::query()->where(function ($s) use ($host) {
            $s->where('name', $host)
                ->orWhere('site_name', $host)
                ->orWhere('crm_name', $host)
                ->orWhereJsonContains('aliases', $host);
        })->value('iso');
    }
}

if (!function_exists('direction')) {
    function direction($abbreviation = null)
    {
        if (!$abbreviation) {
            $abbreviation = locale();
        }

        return resolve(LanguageRepositoryInterface::class)
            ->getDirectionByAbbreviation($abbreviation);
    }
}

if (!function_exists('text_align')) {
    function text_align($lang = null)
    {
        if (!$lang) {
            $lang = locale();
        }

        $langDirection = direction($lang);

        if ($langDirection == 'ltr') {
            return 'left';
        } else {
            return 'right';
        }
    }
}

if (!function_exists('get_current_site')) {
    function site_current()
    {
        $affiliateName = host_name();
        $affiliateName = str_ireplace(['http://', 'https://'], '', $affiliateName);

        return AffiliateWebsite::getCurrentAffiliate($affiliateName);
    }
}

if (!function_exists('report_error')) {
    function report_error($data = null)
    {
        ErrorLog::query()->create([
            'user_id' => auth()?->id() ?? null,
            'application_id' => auth()?->user()?->application_id ?? null,
            'ip_address' => request()->ip(),
            'request' => json_encode($data['request'] ?? request()->request->all()),
            'table_name' => $data['table_name'] ?? null,
            'error' => $data['error'] ?? null,
            'line' => $data['line'] ?? null,
            'url' => $data['url'] ?? null,
            'debug' => $data['debug'] ?? null,
        ]);
    }
}

if (!function_exists('layout')) {
    function layout($view = null, $data = [], $mergeData = [])
    {
        $factory = app(ViewFactory::class);

        if (func_num_args() === 0) {
            return $factory;
        }

        $entityIsoCode = AffiliateWebsite::query()->where(function ($query) {
            $query->where('name', host_name())
                ->orWhere('site_name', host_name())
                ->orWhere('crm_name', host_name())
                ->orWhereJsonContains('aliases', host_name());
        })->pluck('iso')->first();

        if (is_local_env() && !$entityIsoCode) {
            $entityIsoCode = 'global';
        }

        $viewPath = implode('/', array_filter(['templates/site/' . $entityIsoCode, $view]));

        return $factory->make($viewPath, $data, $mergeData);
    }
}

if (!function_exists('array_to_string')) {
    function array_to_string($array, $delimiter = '/')
    {
        return implode($delimiter, array_filter($array));
    }
}

if (!function_exists('is_valid_date')) {
    function is_valid_date($date, $format = 'Y-m-d')
    {
        $d = DateTime::createFromFormat($format, $date);

        // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
        return $d && $d->format($format) === $date;
    }
}

if (!function_exists('locale')) {
    function locale()
    {
        return app()->getLocale() ?? 'en';
    }
}

if (!function_exists('supported_locales')) {
    function supported_locales()
    {
        $result = [];

        $supportedLocales = LaravelLocalization::getSupportedLocales();

        $defaultLang = config('app.fallback_locale');
        $locales = array_keys(config('app.locales'));
        array_unshift($locales, $defaultLang);

        if (!empty($supportedLocales)) {
            foreach ($supportedLocales as $abbreviation => $lang) {
                $path = resource_path('lang/' . $abbreviation);
                if (file_exists($path) && in_array($abbreviation, $locales)) {
                    $result[$abbreviation] = $lang;
                }
            }
        }

        return $result;
    }
}

if (!function_exists('site_langs')) {
    function site_langs()
    {
        $locales = supported_locales();
        $abbreviations = array_keys($locales);

        return Language::getTranslatedContent(Language::getData(new Language(), [
            'where_in_arr' => ['abbreviation' => $abbreviations],
            'with_relations' => ['translations'],
        ]), session('locale'))->pluck('name', 'abbreviation')->toArray();
    }
}

if (!function_exists('host_name')) {
    function host_name()
    {
        return request()->getHost();
    }
}

if (!function_exists('http_host_name')) {
    function http_host_name($host = null, $path = null)
    {
        if (!$host) {
            $host = request()->getHost();
        }

        $http = config('app.https_status') ? 'https' : 'http';

        return $http . '://' . $host . $path;
    }
}

if (!function_exists('ip_address')) {
    function ip_address()
    {
        $ip_address = request()->ip();

        if (is_local_env() && in_array($ip_address, ['127.0.0.1', '::1'])) {
            $geoLocation = config('location.default_data');
            if (array_key_exists('ip', $geoLocation)) {
                $ip_address = $geoLocation['ip'];
            }
        }

        if (str_contains($ip_address, ',')) {
            $ip_address = explode(',', $ip_address)[0];
        }

        return trim((string) $ip_address) ?? null;
    }
}

if (!function_exists('server_ip_address')) {
    function server_ip_address()
    {
        return request()->server->get('SERVER_ADDR') ?? null;
    }
}

if (!function_exists('remote_ip_address')) {
    function remote_ip_address(): string|false
    {
        $server = request()->server;

        $ipWithPort = $server?->get('HTTP_X_FORWARDED_FOR') ?? $server?->get('REMOTE_ADDR');

        // Testing override - remove or comment out in production
        // $ipWithPort = "*************, **************";

        if (!empty($ipWithPort)) {
            // Take the first IP if there are multiple forwarded IPs
            $ipAddress = explode(',', (string) $ipWithPort)[0];

            // Remove any port number if present (e.g., "***********:8080")
            $ipAddress = strtok($ipAddress, ':');

            return trim($ipAddress);
        }

        return false;
    }
}

if (!function_exists('is_json')) {
    function is_json($string)
    {
        if (is_array($string)) {
            return false;
        }

        json_decode($string);

        return json_last_error() === JSON_ERROR_NONE;
    }
}

if (!function_exists('implode_recursive')) {
    function implode_recursive(string $separator, array $array): string
    {
        $string = '';
        foreach ($array as $i => $a) {
            if (is_array($a)) {
                $string .= implode_recursive($separator, $a);
            } else {
                $string .= $a;
                if ($i < count($array) - 1) {
                    $string .= $separator;
                }
            }
        }

        return $string;
    }
}

if (!function_exists('geoLocation')) {
    function geoLocation($ip_address = null)
    {
        if (!$ip_address) {
            $ip_address = ip_address();
        }

        return LocationHelper::get($ip_address);
    }
}

if (!function_exists('location')) {
    function location($ip_address = null)
    {
        $result = [];

        if (!$ip_address) {
            $ip_address = ip_address();
        }

        if (is_local_env()) {
            $geo = (object) config('location.default_data');
        } else {
            $geo = geoLocation($ip_address);
        }

        if ($geo && isset($geo->country_code2)) {
            $geoCacheKey = 'geo_location_' . strtolower($geo->country_code2);

            $result = Cache::remember($geoCacheKey, 60 * 60 * 24, function () use ($geo) {
                return Country::query()->where('country_code', $geo->country_code2)->first()?->toArray() ?? [];
            });

            $timezone = $geo->time_zone;
            if (is_array($timezone)) {
                $timezone = (object) $timezone;
            }

            $result += [
                'ip' => $geo->ip,
                'continent_code' => $geo->continent_code ?? null,
                'continent_name' => $geo->continent_name ?? null,
                'country_code3' => $geo->country_code3 ?? null,
                'country_name' => $geo->country_name,
                'country_capital' => $geo->country_capital,
                'state_prov' => $geo->state_prov,
                'district' => $geo->district,
                'city_name' => $geo->city,
                'zip_code' => $geo->zipcode,
                'latitude' => $geo->latitude,
                'longitude' => $geo->longitude,
                'is_eu' => $geo->is_eu,
                'calling_code' => $geo->calling_code,
                'country_tld' => $geo->country_tld,
                'languages' => $geo->languages,
                'country_flag' => $geo->country_flag,
                'geoname_id' => $geo->geoname_id,
                'isp_name' => $geo->isp,
                'isp_organization' => $geo->organization,
                'connection_type' => $geo->connection_type,
                'asn' => $geo->asn,
                'currency_code' => $geo->currency->code ?? null,
                'currency_name' => $geo->currency->name ?? null,
                'time_zone' => $timezone?->name ?? null,
                'time_zone_current_time' => $timezone?->current_time ?? null,
                'time_zone_current_time_unix' => $timezone?->current_time_unix ?? null,
                'time_zone_dst_savings' => $timezone?->dst_savings ?? null,
            ];
        }

        return $result;
    }
}

if (!function_exists('get_site_id')) {
    function get_site_id()
    {
        return AffiliateWebsite::query()->where(function ($query) {
            $query->where('name', host_name())
                ->orWhere('site_name', host_name())
                ->orWhere('crm_name', host_name())
                ->orWhereJsonContains('aliases', host_name());
        })->value('id');
    }
}

if (!function_exists('collapsed_filter')) {
    function collapsed_filter()
    {
        foreach (app()->request->all() as $k => $r) {
            if ($r != null && $k != 'page') {
                return 'show';
            }
        }
    }
}

if (!function_exists('generate_qr')) {
    function generate_qr($data, $size = 400)
    {
        return 'https://api.qrserver.com/v1/create-qr-code/?size=' . $size . 'x' . $size . '&data=' . $data;
    }
}

if (!function_exists('is_local_env')) {
    function is_local_env()
    {
        return !in_array(config('app.env'), ['production', 'prod']);
    }
}

if (!function_exists('generate_string_key')) {
    function generate_string_key($string, $prefix = null)
    {
        $string = preg_replace('/[^a-zA-Z0-9_]/', '_', $string);
        $string = strtolower($string);

        return !blank($prefix) ? ($prefix . '_' . $string) : $string;
    }
}

if (!function_exists('convert_to_e164')) {
    function convert_to_e164($phoneNumber)
    {
        // Remove any non-digit characters except the leading +
        $phoneNumber = preg_replace('/[^0-9+]/', '', $phoneNumber);

        // If the number starts with 00, replace it with +
        if (preg_match('/^00/', $phoneNumber)) {
            $phoneNumber = preg_replace('/^00/', '+', $phoneNumber);
        }

        // If the number doesn't start with a +, prepend it
        if (!preg_match('/^\+/', $phoneNumber)) {
            $phoneNumber = '+' . $phoneNumber;
        }

        // Ensure the number is now in E.164 format
        if (preg_match('/^\+[1-9]\d{1,14}$/', $phoneNumber)) {
            $phoneNumber = $phoneNumber;
        }

        return $phoneNumber;
    }
}

if (!function_exists('convert_to_e164_with_hyphen')) {
    function convert_to_e164_with_hyphen($phoneNumber, $postCode)
    {
        // If the number starts with 00, replace it with +
        if (preg_match('/^00/', $postCode)) {
            $postCode = preg_replace('/^00/', '+', $postCode);
        }

        // If the number doesn't start with a +, prepend it
        if (!preg_match('/^\+/', $postCode)) {
            $postCode = '+' . $postCode;
        }

        $phoneNumber = ltrim($phoneNumber, '0');

        // Ensure the number is now in E.164 format
        if (preg_match('/^\+[1-9]\d{1,14}$/', $phoneNumber)) {
            $phoneNumber = $phoneNumber;
        }

        return $postCode.'-'.$phoneNumber;
    }
}

if (!function_exists('current_route')) {
    function current_route()
    {
        return route(Route::currentRouteName());
    }
}

if (!function_exists('commission_system_config')) {
    function commission_system_config($key = null)
    {
        if ($key) {
            return (int) CommissionSystemConfig::query()
                ->where('key', $key)
                ->first('value')
                ?->toArray()['value'] ?? null;
        }
    }
}

if (!function_exists('store_temp_file')) {
    /**
     * Store a new in memory files to storage .
     *
     * @param File|UploadedFile $file
     * @param string|null $path
     * @param string|null $name
     * @param string|null $disk
     *
     * @return bool|string
     */
    function store_temp_file(
        UploadedFile|string $file,
        string $path,
        string $name,
        ?string $disk = null
    ): bool|string {
        $symfonyFile = new SymfonyUploadedFile(
            $file,
            basename($name),
            mime_content_type($file),
            UPLOAD_ERR_OK,
            true
        );
        $fileUpload = new UploadedFile(
            $symfonyFile->getPathname(),
            $symfonyFile->getClientOriginalName(),
            $symfonyFile->getClientMimeType(),
            $symfonyFile->getError(),
            true
        );

        return file_upload(
            $path,
            $fileUpload
        );
    }
}

if (!function_exists('user_preferences')) {
    /**
     * Get current authenticated user preferences .
     *
     * @param UserPreferencesKeysEnum|null $key
     * @param Authenticatable|User|null $user
     * @return array|mixed|null
     */
    function user_preferences(?UserPreferencesKeysEnum $key = null, User|Authenticatable|null $user = null): mixed
    {
        if (is_null($user)) {
            $user = auth()->user();
        }

        if (!$user) {
            return null;
        }

        $userPreferences = resolve(UserPreferenceRepositoryInterface::class)->getPreferencesByUser($user);

        if (!is_null($key)) {
            $value = data_get($userPreferences, $key->value);

            if (in_array($value, ['true', 'yes', '1'])) {
                return true;
            }

            if (in_array($value, ['false', 'no', '0'])) {
                return false;
            }

            return $value;
        }

        return $userPreferences;
    }
}

if (!function_exists('get_blade_based_on_entity_iso')) {
    function get_blade_based_on_entity_iso($path, $iso = 'global')
    {
        $allowed_iso = ['global', 'jo', 'au', 'africa'];

        if (!in_array($iso, $allowed_iso)) {
            $iso = 'global';
        }

        return $path . '_' . $iso;
    }
}

if (!function_exists('source_host')) {
    function source_host($host = null)
    {
        // Retrieve the default affiliate website name if no host is provided
        if (is_null($host)) {
            if (app()->runningInConsole()) {
                $host = AffiliateWebsite::getDefaultAffiliateWebsiteName();
            } else {
                $host = request()->getHost();
            }
        }

        // Remove any existing protocol (http or https) from the host
        $host = str_ireplace(['http://', 'https://'], '', $host);

        // Determine the protocol based on the application's HTTPS status
        $protocol = config('app.https_status') ? 'https' : 'http';

        // Construct and return the full URL
        return $protocol . '://' . $host;
    }
}

if (!function_exists('number_formatter')) {
    function number_formatter($amount, $code = 'USD')
    {
        $formatter = new NumberFormatter('en_US', NumberFormatter::CURRENCY);
        $formatter->setTextAttribute(NumberFormatter::CURRENCY_CODE, $code);
        $formatter->setAttribute(NumberFormatter::FRACTION_DIGITS, 2);

        return $formatter->formatCurrency($amount, $code);
    }
}

if (!function_exists('utm_cookie_data')) {
    function utm_cookie_data(): ?string
    {
        if (!request()->hasCookie('ingot_utm_data')) {
            return null;
        }

        return request()->cookie('ingot_utm_data');
    }
}

if (!function_exists('generate_unique_token')) {
    function generate_unique_token(): ?string
    {
        return hash('sha256', uniqid('', true));
    }
}

if (!function_exists('render_nested_array_table')) {
    function render_nested_array_table($data)
    {
        $html = '<table class="table table-bordered">';
        foreach ($data as $key => $value) {
            $html .= '<tr>';
            $html .= '<td>' . $key . '</td>';
            $html .= '<td>';
            if (is_array($value)) {
                $html .= render_nested_array_table($value);
            } else {
                $html .= $value ?? 'NULL';
            }
            $html .= '</td>';
            $html .= '</tr>';
        }
        $html .= '</table>';

        return $html;
    }
}

if (!function_exists('render_view_as_html')) {
    function render_view_as_html($path, $params): string
    {
        return html_entity_decode(view($path, $params));
    }
}

if (!function_exists('rip_tags')) {
    function rip_tags($string)
    {
        // Remove HTML tags
        $string = strip_tags($string);

        // Decode HTML entities
        $string = html_entity_decode($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove control characters
        $string = str_replace(["\r", "\n", "\t", '&nbsp;', ' '], ' ', $string);

        // Remove multiple spaces
        $string = trim(preg_replace('/\s+/', ' ', $string));

        return $string;
    }
}

if (!function_exists('recursive_array_list_print')) {
    function recursive_array_list_print($array)
    {
        $html = null;

        if (is_array($array) && count($array) > 0) {
            $html = '<ul class="list-group list-group-flush">';
        }

        foreach ($array as $key => $value) {
            $html .= '<li class="list-group-item">';
            if (is_array($value)) {
                $randomKey = Str::random(10);

                $html .= '<a class="collapsed " data-toggle="collapse" href="#collapse' . $randomKey . '" role="button" aria-expanded="false" aria-controls="collapse' . $randomKey . '">+ ' . $key . '</a>';
                $html .= '<div class="collapse" id="collapse' . $randomKey . '">';
                $html .= recursive_array_list_print($value);
                $html .= '</div>';
            } else {
                $html .= $key . ' => ' . ($value ?? 'null');
            }
            $html .= '</li>';
        }

        if (is_array($array) && count($array) > 0) {
            $html .= '</ul>';
        }

        return $html;
    }
}

if (!function_exists('logo_theme')) {
    function logo_theme(): string
    {
        $site_logo = trans_config('site_dark_logo');
        if ($user = auth()->user()) {
            $theme_mode = $user->getUserThemeMode();
            if ($theme_mode == 'dark') {
                $site_logo = trans_config('site_light_logo');
            }
        }

        return asset('storage/uploads/' . $site_logo);
    }
}

if (!function_exists('public_asset_url')) {
    function public_asset_url($path, $host = null): string
    {
        $host = source_host($host);

        $query = http_build_query([
            'f' => $path,
        ]);

        return  urldecode("{$host}/media-assets?{$query}");
    }
}

// if using mobile app
if (!function_exists('has_mobile_token')) {
    function has_mobile_token(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return session('hasMobileToken') == true;
    }
}

// for clients who register from app and wants to use CRM, should be same flow
if (!function_exists('has_mobile_eligibility')) {
    function has_mobile_eligibility(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return has_mobile_token() || auth()->user()?->isMobileReferral();
    }
}

// is using mobile device
if (!function_exists('is_mobile_user')) {
    function is_mobile_user(): bool
    {
        return session('hasMobileToken') == true || Browser::isMobile();
    }
}
