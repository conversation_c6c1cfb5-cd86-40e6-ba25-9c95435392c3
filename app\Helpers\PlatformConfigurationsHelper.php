<?php

namespace App\Helpers;

use App\Models\MtGroup;
use App\Models\TradingPlatform;
use App\Models\AffiliateWebsite;
use App\Models\TradingPlatformConfig;
use App\Repositories\Interfaces\AffiliateWebsiteRepositoryInterface;
use App\Repositories\Interfaces\TradingServerRepositoryInterface;

class PlatformConfigurationsHelper
{
    /**
     * @return array $result
     */
    public static function getPlatformData($platformName, $affiliateName = null)
    {
        $result = [];

        if (!empty($platformName)) {
            if (!$affiliateName) {
                if (app()->runningInConsole()) {
                    $affiliateName = AffiliateWebsite::getDefaultAffiliateWebsiteName();
                } elseif (auth()->check()) {
                    $relation = auth()?->user()?->application?->belongsToAgentApp();
                    if ($relation && $relation['status'] && $relation['affiliate_website']) {
                        $affiliateName = $relation['affiliate_website'];
                    } elseif (auth()->user()?->application?->affiliateWebsite) {
                        $affiliateName = auth()->user()?->application?->affiliateWebsite?->name;
                    }
                }

                if (!$affiliateName) {
                    $affiliateName = request()->getHost();
                }
            }

            $affiliateName = strtolower($affiliateName);

            $platformId = TradingPlatform::query()->where('name', strtolower($platformName))->value('id');

            $affiliateId = AffiliateWebsite::query()->where(function ($s) use ($affiliateName) {
                $s->where('name', $affiliateName)
                    ->orWhere('site_name', $affiliateName)
                    ->orWhere('crm_name', $affiliateName)
                    ->orWhereJsonContains('aliases', $affiliateName);
            })->value('id');

            if ($platformId && $affiliateId) {
                $result = TradingPlatformConfig::query()->where([
                    'trading_platform_id' => $platformId,
                    'affiliate_website_id' => $affiliateId,
                ])->pluck('value', 'key')->toArray();
            }
        }

        return $result;
    }

    public static function getConfigBServerId(int $server_id, string $affiliateWebsite = null): array
    {
        $affiliateWebsite = self::getAffiliateWebsite($affiliateWebsite);

        if (!$affiliateWebsite) {
            return [];
        }

        $tradingServer = resolve(TradingServerRepositoryInterface::class)->findById(
            id: $server_id,
            with: ['configs']
        );

        $configurations = $tradingServer?->configs
        ?->where('affiliate_website_id', $affiliateWebsite->id)
        ?->pluck('value', 'key')->toArray() ?? [];

        return [
            'TRADING_SERVER_NAME' => $tradingServer?->name,
            ...$configurations,
        ];
    }

    public static function getConfigByMtGroup(MtGroup $mtGroup, string $affiliateWebsite = null): array
    {
        $affiliateWebsite = self::getAffiliateWebsite($affiliateWebsite);

        if (!$affiliateWebsite) {
            return [];
        }

        $configurations = $mtGroup->tradingServer?->configs
        ?->where('affiliate_website_id', $affiliateWebsite->id)
        ?->pluck('value', 'key')->toArray() ?? [];

        return [
            'TRADING_SERVER_NAME' => $mtGroup->tradingServer?->name,
            ...$configurations,
        ];
    }

    public static function getAffiliateWebsite(?string $affiliateWebsite): AffiliateWebsite | null
    {
        if (!$affiliateWebsite) {
            if (app()->runningInConsole()) {
                $affiliateWebsite = AffiliateWebsite::getDefaultAffiliateWebsiteName();
            } elseif (auth()->check()) {
                $relation = auth()?->user()?->application?->belongsToAgentApp();
                if ($relation && $relation['status'] && $relation['affiliate_website']) {
                    $affiliateWebsite = $relation['affiliate_website'];
                } elseif (auth()->user()?->application?->affiliateWebsite) {
                    $affiliateWebsite = auth()->user()?->application?->affiliateWebsite?->name;
                }
            }

            if (!$affiliateWebsite) {
                $affiliateWebsite = request()->getHost();
            }
        }

        $affiliateWebsite = strtolower($affiliateWebsite);

        return resolve(AffiliateWebsiteRepositoryInterface::class)->findByName($affiliateWebsite);
    }
}
