<?php

namespace App\Exports;

use App\Http\Controllers\Admin\UniversityLeadController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class UniversityLeadExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(UniversityLeadController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Campaign Name',
            'Promo Code',
            'First Name',
            'Last Name',
            'Email',
            'Phone Number',
            'ID Type',
            'ID Number',
            'University ID Number',
            'University Name',
            'Year of Study',
            'School',
            'Date'
        ];
    }

    public function map($row): array
    {
        return [
            $row->campaign_name ?? '-',
            $row->promo_code ?? '-',
            $row->first_name ?? '-',
            $row->last_name ?? '-',
            $row->email ?? '-',
            $row->phone_number ?? '-',
            $row::$id_types[$row->id_type] ?? '-',
            $row->id_number ?? '',
            $row->university_id_number ?? '',
            $row->university_name ?? '',
            $row->academic_year ?? '',
            $row->college->name ?? '',
            $row->created_at->format('d-m-Y H:i:s') ?? '-',
        ];
    }
}
