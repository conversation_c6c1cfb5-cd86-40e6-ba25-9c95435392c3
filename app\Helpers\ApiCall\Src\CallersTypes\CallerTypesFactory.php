<?php

namespace App\Helpers\ApiCall\Src\CallersTypes;

use Exception;

class CallerTypesFactory
{
    /**
     * @throws Exception
     */
    public static function create($type, $url): DELETE|GET|POST|PUT
    {
        $type = strtoupper($type);
        $caller = match ($type) {
            'GET' => new GET($url),
            'POST' => new POST($url),
            'PUT' => new PUT($url),
            'DELETE' => new DELETE($url),
            default => throw new Exception("Method {$type} isn't allowed")
        };

        return $caller;
    }
}
