<?php

namespace App\Exports\CSV\ExportStrategies;

class Getter extends ExportStrategyAbstraction
{
    protected $data;

    public function __construct()
    {
    }

    public function setData(mixed $data): ExportStrategyAbstraction
    {
        $this->data = $data;

        return $this;
    }

    public function handle(&$csvFile): void
    {
        $this->fillHeaders($this->data, $csvFile);
        $this->fillToFile($csvFile, $this->data);
    }

    public function __serialize(): array
    {
        $data = parent::__serialize(); // TODO: Change the autogenerated stub
        $data['data'] = serialize($this->data);

        return $data;
    }

    public function __unserialize(array $data): void
    {
        parent::__unserialize($data); // TODO: Change the autogenerated stub
        $this->data = unserialize($data['data']);
    }
}
