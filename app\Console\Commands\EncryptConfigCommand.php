<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;

class EncryptConfigCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'config:encrypt {--decrypt}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Encrypt configuration data';

    /**
     * Execute the console command.
     */
    protected $tablesToEncrypt = [
        'affiliate_website_configs' => ['value'],
        'trading_server_configs' => ['value'],
        'trading_platform_configs' => ['value'],
        'payment_gateway_configs' => ['value'],
    ];

    public function handle(): void
    {
        foreach ($this->tablesToEncrypt as $table => $fields) {
            $this->encryptTableFields($table, $fields);
        }
    }

    protected function encryptTableFields($table, $fields): void
    {
        $shouldDecrypt = $this->option('decrypt');

        DB::table($table)->chunkById(100, function ($records) use ($fields, $table, $shouldDecrypt) {
            foreach ($records as $record) {
                $updates = [];

                foreach ($fields as $field) {
                    if (blank($record->$field)) {
                        continue;
                    }

                    if ($shouldDecrypt) {
                        $updates[$field] = Crypt::decryptString($record->$field);
                    } else {
                        $updates[$field] = Crypt::encryptString($record->$field);
                    }
                }

                if (!empty($updates)) {
                    DB::table($table)
                        ->where('id', $record->id)
                        ->update($updates);
                }
            }
        });

        if ($shouldDecrypt) {
            $this->info("Decryption completed for table: $table.");
        } else {
            $this->info("Encryption completed for table: $table.");
        }
    }
}
