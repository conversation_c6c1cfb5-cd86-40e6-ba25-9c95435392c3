<?php

namespace App\Exports;

use App\Http\Controllers\Admin\OpenPositionReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class OpenPositionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    protected $actions = [];

    protected $mt_login_arr = [];

    protected $platform;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $exportQuery = $this->allQuery();

        $this->mt_login_arr = $exportQuery['login_arr'];
        $this->actions = $exportQuery['actions'];
        $this->platform = $exportQuery['platform'];
    }

    public function headings(): array
    {
        return [
            'LOGIN',
            'PLATFORM',
            'ACQUISITION MANAGER',
            'RETENTION MANAGER',
            'TICKET',
            'ACTION',
            'OPEN TIME',
            'OPEN PRICE',
            'SYMBOL',
            'VOLUME',
            'PROFIT',
        ];
    }

    public function map($row): array
    {
        $action = $this->actions[$this->platform][$row->action];
        $acquisition_manager = $this->mt_login_arr[$this->platform][$row->LOGIN]['ACCOUNT_MANAGER_CLOSER'] ?? '-';
        $retention_manager = $this->mt_login_arr[$this->platform][$row->LOGIN]['ACCOUNT_MANAGER_FOLLOWER'] ?? '-';

        return [
            $row->login,
            $this->platform,
            $acquisition_manager,
            $retention_manager,
            $row->ticket,
            $action,
            $row->open_time,
            $row->open_price,
            $row->symbol,
            $row->volume,
            $row->profit,
        ];
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(OpenPositionReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        )['data'];
    }

    public function allQuery()
    {
        auth()->loginUsingId($this->userId);

        return app(OpenPositionReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }
}
