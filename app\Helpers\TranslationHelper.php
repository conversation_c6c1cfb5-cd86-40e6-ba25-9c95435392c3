<?php

namespace App\Helpers;

use App\Models\Translation;
use Exception;
use Google\Cloud\Translate\V2\TranslateClient;

class TranslationHelper
{
    public static function createTranslation($object)
    {
        $langs = config('app.locales');
        foreach ($object->getTranslatable() as $field) {
            foreach ($langs as $lang => $value) {
                $transValue = $object->{$field};
                if (is_array($transValue)) {
                    $transValue = json_encode($transValue);
                }

                Translation::updateOrCreate([
                    'model_name' => class_basename($object),
                    'field_name' => $field,
                    'object_id' => $object->id,
                    'locale' => $lang,
                ], [
                    'value' => $transValue,
                ]);
            }
        }
    }

    public static function updateTranslation($object)
    {
        foreach ($object->getTranslatable() as $field) {
            $transValue = $object->{$field};
            if (is_array($transValue)) {
                $transValue = json_encode($transValue);
            }

            Translation::updateOrCreate([
                'model_name' => class_basename($object),
                'field_name' => $field,
                'object_id' => $object->id,
                'locale' => $object->locale,
            ], [
                'value' => $transValue,
            ]);
        }
    }

    public static function deleteTranslation($object)
    {
        foreach ($object->getTranslatable() as $field) {
            $translation = Translation::query()->where('Model_name', class_basename($object))
                ->where('field_name', $field)
                ->where('object_id', $object->id);
            $translation->delete();
        }
    }

    public static function getGoogleTranslation($text)
    {
        try {
            $translate = new TranslateClient([
                'key' => config('services')['google_developer']['api_key'],
                'restOptions' => ['verify' => !is_local_env()],
            ]);

            $result = $translate->translate($text, [
                'target' => 'en',
            ]);

            if (!empty($result)) {
                return $result['text'];
            }
        } catch (Exception $e) {
            return $text;
        }
    }
}
