<?php

namespace App\Events\PaymentGateway;

use App\Models\PaymentGateway;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserPaymentGatewaySelected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public User $user,
        public PaymentGateway $paymentGateway,
    )
    {
    }
}



