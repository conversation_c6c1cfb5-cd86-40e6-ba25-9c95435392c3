<?php

namespace App\Exports;

use App\Http\Controllers\Admin\AgentTransactionReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AgentTransactionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(AgentTransactionReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Allocation',
            'MT Login',
            'MT Group',
            'Platform',
            'Currency',
            'Credit',
        ];
    }

    public function map($row): array
    {
        return [
            $row->mt_group_allocation_name ?? '-',
            $row->mtAccount->login ?? '-',
            $row->mt_group_name ?? '-',
            $row->trading_platform_name ?? '-',
            $row->currency_code ?? '-',
            $row->mtAccount?->metaTraderRealAccount?->credit ? number_format($row->mtAccount->metaTraderRealAccount->credit, 4, '.', '') : 0,
        ];
    }
}
