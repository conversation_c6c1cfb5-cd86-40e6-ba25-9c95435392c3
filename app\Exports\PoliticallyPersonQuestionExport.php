<?php

namespace App\Exports;

use App\Http\Controllers\Admin\PoliticallyPersonQuestionController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PoliticallyPersonQuestionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(PoliticallyPersonQuestionController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Answers'
        ];
    }

    public function map($row): array
    {
        $data[0] = [
            $row->name,
        ];
        foreach ($row->answers as $key => $answer) {
            if ($key == 0) {
                $data[$key][] = $answer->name;
            } else {
                $data[$key] = [null ,$answer->name];
            }
        }

        return $data;
    }
}
