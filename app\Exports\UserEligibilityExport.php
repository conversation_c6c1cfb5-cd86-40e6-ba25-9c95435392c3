<?php

namespace App\Exports;

use App\Http\Controllers\Admin\UserEligibilityController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class UserEligibilityExport implements FromCollection, WithMapping, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(UserEligibilityController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function map($data): array
    {
        $user_name = $data->first()->application->mainUser->full_name ?? '-';
        $export_data = [];
        $export_data[] = [
            'Client Name',
            $user_name
        ];
        $export_data[] = [];

        $q_count = 1;
        foreach ($data as $question) {
            $export_data[] = [
                'Question ' . $q_count++,
                $question->asicQuestion->question,
                $question->asicAnswer->question_answer ?? '-',
                $question->asicAnswer ? ($question->asicAnswer->correct ? 'correct' : 'wrong') : '-',
            ];
        }
        $export_data[] = [];
        $export_data[] = [];

        return $export_data;
    }
}
