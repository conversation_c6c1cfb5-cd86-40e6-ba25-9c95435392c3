<?php

namespace App\Exports\CSV\ExportStrategies;

use Illuminate\Database\Eloquent\Builder;
use <PERSON>vie\SerializesQuery\Query;
use AnourValar\EloquentSerialize\Facades\EloquentSerializeFacade as EloquentSerialize;

class Chunkier extends ExportStrategyAbstraction
{
    protected $query;

    public function __construct(protected int $chunkCount = 5000)
    {
    }

    public function setData(mixed $data): ExportStrategyAbstraction
    {
        $this->query = $data;

        return $this;
    }

    /**
     * @param $csvFile
     * @return mixed
     */
    public function handle(&$csvFile): void
    {
        $callback = $this->callbacks;
        if (empty($this->query->orders)) {
            $this->query->orderBy('id');
        }

        $firstChunk = true;
        /**
         * @var Builder $query
         */
        $this->query->chunk($this->chunkCount ?? 5000, function ($result) use ($callback, $csvFile, &$firstChunk) {
            $result = $result->all();
            if (isset($callback['before'])) {
                $callback['before']($result, $this);
            }
            // Add Headers To File
            if ($firstChunk) {
                $firstChunk = false;
                $this->fillHeaders($result, $csvFile);
            }

            // Add the data to file
            $this->fillToFile($csvFile, $result);
        });
    }

    public function __serialize(): array
    {
        $data = parent::__serialize();
        if (($this->query instanceof \Illuminate\Database\Query\Builder)) {
            $data['query'] = Query::serialize($this->query);
            $data['query_serialization'] = true;

            return $data;
        }
        $data['query'] = EloquentSerialize::serialize($this->query);
        $data['query_serialization'] = false;

        return $data;
    }

    public function __unserialize(array $data): void
    {
        parent::__unserialize($data);
        $this->query = $data['query_serialization'] ? Query::unserialize($data['query'])
            : EloquentSerialize::unserialize($data['query']);
    }
}
