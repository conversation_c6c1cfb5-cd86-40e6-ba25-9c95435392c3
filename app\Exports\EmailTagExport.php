<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use App\Http\Controllers\Admin\EmailTagController;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class EmailTagExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison, WithTitle
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData, protected $language = null)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(EmailTagController::class)->index(
            request: request()->merge($this->requestData),
            export: true,
            language: $this->language
        );
    }

    public function headings(): array
    {
        return [
            'Title',
            'Type',
            'Content',
            'Created Date',
            'Updated On',
        ];
    }

    public function map($data): array
    {
        $text = substr(rip_tags($data->text), 0, 32500);
        $text = mb_convert_encoding($text, 'UTF-8');

        return [
            $data->key ?? '-',
            $data->type ?? '-',
            $text ?? '-',
            $data->created_at ?? '-',
            $data->updated_at ?? '-',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return strtoupper($this->language);
    }
}
