<?php

namespace App\Helpers;

use Exception;
use App\Models\Role;
use App\Models\User;
use App\Models\Referral;
use App\Models\Application;
use App\Models\AppRelation;
use App\Models\CampaignLead;
use App\Mail\NewAssignedUser;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\AppRelationCountryAssign;
use App\Mail\MemberNewReferralRegistered;

class LeadsAssignHelper
{
    public static function assignAcquisition(
        $referral_value,
        $app_data,
        $user
    ): void {
        if (!$user) {
            return;
        }

        $closer_app_id = $parent_app_id = $lead = null;

        $app = $user->application;
        $client_county = $user->country_id ?? null;
        $client_citizenship = $user->citizenship_id ?? null;
        $entity = $app->affiliateWebsite->regulation_entity_id;
        $lang = $user->pref_lang;
        $app_type_id = $app->app_type_id;

        Log::channel('assign')->info('Start assignAcquisition, Data:', [
            'app_id' => $user->application_id,
            'referral_value' => $referral_value,
            'client_county' => $client_county,
            'client_citizenship' => $client_citizenship,
            'app_type_id' => $app_type_id,
            'user' => $user->id,
        ]);

        // decrypt referral value
        $referral_data = IngotHelper::detectReferralSource($referral_value, request()->get('source'));

        Log::channel('assign')->info('Fetch data from IngotHelper:', $referral_data ?? 'no data found!');

        // flag the campaign lead as registered user
        $lead = CampaignLead::query();
        if ($referral_data && array_key_exists('lead_id', $referral_data)) {
            $lead->where('id', $referral_data['lead_id']);
        } else {
            $lead->where('email', $user->email);
        }
        $lead = $lead->first();
        if ($lead && !$lead->user_id) {
            $lead->update([
                'user_id' => $user->id,
                'status' => CampaignLead::STATUS_DEAL_CLOSED,
            ]);
        }

        Log::channel('assign')->info('Checking CampaignLead Data:', (array) $lead ?? 'no data found!');

        // if the lead has parent_app_id
        if ($referral_data && array_key_exists('parent_app_id', $referral_data)) {
            $referral_type = $referral_data['referral_type'];

            // if the referral is agent/referral
            if (in_array($referral_type, Referral::$partnership_referrals)) {
                $parent_app_id = $referral_data['parent_app_id'];

                $referral = User::query()
                    ->where('application_id', $parent_app_id)
                    ->with([
                        'application.appRelation.parentApplication.mainUser.role:id,name',
                        'application.appRelation.closerApplication.mainUser.role:id,name',
                        'role:id,name',
                    ])
                    ->withAggregate('role', 'name')
                    ->first();

                Log::channel('assign')->info('referral_data detected has parent:', (array) $referral);

                // check if the referral role is sales then assign the lead to the parent of the agent.
                if (in_array($referral?->role?->name, Role::$account_manager_roles)) {
                    $closer_app_id = $parent_app_id;

                    Log::channel('assign')->info('Closer is account manager', [
                        'closer_app_id' => $closer_app_id,
                    ]);
                } elseif (in_array($referral?->role?->name, Role::$client_roles)) {
                    // check if the client has a rule for round-robin
                    $referral_data = AppRelationCountryAssign::autoAssignUserByCountry(
                        $client_county,
                        AppRelationCountryAssign::ASSIGN_ROLE_ACQUISITION,
                        $entity,
                        $app_type_id,
                        $client_citizenship,
                        $parent_app_id,
                        $lang
                    );

                    Log::channel('assign')->info('Referral is client', [
                        'autoAssignUserByCountry results' => $referral_data,
                    ]);

                    if ($referral_data && array_key_exists('parent_app_id', $referral_data)) {
                        $closer_app_id = $referral_data['parent_app_id'] ?? null;
                        $parent_app_id = $referral_data['referral_app_id'] ?? null;

                        Log::channel('assign')->info('autoAssignUserByCountry has parent_app_id', [
                            'closer_app_id' => $closer_app_id,
                            'parent_app_id' => $parent_app_id,
                        ]);
                    } else {
                        $referral_closer_role = $referral?->application?->appRelation?->closerApplication?->mainUser?->role?->name;
                        // if the closer of the parent  one of these (sales manager, sales, telesales, acquisition) , assign the lead to him
                        if (in_array($referral_closer_role, Role::$account_manager_roles)) {
                            $closer_app_id = $referral->application->appRelation->closer_app_id;

                            Log::channel('assign')->info('Closer is account manager', [
                                'closer_app_id' => $closer_app_id,
                            ]);
                        }
                    }
                }
            }
        }

        // check if force assign to sales app
        $sales_app_id = null;
        if (session()->has('sales_app_id')) {
            $sales_app_id = session()->get('sales_app_id');
        } elseif (array_key_exists('sales_app_id', $app_data) && !is_null($app_data['sales_app_id'])) {
            $sales_app_id = $app_data['sales_app_id'];
        }
        if ($sales_app_id) {
            $sales_app = Application::query()->active()
            ->where('id', $sales_app_id)
            ->whereHas('mainUser.role', function ($r) {
                $r->whereIn('name', Role::$account_manager_roles);
            })
            ->exists();

            if ($sales_app) {
                $closer_app_id = $sales_app_id;

                Log::channel('assign')->info('Assign has sales force session', [
                    'closer_app_id' => $closer_app_id,
                ]);
            } else {
                Log::channel('assign')->info('Assign has sales force session but not exists', [
                    'sales_app_id' => $sales_app_id,
                ]);
            }
        }

        // if referral not agent OR referral is agent but his parent not from these roles : (sales manager, sales, telesales, acquisition) THEN call auto assign by country
        if (!$closer_app_id) {
            // if referral is campaign lead assign to same account manager
            if (!is_null($lead)) {
                $closer_app_id = $lead->account_manager ?? null;
                $parent_app_id = null;

                Log::channel('assign')->info('Lead not assigned, trying to get account manager from leads', [
                    'closer_app_id' => $closer_app_id,
                ]);
            }

            // there no no account manager matched with the rules
            if (!$closer_app_id) {
                $referral_data = AppRelationCountryAssign::autoAssignUserByCountry(
                    $client_county,
                    AppRelationCountryAssign::ASSIGN_ROLE_ACQUISITION,
                    $entity,
                    $app_type_id,
                    $client_citizenship,
                    $parent_app_id,
                    $lang
                );

                Log::channel('assign')->info('trying to get closer again', (array) $referral_data);
            }

            if ($referral_data && array_key_exists('parent_app_id', $referral_data)) {
                $closer_app_id = $referral_data['parent_app_id'];
                $parent_app_id ??= $referral_data['referral_app_id'];

                Log::channel('assign')->info('autoAssignUserByCountry has parent_app_id', [
                    'closer_app_id' => $closer_app_id,
                    'parent_app_id' => $parent_app_id,
                ]);
            }
        }

        if ($closer_app_id || $parent_app_id) {
            AppRelation::query()->updateOrCreate([
                'child_app_id' => $user->application_id,
            ], [
                'closer_app_id' => $closer_app_id ?? null,
                'parent_app_id' => $parent_app_id ?? null,
                'comment' => 'First time assigned to acquisition'
            ]);

            Log::channel('assign')->info('AppRelation created/updated', [
                'app_id' => $user->application_id,
                'closer_app_id' => $closer_app_id,
                'parent_app_id' => $parent_app_id,
            ]);
        }

        // notify the account manager
        if ($closer_app_id) {
            $closer = User::query()
                ->where('application_id', $closer_app_id)
                ->with([
                    'language:id,abbreviation',
                ])
                ->first();

            if ($closer) {
                try {
                    $closer_lang = $closer->language->abbreviation ?? 'en';
                    $mail = Mail::to([[
                        'email' => $closer->email,
                        'name' => $closer->full_name,
                    ]])
                    ->locale($closer_lang);

                    $mail->send(new MemberNewReferralRegistered($app->appType->display_name, $user, $closer_lang));
                } catch (Exception $e) {
                }

                Log::channel('assign')->info('Email sent to closer', [
                    'closer_app_id' => $closer_app_id,
                    'email' => $closer->email,
                ]);
            }

            Log::channel('assign')->info('End assignAcquisition');
        }
    }

    /**
     * @param $client_app_id
     * @return void
     */
    public static function autoAssignRetention(
        $client_app_id,
        $client_county,
        $app_type_id,
        $client_citizenship,
        $lang = null
    ): void {
        $client_app_relation = AppRelation::query()
            ->where('app_relations.child_app_id', '=', $client_app_id)
            ->whereNull('app_relations.deleted_at')
            ->whereNull('app_relations.follower_app_id')
            ->with([
                'closerApplication.mainUser.role:id,name',
            ])
            ->first();

        // to except the client ho has parent from assigning to retention
        if ($client_app_relation?->parent_app_id) {
            return;
        }

        // in case user already assigned to retention in (closer_app_id) then we keep the user assigned to him
        if (!in_array($client_app_relation?->closerApplication?->mainUser?->role?->name, Role::$acquisition_roles)) {
            return;
        }

        $app_has_deposited = Application::query()
            ->where('id', $client_app_id)
            ->whereHas('firstDeposit')
            ->exists();

        $entity_id = Application::query()->where('id', $client_app_id)->with([
            'affiliateWebsite:id,regulation_entity_id',
        ])->first()?->affiliateWebsite?->regulation_entity_id;

        $auto_assign = AppRelationCountryAssign::autoAssignUserByCountry(
            $client_county,
            AppRelationCountryAssign::ASSIGN_ROLE_RETENTION,
            $entity_id,
            $app_type_id,
            $client_citizenship,
            $client_app_relation?->parent_app_id,
            $lang
        );

        if ($app_has_deposited && $client_app_relation && $auto_assign && isset($auto_assign['parent_app_id'])) {
            AppRelation::query()->updateOrCreate([
                'id' => $client_app_relation->id,
            ], [
                'follower_app_id' => $auto_assign['parent_app_id'],
                'comment' => 'Auto assigned to retention'
            ]);

            try {
                $user_emails = User::query()->where('application_id', $auto_assign['parent_app_id'])->pluck('email')->toArray();
                if (!empty($user_emails)) {
                    $assigned_users = User::query()->where('application_id', $client_app_id)->get();

                    Mail::to($user_emails)->locale('en')->send(
                        new NewAssignedUser($assigned_users)
                    );
                }
            } catch (Exception $e) {
            }
        }
    }
}
