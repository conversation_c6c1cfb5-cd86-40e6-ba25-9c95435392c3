<?php

namespace App\Enums;

enum MetaTrader5Enum: string
{
    public const LOT_SIZE = 10000;

    public const DEAL_ACTION = [
        '0' => 'buy',
        '1' => 'sell',
        '2' => 'balance',
        '3' => 'credit',
        '4' => 'charge',
        '5' => 'correction',
        '6' => 'bonus',
        '7' => 'commission',
        '8' => 'daily commission',
        '9' => 'monthly commission',
        '10' => 'agent daily',
        '11' => 'agent monthly',
        '12' => 'interestrate',
        '13' => 'buy canceled',
        '14' => 'sell canceled',
        '15' => 'dividend',
        '16' => 'dividend franked',
        '17' => 'tax',
        '18' => 'agent',
        '19' => 'compensation',
        '20' => 'so credit compensation',
    ];

    public const TRADE_DEAL_ACTION = [
        '0' => 'buy',
        '1' => 'sell',
        '13' => 'buy canceled',
        '14' => 'sell canceled',
    ];

    public const AGENT_COMMISSION_DEAL_ACTION = [
        '11' => 'agent monthly',
        '18' => 'agent',
    ];

    public const ENTRY_FLAGS = [
        '0' => 'In',
        '1' => 'Out',
        '2' => 'In/Out',
        '3' => 'Out By',
    ];

    public const REASON = [
        '0' => 'client',
        '1' => 'expert',
        '2' => 'dealer',
        '3' => 'sl',
        '4' => 'tp',
        '5' => 'so',
        '6' => 'ollover',
        '7' => 'external client',
        '8' => 'vmargin',
        '9' => 'gateway',
        '10' => 'signal',
        '11' => 'settlement',
        '12' => 'transfer',
        '13' => 'sync',
        '14' => 'external service',
        '15' => 'migration',
        '16' => 'mobile',
        '17' => 'web',
        '18' => 'split',
    ];

    public const ORDER_TYPES = [
        '0' => 'Buy',
        '1' => 'Sell',
        '2' => 'Buy Limit',
        '3' => 'Sell Limit',
        '4' => 'Buy Stop',
        '5' => 'Sell Stop',
        '6' => 'Buy Stop Limit',
        '7' => 'Sell Stop Limit',
        '8' => 'Close by',
    ];

    public const STATES = [
        '0' => 'Started',
        '1' => 'Placed',
        '2' => 'Canceled',
        '3' => 'Partially filled',
        '4' => 'Filled',
        '5' => 'Rejected',
        '6' => 'Expired',
        '7' => 'Request add',
        '8' => 'Request modify',
        '9' => 'Request cancel',
    ];

    public const REGIONS = [
        'australia' => '10',
        'russia' => '11',
        'suadi arabia and bahrain' => '12',
        'emirates' => '13',
        'turkey' => '14',
        'iraq' => '15',
        'far east' => '16',
        'rest of asia' => '17',
        'china' => '20',
        'europe' => '30',
        'egypt' => '32',
        'north africa' => '33',
        'africa' => '34',
        'north america' => '35',
        'south america' => '36',
        'jordan' => '37',
    ];
}
