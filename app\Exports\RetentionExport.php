<?php

namespace App\Exports;

use App\Http\Controllers\Admin\RetentionReportController;
use App\Models\TransactionType;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class RetentionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private $app_logins = [];

    private $deposit;

    private $mt_data_arr = [];

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->deposit = TransactionType::query()->where('key', 'deposit')->first()->key ?? null;

        $this->query();
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(RetentionReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Username',
            'Phone',
            'ACCOUNT TYPE',
            'ACCOUNT Manager',
            'Register Date',
            'Total Deposits',
            'Last Deposit Date',
            'Last Withdrawal Date',
            'Last Login Date',
            'Login',
            'Instruments',
            'Margin',
        ];
    }

    public function map($application): array
    {
        $data[0] = [
            $application->mainUser?->getUserHolderName() ?? '-',
            $application->mainUser->phone ?? '-',
            $application?->appType?->display_name ?? '-',
            $application->getAppAccountManagerName() ?? '-',
            date('d/m/Y', strtotime(($application->created_at))) ?? '-',
            $application->totalTransactions([$this->deposit,'deposit_correction']) . ' ' . $application->mainAppWallet?->currency->code ?? '-',
            date('d/m/Y', strtotime($application->lastDeposit?->created_at)) ?? '-',
            date('d/m/Y', strtotime($application->lastWithdrawal?->created_at)) ?? '-',
            date('d/m/Y', strtotime($application->latestLogin?->created_at)) ?? '-',
        ];

        $count_fields = count($data[0]);
        $key = count($data) - 1;
        foreach ($application->mtAccount as $mtAccount) {
            if ($key != 0) {
                $data[$key] = [];
                array_push($data[$key], ...Collection::times($count_fields, fn () => ' '));
            }

            if ($mtAccount->isMt5()) {
                $mtAccount['instruments'] = $mtAccount?->metaTraderRealAccount?->mt5Positions?->pluck('symbol')->toArray() ?? null;
            } else {
                $mtAccount['instruments'] = $mtAccount?->metaTraderRealAccount?->mt4Trades->pluck('symbol')->toArray() ?? null;
            }

            $data[$key][] = $mtAccount->login;
            $data[$key][] = isset($mtAccount->instruments) && $mtAccount->instruments ? implode(',', (array) $mtAccount->instruments) : '-';
            $data[$key][] = $mtAccount->metaTraderRealAccount?->margin ?? 0;
            $key += 1;
        }

        return $data;
    }
}
