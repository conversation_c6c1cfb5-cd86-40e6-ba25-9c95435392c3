<?php

namespace App\Exports;

use App\Http\Controllers\Admin\WebinarController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class WebinarUserExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData, protected array $actionParameters)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(WebinarController::class)->webinarUsers(
            request: request()->merge($this->requestData),
            id: $this->actionParameters['id'],
            export: true
        )['data'];
    }

    public function headings(): array
    {
        return [
            'User Type',
            'Name',
            'Email',
            'Phone',
            'Country',
            'Source',
            'Registered on website',
            'Registration Date',
        ];
    }

    public function map($row): array
    {
        $user = $created_at = $phone = $type = $name = null;

        if ($row->user) {
            $user = $row->user;
            $type = __('content.registered');

            $created_at = $row->created_at;
            $phone = $user->phone;
            $name = $user->full_name;
        } elseif ($row->webinarGuestUsers) {
            $user = $row->webinarGuestUsers;
            $type = __('content.guest');
            $created_at = $user->created_at;
            $phone = $user->mobile;
            $name = $user->first_name . ' ' . $user->last_name;
        }

        $data = [];
        if ($user) {
            $data = [
                $type,
                $name,
                $user->email,
                $phone,
                $user->country->name ?? '',
                $row->source ?? '',
                $row->affiliate_website_name ?? '',
                $created_at,
            ];
        }

        return $data;
    }
}
