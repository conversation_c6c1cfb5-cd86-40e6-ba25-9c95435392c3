<?php

namespace App\Helpers\ThirdPartyApis\MicrosoftGraph\OutlookMail;

use App\Helpers\ThirdPartyApis\MicrosoftGraph\OutlookMail\Content\Content;
use GuzzleHttp\Exception\GuzzleException;
use JetBrains\PhpStorm\NoReturn;
use Nette\Utils\JsonException;

class MailSender
{
    protected OutlookRequest $request;

    protected string $sender;

    protected array|string $toReceivers;

    protected array|string $ccReceivers = [];

    protected Content $content;

    protected $token;

    protected $subject;

    protected bool $isMessageSaveInItems = true;

    public function __construct(string $sender, array|string $toReceivers, $subject, Content $content)
    {
        $this->request = new OutlookRequest();
        $this->content = $content;
        $this->sender = $sender;
        $this->toReceivers = $toReceivers;
        $this->subject = $subject;
    }

    /**
     * @param  array|string  $ccReceivers
     */
    public function setCcReceivers(array|string $ccReceivers)
    {
        $this->ccReceivers = $ccReceivers;

        return $this;
    }

    /**
     * @param  bool  $isMessageSaveInItems
     */
    public function setIsMessageSaveInItems(bool $isMessageSaveInItems): self
    {
        $this->isMessageSaveInItems = $isMessageSaveInItems;

        return $this;
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    #[NoReturn]
    public function send()
    {
        $this->request->sendMessage($this->getBody(), $this->sender);
    }

    protected function getBody(): array
    {
        $body = [
            'message' => [
                'subject' => $this->subject,
                'body' => [
                    'contentType' => $this->content->getContentType(),
                    'content' => $this->content->getContent(),
                ],
            ],
        ];
        if (is_array($this->toReceivers)) {
            foreach ($this->toReceivers as $receiver) {
                $body['message']['toRecipients'][]['emailAddress']['address'] = $receiver;
            }
        } else {
            $body['message']['toRecipients'][]['emailAddress']['address'] = $this->toReceivers;
        }

        if (is_array($this->ccReceivers)) {
            foreach ($this->ccReceivers as $receiver) {
                $body['message']['ccRecipients'][]['emailAddress']['address'] = $receiver;
            }
        } else {
            $body['message']['ccRecipients'][]['emailAddress']['address'] = $this->toReceivers;
        }

        $body['saveToSentItems'] = $this->isMessageSaveInItems;

        return $body;
    }
}
