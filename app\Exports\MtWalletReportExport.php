<?php

namespace App\Exports;

use App\Models\Transaction;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\Http\Controllers\Admin\MtWalletReportController;

class MtWalletReportExport implements FromQuery, WithHeadings, WithMapping
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(MtWalletReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'App ID',
            'Login',
            'Username',
            'Entity',
            'Email',
            'Currency',
            'Country',
            'Wallet balance',
            'Total Deposits',
            'Number of Deposits',
            'First Deposit Amount',
            'Total Withdrawals',
            'Number of  Withdrawals',
            'Acquisition Manager',
            'Retention Manager',
            'Created At'
        ];
    }

    public function map($row): array
    {
        $app_ids = $row->application_id;

        $total_withdrawals = $num_withdrawals = $total_deposits = $num_deposits = 0;

        $total_transactions = Transaction::getTotalTransactions((array) $app_ids);
        if (array_key_exists($app_ids, $total_transactions)) {
            $total_withdrawals = number_format($total_transactions[$app_ids]['total_withdrawals'], 4, '.', '');
            $num_withdrawals = $total_transactions[$app_ids]['num_withdrawals'];
        }

        if (array_key_exists($app_ids, $total_transactions)) {
            $total_deposits = number_format($total_transactions[$app_ids]['total_deposits'], 4, '.', '');
            $num_deposits = $total_transactions[$app_ids]['num_deposits'];
        }

        return [
            $row->application->appIdentifierId() ?? '-',
            $row->login ?? '-',
            $row->application->mainUser?->full_name ?? '-',
            $row->application->getRegulatedEntity() ?? '-',
            $row->application->mainUser?->email,
            $row->mtGroup->currency->code,
            $row->application->mainUser?->country?->name ?: '-',
            number_format($row->metaTraderRealAccount?->balance ?? 0, 4, '.', ''),
            $total_deposits,
            $num_deposits,
            number_format($row->application->firstDeposit->rec_amount ?? 0, 4, '.', ''),
            $total_withdrawals,
            $num_withdrawals,
            $row->application->getAppAcquisitionManagerName(),
            $row->application->getAppRetentionManagerName(),
            $row->created_at ?? '-'
        ];
    }
}
