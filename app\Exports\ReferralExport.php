<?php

namespace App\Exports;

use App\Http\Controllers\Admin\ReferralReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class ReferralExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(ReferralReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID Application',
            'Name',
            'Total Referrals',
            'Trading',
            'Demo',
            'Corporate',
            'Agent',
            'Country',
            'Account Manager',
        ];
    }

    public function map($referral): array
    {
        return [
            isset($referral->mainUser) ? $referral->mainUser->application->appIdentifierId() : '-',
            isset($referral->mainUser) ? $referral->mainUser->getUserHolderName() : '-',
            $referral->parent_deal_app_relation_count,
            $referral->total_individual_count,
            $referral->total_demo_count,
            $referral->total_corporate_count,
            $referral->total_agent_count,
            $referral->mainUser->country->name ?? '-',
            $referral->getAppAccountManagerName(),
        ];
    }
}
