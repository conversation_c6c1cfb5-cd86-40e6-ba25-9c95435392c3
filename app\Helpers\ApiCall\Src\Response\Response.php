<?php

namespace App\Helpers\ApiCall\Src\Response;

use App\Helpers\ApiCall\Src\Handlers\Data;
use App\Helpers\ApiCall\Src\Handlers\Pagination;

class Response
{
    protected $code;

    protected bool $success;

    protected Data $data;

    protected $message = null;

    public function __construct($res)
    {
        $this->code = $res->status();
        $responseData = $res->collect();
        $this->success = (bool) $responseData['success'];
        $this->message = $responseData['message'] ?? null;
        $this->data = new Data($responseData['data']);
    }

    /**
     * @return mixed|null
     */
    public function getMessage(): mixed
    {
        return $this->message;
    }

    public function isSuccess(): bool
    {
        return (bool) $this->success;
    }

    public function getCode()
    {
        return $this->code;
    }

    public function data(): Data
    {
        return $this->data;
    }

    public function paginate()
    {
        return new Pagination($this->data->toArray());
    }
}
