<?php

namespace App\Enums;

enum TaxInvoiceStatusesEnum: int
{
    case Pending = 0;
    case Submitted = 1;
    case Rejected = 2;

    public static function toSelectArray(): array
    {
        return [
            self::Pending->value => self::Pending->getBadge(),
            self::Submitted->value => self::Submitted->getBadge(),
            self::Rejected->value => self::Rejected->getBadge(),
        ];
    }

    public function getBadge(): string
    {
        return match ($this) {
            self::Pending => '<span class="badge badge-warning">Pending</span>',
            self::Submitted => '<span class="badge badge-success">Submitted</span>',
            self::Rejected => '<span class="badge badge-danger">Rejected</span>',
        };
    }

    public function isPending(): bool
    {
        return $this->value === self::Pending->value;
    }
}
