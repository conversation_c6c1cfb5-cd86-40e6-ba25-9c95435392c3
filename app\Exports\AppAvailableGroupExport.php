<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use App\Http\Controllers\Admin\AppAvailableGroupController;

class AppAvailableGroupExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(AppAvailableGroupController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Application ID',
            'Username',
            'Group ID',
            'Group Name',
            'Created At',
            'Updated At',
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId() ?? '-',
            $row->application->mainUser?->full_name ?? '',
            $row->mt_group_id ?? '-',
            $row->mtGroup->name ?? '-',
            $row->created_at ?? '-',
            $row->updated_at ?? '-',
        ];
    }
}
