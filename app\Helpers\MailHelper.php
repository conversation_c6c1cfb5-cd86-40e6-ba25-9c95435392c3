<?php

namespace App\Helpers;

use Exception;
use Illuminate\Support\Facades\Mail;
use Throwable;
use App\Models\Role;
use App\Models\User;
use App\Jobs\MailingJob;
use App\Models\ErrorLog;
use App\Mail\NotifyNewOrder;
use App\Mail\NewTicketCreated;
use App\Mail\NewComplaintCreated;
use App\Models\AppParentRelation;
use App\Mail\NotifyNewJoinPartner;
use App\Mail\NotifyNewTransaction;
use App\Mail\NotifyUserJoinPartner;
use App\Mail\NewCompleteApplication;
use App\Mail\NotifyNewAppRegistered;
use App\Mail\NotifyTransferTransaction;

class MailHelper
{
    private static $locale;

    public function __construct()
    {
        $this->locale = 'en';
    }

    // send a deposit, withdrawal notifications
    public static function sendTransactionNotification($transaction)
    {
        try {
            if ($transaction) {
                $destinations[] = $transaction->role?->department_email;

                //add custom emails
                static::addCustomEmailByCountry($transaction->user, $destinations);

                static::addCustomEmailByAffiliateWebsite($transaction->user, $destinations);

                // get account manager related to the transaction
                $account_manager = $transaction->accountManager;
                if ($account_manager) {
                    // get manage of account manager
                    $departmentManagerEmail = AppParentRelation::getDepartmentManagerEmail((int) $account_manager->id);
                    if ($departmentManagerEmail) {
                        $destinations[] = $departmentManagerEmail;
                    }

                    $destinations[] = $transaction->accountManager?->mainUser?->email;
                }

                $destinations = array_filter(array_unique($destinations));

                if ($destinations) {
                    Mail::to($destinations)
                        ->locale(self::$locale)
                        ->send(new NotifyNewTransaction($transaction, self::$locale, auth()->user()));
                }
            }
        } catch (Throwable $e) {
            ErrorLog::query()->create([
                'user_id' => auth()->user()?->id,
                'application_id' => auth()->user()?->application_id,
                'file' => __FUNCTION__,
                'table_name' => 'Send email notification',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }
    }

    // send an internal transfer notifications
    public static function sendTransferNotification($transaction_type, $login, $amount, $currency, $user, $withdrawal_percentage = null)
    {
        try {
            $transaction_type = $transaction_type->name;

            // always destinations
            $destinations = Role::query()->whereIn('name', [
                'jr-dealing',
                'sr-customer-service',
            ])->where('department_email', '!=', null)
                ->pluck('department_email')
                ->toArray();

            //add custom emails
            static::addCustomEmailByCountry($user, $destinations);
            static::addCustomEmailByAffiliateWebsite($user, $destinations);

            // account manager
            $user_app_id = $user->application_id;
            $account_manager = User::getAccountManager($user_app_id);
            if ($account_manager) {
                $account_manager_email = $account_manager->email;
                $destinations[] = $account_manager_email;
            }

            $destinations = array_filter(array_unique($destinations));

            if ($destinations) {
                dispatch(
                    new MailingJob(
                        $destinations,
                        new NotifyTransferTransaction($transaction_type, $login, $amount, $currency, $user, self::$locale, $withdrawal_percentage, $user_app_id)
                    )
                )->onQueue(config('queue.connections.database.queue_notify'));
            }
        } catch (Exception $e) {
        }
    }

    // send an order notification
    public static function sendOrderNotification($order)
    {
        try {
            // always destinations
            $destinations = Role::query()->where('id', $order->role_id)
                ->where('department_email', '!=', null)
                ->pluck('department_email')
                ->toArray();

            //add custom emails
            static::addCustomEmailByCountry($order->user, $destinations);
            static::addCustomEmailByAffiliateWebsite($order->user, $destinations);

            // account manager
            $user_app_id = $order->application_id;
            $account_manager = User::getAccountManager($user_app_id);
            if ($account_manager) {
                $account_manager_email = $account_manager->email;
                $destinations[] = $account_manager_email;
            }

            $destinations = array_filter(array_unique($destinations));

            if ($destinations) {
                dispatch(new MailingJob($destinations, new NotifyNewOrder($order, self::$locale)))
                    ->onQueue(config('queue.connections.database.queue_notify'));
            }
        } catch (Exception $e) {
            ErrorLog::query()->create([
                'user_id' => auth()->user()?->id,
                'application_id' => auth()->user()?->application_id,
                'file' => __FUNCTION__,
                'table_name' => 'Send email notification',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }
    }

    // send a notification when a client open a new ticket
    public static function sendTicketNotification($ticket)
    {
        try {
            $destinations = [];

            //add custom emails
            static::addCustomEmailByCountry($ticket->user, $destinations);
            static::addCustomEmailByAffiliateWebsite($ticket->user, $destinations);

            $support_email = site_config('site_support_email');
            if ($support_email) {
                $destinations[] = $support_email;
            }

            // account manager
            $user_app_id = $ticket->user->application_id;
            $account_manager = User::getAccountManager($user_app_id);
            if ($account_manager) {
                $account_manager_email = $account_manager->email;
                $destinations[] = $account_manager_email;
            }

            $destinations = array_filter(array_unique($destinations));

            if ($destinations) {
                dispatch(new MailingJob($destinations, new NewTicketCreated($ticket, self::$locale)))
                    ->onQueue(config('queue.connections.database.queue_notify'));
            }
        } catch (Exception $e) {
            ErrorLog::query()->create([
                'user_id' => auth()->user()?->id,
                'application_id' => auth()->user()?->application_id,
                'file' => __FUNCTION__,
                'table_name' => 'Send email notification',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }
    }

    // send a notification when a client open a new complaint
    public static function sendComplaintNotification($complaint)
    {
        try {
            $destinations = [];

            //add custom emails
            static::addCustomEmailByCountry($complaint->user, $destinations);
            static::addCustomEmailByAffiliateWebsite($complaint->user, $destinations);

            $support_email = site_config('site_support_email');
            if ($support_email) {
                $destinations[] = $support_email;
            }

            // account manager
            $user_app_id = $complaint->user->application_id;
            $account_manager = User::getAccountManager($user_app_id);
            if ($account_manager) {
                $account_manager_email = $account_manager->email;
                $destinations[] = $account_manager_email;
            }

            $destinations = array_filter(array_unique($destinations));

            if ($destinations) {
                dispatch(new MailingJob($destinations, new NewComplaintCreated($complaint, self::$locale)))
                    ->onQueue(config('queue.connections.database.queue_notify'));
            }
        } catch (Exception $e) {
            ErrorLog::query()->create([
                'user_id' => auth()->user()?->id,
                'application_id' => auth()->user()?->application_id,
                'file' => __FUNCTION__,
                'table_name' => 'Send email notification',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }
    }

    // send a notification when somebody open a new application
    public static function newRegistrantEmailSend($user)
    {
        try {
            $destinations = Role::query()->whereIn('name', [
                'sr-customer-service',
            ])->where('department_email', '!=', null)
                ->pluck('department_email')
                ->toArray();

            //add custom emails
            static::addCustomEmailByCountry($user, $destinations);
            static::addCustomEmailByAffiliateWebsite($user, $destinations);

            $destinations = array_filter(array_unique($destinations));

            if ($destinations) {
                $subject = trans('emails.new_application');
                $mail = new NotifyNewAppRegistered($subject, $user);
                $mail->locale(self::$locale);
                dispatch(new MailingJob($destinations, $mail))
                    ->onQueue(config('queue.connections.database.queue_notify'));
            }
        } catch (Exception $e) {
            ErrorLog::query()->create([
                'user_id' => auth()->user()?->id,
                'application_id' => auth()->user()?->application_id,
                'file' => __FUNCTION__,
                'table_name' => 'Send email notification',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }
    }

    public static function sendJoinPartnerNotification($order)
    {
        try {
            $destinations = [];

            # account manager
            $account_manager = User::getAccountManager($order->application_id);
            if ($account_manager) {
                $account_manager_email = $account_manager->email;
                $destinations[] = $account_manager_email;
            }

            static::addCustomEmailByCountry($order->user, $destinations);

            $destinations = array_unique($destinations);

            if ($destinations) {
                dispatch(new MailingJob($destinations, new NotifyNewJoinPartner($order->user, self::$locale)))
                    ->onQueue(config('queue.connections.database.queue_notify'));
            }

            if ($order->user->email) {
                $userLocale = $order->user->language->abbreviation ?? 'en';
                dispatch(new MailingJob($order->user->email, new NotifyUserJoinPartner($order->user, $userLocale)))
                    ->onQueue(config('queue.connections.database.queue_notify'));
            }
        } catch (Exception $e) {
            ErrorLog::query()->create([
                'user_id' => auth()->user()?->id,
                'application_id' => auth()->user()?->application_id,
                'file' => __FUNCTION__,
                'table_name' => 'Send email notification',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }
    }

    public static function sendApplicationCompletionNotification($order)
    {
        try {
            $destinations = [];

            if ($order->role->department_email) {
                $destinations[] = $order->role->department_email;
            }

            $account_manager = User::getAccountManager($order->application_id);
            if ($account_manager) {
                $destinations[] = $account_manager->email;
            }

            //add custom emails
            static::addCustomEmailByAffiliateWebsite($order->user, $destinations);

            if ($destinations && $order->user) {
                dispatch(new MailingJob($destinations, new NewCompleteApplication($order->user)))
                    ->onQueue(config('queue.connections.database.queue_notify'));
            }
        } catch (Exception $e) {
            ErrorLog::query()->create([
                'user_id' => auth()->user()?->id,
                'application_id' => auth()->user()?->application_id,
                'file' => __FUNCTION__,
                'table_name' => 'Send email notification',
                'error' => $e->getMessage() ?? null,
                'ip_address' => ip_address(),
            ]);
        }
    }

    # add custom email to destinations emails by user
    private static function addCustomEmailByCountry($user, &$destinations)
    {
        $extra_destinations = match ($user->country->country_code) {
            'IR' => ['<EMAIL>'],
            default => [],
        };

        array_push($destinations, ...$extra_destinations);
    }

    // add custom email to destinations emails by affiliate website
    private static function addCustomEmailByAffiliateWebsite($user, &$destinations)
    {
        $host_name = $user->application->affiliateWebsite->name ?? host_name();
        $host_name = str_ireplace([
            '.local',
            'portal.',
            'crm.',
            'www.',
        ], '', $host_name);

        $ccEmails = match ($host_name) {
            'ingotbrokers.com.au' => ['<EMAIL>'],
            'ingotbrokersafrica.com' => ['<EMAIL>'],
            default => [],
        };

        foreach ($ccEmails as $email) {
            $destinations[] = $email;
        }
    }

    public static function addPDFAttachments($email, $attachmentPaths)
    {
        foreach ($attachmentPaths as $name => $path) {
            $email->attach($path, ['as' => "$name.pdf", 'mime' => 'application/pdf']);
        }

        return $email;
    }
}
