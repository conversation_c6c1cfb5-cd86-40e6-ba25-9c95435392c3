<?php

namespace App\Console\Commands;

use App\Models\Country;
use App\Models\Currency;
use App\Models\Language;
use App\Models\AccountForm;
use Illuminate\Console\Command;
use App\Models\AffiliateWebsite;
use App\Models\AffiliateWebsiteConfig;
use App\Models\AffiliateWebsiteTranslatableConfig;

class AddDomainConfigCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:config-domain';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create or update a domain and initialize its configuration settings';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $domainName = $this->ask('Enter the domain name', 'portal.ingotbrokers.cy.local');
        $isoCode = $this->ask('Enter the ISO code', 'cyprus');
        $deletedOld = $this->ask('Delete old domain', 'no');

        if (empty($domainName) || empty($isoCode)) {
            $this->error('Both domain name and ISO code are required.');

            return;
        }

        $hasEwallet = $this->ask('Does this domain has E-Wallet', 'yes');

        // Create or update the domain record
        $domain = AffiliateWebsite::query()->updateOrCreate([
            'name' => $domainName
        ], [
            'iso' => $isoCode,
            'crm_name' => $domainName,
            'site_name' => $domainName,
        ]);

        $this->info("Domain '{$domainName}' has been successfully created or updated (ID: {$domain->id}).");

        // delete if there is old data if we replaced the domain for numbering
        if ($deletedOld == 'yes') {
            $oldAccountForms = AccountForm::query()->whereJsonContains('websites', (string) $domain->id)->get();
            if ($oldAccountForms->isNotEmpty()) {
                $this->info('Found ' . $oldAccountForms->count() . ' old account forms for this domain.');
                foreach ($oldAccountForms as $oldAccountForm) {
                    $websites = json_decode($oldAccountForm->websites, true);
                    $oldAccountForm->websites = json_encode(array_diff($websites, [(string) $domain->id]));
                    $oldAccountForm->save();
                    $this->line("Old account form ID {$oldAccountForm->id} updated.");
                }
            }
        }

        // Retrieve all default configuration keys and their types
        $configTemplates = AffiliateWebsiteConfig::query()->pluck('type', 'key')->toArray();
        // Create or update config entries for the domain
        foreach ($configTemplates as $key => $type) {
            AffiliateWebsiteConfig::query()->updateOrCreate([
                'affiliate_website_id' => $domain->id,
                'key' => $key,
            ], [
                'value' => '',
                'type' => $type,
            ]);

            $this->line("Configuration '{$key}' initialized for domain ID {$domain->id}.");
        }

        // Retrieve all trans. default configuration keys and their types
        $transConfigTemplates = AffiliateWebsiteTranslatableConfig::query()->pluck('type', 'key')->toArray();
        // Create or update translatable config entries for the domain
        foreach ($transConfigTemplates as $key => $type) {
            AffiliateWebsiteTranslatableConfig::query()->updateOrCreate([
                'affiliate_website_id' => $domain->id,
                'key' => $key,
            ], [
                'value' => '',
                'type' => $type,
            ]);

            $this->line("Translatable configuration '{$key}' initialized for domain ID {$domain->id}.");
        }

        // add the new domain to the countries, websites json column
        $countries = Country::query()->whereJsonLength('websites', '>', 0)->get();
        $this->info('Found ' . $countries->count() . ' countries with websites.');
        foreach ($countries as $country) {
            $websites = $country->websites;

            if (!is_array($websites)) {
                $websites = [];
            }

            $websites[] = $domain->id;
            $country->websites = array_unique($websites);
            $country->save();
            $this->line("Domain ID {$domain->id} added to country ID {$country->id}.");
        }

        // add the new domain to the languages, websites json column
        $languages = Language::query()->whereJsonLength('websites', '>', 0)->get();
        $this->info('Found ' . $languages->count() . ' languages with websites.');
        foreach ($languages as $lang) {
            $websites = json_decode($lang->websites, true);
            if (!is_array($websites)) {
                $websites = [];
            }

            $websites[] = $domain->id;
            $lang->websites = json_encode(array_unique($websites));
            $lang->save();
            $this->line("Domain ID {$domain->id} added to language ID {$lang->id}.");
        }

        // add the new domain to the currencies, websites json column
        $currencies = Currency::query()->whereJsonLength('websites', '>', 0)->get();
        $this->info('Found ' . $currencies->count() . ' currencies with websites.');
        foreach ($currencies as $currency) {
            $websites = json_decode($currency->websites, true);
            if (!is_array($websites)) {
                $websites = [];
            }

            $websites[] = $domain->id;
            $currency->websites = json_encode(array_unique($websites));
            $currency->save();
            $this->line("Domain ID {$domain->id} added to currency ID {$currency->id}.");
        }

        // enable configurations
        if ($hasEwallet === 'yes') {
            AffiliateWebsiteConfig::query()->updateOrCreate([
                'affiliate_website_id' => $domain->id,
                'key' => 'ingot_wallet',
            ], [
                'value' => 'yes',
                'type' => 'text',
            ]);
        }

        $this->info('Domain configuration setup completed successfully.');
    }
}
