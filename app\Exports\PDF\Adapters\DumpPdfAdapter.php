<?php

namespace App\Exports\PDF\Adapters;

use App\Exports\PDF\PDF;
use Illuminate\Support\Arr;

class DumpPdfAdapter extends PDF
{
    /**
     * @return mixed
     */
    protected function template(): mixed
    {
        $pdf = app('dompdf.wrapper');
        $options = [
            'default_paper_orientation' => 'portrait',
            'encoding' => 'UTF-8',
            'dpi' => 150,
            'defaultFont' => 'sans-serif',
            'isRemoteEnabled' => true,
        ];
        $pdf->setOptions($options);
        if (!empty($this->view)) {
            return $pdf->loadView($this->view, ['data' => $this->data]);
        }
        $html = array_merge(Arr::wrap($this->header), Arr::wrap($this->body), Arr::wrap($this->footer));

        return $pdf->loadHtml(implode(' ', $html), 'utf-8');
    }
}
