<?php $__env->startSection('content'); ?>
    <div class="card">
        <a data-action="collapse">
            <div class="card-header">
                <h4 class="card-title"><?php echo e(__('content.search_filter')); ?></h4>
                <div class="heading-elements">
                    <ul class="list-inline mb-0">
                        <li><i class="la la-plus"></i></li>
                    </ul>
                </div>
            </div>
        </a>
        <div class="card-content card-filters collapse <?php echo e(collapsed_filter()); ?>">
            <div class="card-body">
                <form class="form-horizontal" action="<?php echo e(route('existing-users.index')); ?>" method="GET"
                    class="form-horizontal">
                    <div class="form-group row mb-0">
                        <div class="col-xl-3 col-lg-4 col-md-4 mb-1">
                            <input type="text" value="<?php echo e($request->get('email') ?? ''); ?>" name="email"
                                placeholder="<?php echo e(__('content.email')); ?>" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row mb-0">
                        <div class="col-xl-12 col-lg-12 col-md-12">
                            <button type="submit" class="btn btn-md btn-outline-dark btn-loading">
                                <i class="la la-search"></i> <?php echo e(__('content.search')); ?>

                            </button>
                            <a class="btn btn-outline-danger btn-loading"
                                href="<?php echo e(route('internal-notifications.index')); ?>">
                                <i class="la la-reply"></i> <?php echo e(__('content.reset')); ?>

                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title"><?php echo e(__('content.existing_users')); ?></h4>
        </div>

        <div class="card-content">
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <p><?php echo e($user->name); ?></p>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p><?php echo e(__('content.no_results_found')); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('templates.crm.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\projects\ingotbrokers\resources\views/templates/crm/admin/existing_users/index.blade.php ENDPATH**/ ?>