{"__meta": {"id": "01JW8FZYF5B566AC68HF0APPE0", "datetime": "2025-05-27 12:08:20", "utime": **********.581913, "method": "GET", "uri": "/en/roles", "ip": "::1"}, "php": {"version": "8.2.0", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748336899.743219, "end": **********.581925, "duration": 0.8387060165405273, "duration_str": "839ms", "measures": [{"label": "Booting", "start": 1748336899.743219, "relative_start": 0, "end": **********.123322, "relative_end": **********.123322, "duration": 0.*****************, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.123333, "relative_start": 0.****************, "end": **********.581926, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "459ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.126878, "relative_start": 0.*****************, "end": **********.131965, "relative_end": **********.131965, "duration": 0.0050868988037109375, "duration_str": "5.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.185677, "relative_start": 0.****************, "end": **********.581227, "relative_end": **********.581227, "duration": 0.***************, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "15MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "1x templates.crm.admin.role.index", "param_count": null, "params": [], "start": **********.187688, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/role/index.blade.phptemplates.crm.admin.role.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fadmin%2Frole%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.admin.role.index"}, {"name": "1x templates.pub.paginator", "param_count": null, "params": [], "start": **********.44885, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/pub/paginator.blade.phptemplates.pub.paginator", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fpub%2Fpaginator.blade.php&line=1", "ajax": false, "filename": "paginator.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.pub.paginator"}, {"name": "1x templates.crm.layout", "param_count": null, "params": [], "start": **********.454161, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/layout.blade.phptemplates.crm.layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.layout"}, {"name": "1x templates.pub.meta", "param_count": null, "params": [], "start": **********.460714, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/pub/meta.blade.phptemplates.pub.meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fpub%2Fmeta.blade.php&line=1", "ajax": false, "filename": "meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.pub.meta"}, {"name": "1x templates.crm.partials.admin-header", "param_count": null, "params": [], "start": **********.516045, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/admin-header.blade.phptemplates.crm.partials.admin-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Fadmin-header.blade.php&line=1", "ajax": false, "filename": "admin-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.admin-header"}, {"name": "1x templates.crm.partials.left-side-menu", "param_count": null, "params": [], "start": **********.538812, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/left-side-menu.blade.phptemplates.crm.partials.left-side-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Fleft-side-menu.blade.php&line=1", "ajax": false, "filename": "left-side-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.left-side-menu"}, {"name": "1x templates.crm.partials.trading-hub-admin-sidebar", "param_count": null, "params": [], "start": **********.53991, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/trading-hub-admin-sidebar.blade.phptemplates.crm.partials.trading-hub-admin-sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Ftrading-hub-admin-sidebar.blade.php&line=1", "ajax": false, "filename": "trading-hub-admin-sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.trading-hub-admin-sidebar"}, {"name": "1x templates.crm.partials.alerts", "param_count": null, "params": [], "start": **********.557762, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/alerts.blade.phptemplates.crm.partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.alerts"}, {"name": "1x templates.crm.partials.modals", "param_count": null, "params": [], "start": **********.576696, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/modals.blade.phptemplates.crm.partials.modals", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Fmodals.blade.php&line=1", "ajax": false, "filename": "modals.blade.php", "line": "?"}, "render_count": 1, "name_original": "templates.crm.partials.modals"}, {"name": "1x components.button", "param_count": null, "params": [], "start": **********.579077, "type": "blade", "hash": "bladeD:\\projects\\ingotbrokers\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.button"}]}, "route": {"uri": "GET en/roles", "middleware": "web, auth, password.expiry, localeSessionRedirect, localizationRedirect, localeViewPath, can:view-role, Ingotbrokers", "controller": "App\\Http\\Controllers\\Admin\\RoleController@index<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FRoleController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/en/roles", "as": "roles.index", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FRoleController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/RoleController.php:27-64</a>"}, "queries": {"count": 25, "nb_statements": 25, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.053959999999999994, "accumulated_duration_str": "53.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `name`, `permission` from `permissions` where `permissions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/RoleController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\RoleController.php", "line": 29}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.140024, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:29", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/RoleController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\RoleController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FRoleController.php&line=29", "ajax": false, "filename": "RoleController.php", "line": "29"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select count(*) as aggregate from `roles` where `roles`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/RoleController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\RoleController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1488059, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/RoleController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\RoleController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FRoleController.php&line=61", "ajax": false, "filename": "RoleController.php", "line": "61"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `roles`.*, (select count(*) from `users` where `roles`.`id` = `users`.`role_id` and `users`.`deleted_at` is null) as `users_count`, (select count(*) from `users` where `roles`.`id` = `users`.`role_id` and exists (select * from `applications` where `users`.`application_id` = `applications`.`id` and not exists (select * from `disabled_applications` where `applications`.`id` = `disabled_applications`.`application_id` and `disabled_applications`.`deleted_at` is null) and not exists (select * from `archived_applications` where `applications`.`id` = `archived_applications`.`application_id` and `archived_applications`.`deleted_at` is null) and not exists (select * from `suspended_applications` where `applications`.`id` = `suspended_applications`.`application_id` and `suspended_applications`.`deleted_at` is null) and `applications`.`deleted_at` is null) and `users`.`deleted_at` is null) as `active_users_count` from `roles` where `roles`.`deleted_at` is null order by `id` desc limit 50 offset 0", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/RoleController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\RoleController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.150485, "duration": 0.03139, "duration_str": "31.39ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/RoleController.php", "file": "D:\\projects\\ingotbrokers\\app\\Http\\Controllers\\Admin\\RoleController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FRoleController.php&line=61", "ajax": false, "filename": "RoleController.php", "line": "61"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `name` from `countries` where `id` in ('129', '211', '232', '111', '197') and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["129", "211", "232", "111", "197"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, {"index": 15, "namespace": "view", "name": "templates.crm.admin.role.index", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/role/index.blade.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.3371599, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Role.php:529", "source": {"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRole.php&line=529", "ajax": false, "filename": "Role.php", "line": "529"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `name` from `countries` where `id` in ('15') and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["15"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, {"index": 15, "namespace": "view", "name": "templates.crm.admin.role.index", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/role/index.blade.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.377447, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:529", "source": {"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRole.php&line=529", "ajax": false, "filename": "Role.php", "line": "529"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `name` from `countries` where `id` in ('30') and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["30"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, {"index": 15, "namespace": "view", "name": "templates.crm.admin.role.index", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/role/index.blade.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.3814502, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Role.php:529", "source": {"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRole.php&line=529", "ajax": false, "filename": "Role.php", "line": "529"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `name` from `countries` where `id` in ('196', '28', '35', '49', '50', '68', '81', '119', '128', '145', '147', '178', '238', '239') and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["196", "28", "35", "49", "50", "68", "81", "119", "128", "145", "147", "178", "238", "239"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, {"index": 15, "namespace": "view", "name": "templates.crm.admin.role.index", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/role/index.blade.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.385733, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:529", "source": {"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRole.php&line=529", "ajax": false, "filename": "Role.php", "line": "529"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `name` from `countries` where `id` in ('211') and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["211"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, {"index": 15, "namespace": "view", "name": "templates.crm.admin.role.index", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/role/index.blade.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.389931, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Role.php:529", "source": {"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRole.php&line=529", "ajax": false, "filename": "Role.php", "line": "529"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `name` from `countries` where `id` in ('1', '101') and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "101"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, {"index": 15, "namespace": "view", "name": "templates.crm.admin.role.index", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/admin/role/index.blade.php", "line": 142}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.43205, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:529", "source": {"index": 14, "namespace": null, "name": "app/Models/Role.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\Role.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRole.php&line=529", "ajax": false, "filename": "Role.php", "line": "529"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `user_preferences` where `user_id` = 1 and `user_preferences`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/UserPreferenceRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\UserPreferenceRepository.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 742}, {"index": 19, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 124}, {"index": 20, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 28}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}], "start": **********.451253, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "UserPreferenceRepository.php:27", "source": {"index": 16, "namespace": null, "name": "app/Repositories/UserPreferenceRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\UserPreferenceRepository.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FUserPreferenceRepository.php&line=27", "ajax": false, "filename": "UserPreferenceRepository.php", "line": "27"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where `languages`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 72}, {"index": 17, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 19, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 39}], "start": **********.456095, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:138", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FBaseRepository.php&line=138", "ajax": false, "filename": "BaseRepository.php", "line": "138"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where `languages`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, {"index": 16, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 72}, {"index": 17, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Helpers/CacheHelper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\CacheHelper.php", "line": 57}, {"index": 19, "namespace": null, "name": "app/Traits/Repositories/CacheableRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\Repositories\\CacheableRepository.php", "line": 39}], "start": **********.4616401, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:138", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\projects\\ingotbrokers\\app\\Repositories\\BaseRepository.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FRepositories%2FBaseRepository.php&line=138", "ajax": false, "filename": "BaseRepository.php", "line": "138"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `applications` where `applications`.`id` = 1 and `applications`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "D:\\projects\\ingotbrokers\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, {"index": 21, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.517316, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BuildsAdjacencyListQueries.php:26", "source": {"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "D:\\projects\\ingotbrokers\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fvendor%2Fstaudenmeir%2Flaravel-adjacency-list%2Fsrc%2FEloquent%2FTraits%2FBuildsAdjacencyListQueries.php&line=26", "ajax": false, "filename": "BuildsAdjacencyListQueries.php", "line": "26"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `affiliate_websites` where `affiliate_websites`.`id` = 3 and `affiliate_websites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 35}, {"index": 22, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.520067, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:35", "source": {"index": 21, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=35", "ajax": false, "filename": "IngotTrait.php", "line": "35"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `id` from `affiliate_websites` where (`name` = 'portal.ingotbrokers.local' or `site_name` = 'portal.ingotbrokers.local' or `crm_name` = 'portal.ingotbrokers.local' or json_contains(`aliases`, '\\\"portal.ingotbrokers.local\\\"')) and `affiliate_websites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["portal.ingotbrokers.local", "portal.ingotbrokers.local", "portal.ingotbrokers.local", "\"portal.ingotbrokers.local\""], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 78}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 72}, {"index": 22, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 41}], "start": **********.522065, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AffiliateWebsite.php:78", "source": {"index": 17, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=78", "ajax": false, "filename": "AffiliateWebsite.php", "line": "78"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `languages` where json_contains(`websites`, '\\\"3\\\"') and (`abbreviation` in ('en', 'ar', 'es', 'fa', 'vi', 'pt')) and `languages`.`deleted_at` is null limit 9223372036854775807", "type": "query", "params": [], "bindings": ["\"3\"", "en", "ar", "es", "fa", "vi", "pt"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, {"index": 16, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.5240948, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:117", "source": {"index": 15, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=117", "ajax": false, "filename": "IngotTrait.php", "line": "117"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `translations` where (`model_name` = 'Language' and `locale` = 'en') and `translations`.`object_id` in (1, 2, 7, 11, 15) and `translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["Language", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, {"index": 21, "namespace": null, "name": "app/Helpers/Helper.php", "file": "D:\\projects\\ingotbrokers\\app\\Helpers\\Helper.php", "line": 386}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "start": **********.52628, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "IngotTrait.php:117", "source": {"index": 20, "namespace": null, "name": "app/Traits/IngotTrait.php", "file": "D:\\projects\\ingotbrokers\\app\\Traits\\IngotTrait.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FTraits%2FIngotTrait.php&line=117", "ajax": false, "filename": "IngotTrait.php", "line": "117"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `id` from `order_types` where `key` = 'vacation_request' and `order_types`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["vacation_request"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 886}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 275}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.530942, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "UserPolicy.php:886", "source": {"index": 17, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 886}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FPolicies%2FUserPolicy.php&line=886", "ajax": false, "filename": "UserPolicy.php", "line": "886"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select exists(select * from `orders` where `order_type_id` = 22 and `user_id` = 1 and `orders`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [22, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 888}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 275}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.5336611, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "UserPolicy.php:888", "source": {"index": 11, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 888}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FPolicies%2FUserPolicy.php&line=888", "ajax": false, "filename": "UserPolicy.php", "line": "888"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `app_types` where `app_types`.`id` = 1 and `app_types`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 550}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}], "start": **********.5514278, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "UserPolicy.php:550", "source": {"index": 21, "namespace": null, "name": "app/Policies/UserPolicy.php", "file": "D:\\projects\\ingotbrokers\\app\\Policies\\UserPolicy.php", "line": 550}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FPolicies%2FUserPolicy.php&line=550", "ajax": false, "filename": "UserPolicy.php", "line": "550"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `temporary_assigned_leads` where `from_id` = 1 and (`canceled_at` is null and (`to_date` <= '2025-05-27' and `from_date` >= '2025-05-27') or `from_date` <= '2025-05-27 12:08:20' and `to_date` >= '2025-05-27 12:08:20') and `temporary_assigned_leads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, "2025-05-27", "2025-05-27", "2025-05-27 12:08:20", "2025-05-27 12:08:20"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 89}, {"index": 17, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.5556629, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MembersServiceProvider.php:89", "source": {"index": 16, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FMembersServiceProvider.php&line=89", "ajax": false, "filename": "MembersServiceProvider.php", "line": "89"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `user_2fa_devices` where `user_2fa_devices`.`user_id` = 1 and `user_2fa_devices`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "templates.crm.partials.alerts", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/alerts.blade.php", "line": 11}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.559025, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "templates.crm.partials.alerts:11", "source": {"index": 20, "namespace": "view", "name": "templates.crm.partials.alerts", "file": "D:\\projects\\ingotbrokers\\resources\\views/templates/crm/partials/alerts.blade.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fresources%2Fviews%2Ftemplates%2Fcrm%2Fpartials%2Falerts.blade.php&line=11", "ajax": false, "filename": "alerts.blade.php", "line": "11"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `affiliate_websites` where (`name` = 'portal.ingotbrokers.local' or `site_name` = 'portal.ingotbrokers.local' or `crm_name` = 'portal.ingotbrokers.local' or json_contains(`aliases`, '\\\"portal.ingotbrokers.local\\\"')) and `affiliate_websites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["portal.ingotbrokers.local", "portal.ingotbrokers.local", "portal.ingotbrokers.local", "\"portal.ingotbrokers.local\""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 172}], "start": **********.561807, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "AffiliateWebsite.php:60", "source": {"index": 16, "namespace": null, "name": "app/Models/AffiliateWebsite.php", "file": "D:\\projects\\ingotbrokers\\app\\Models\\AffiliateWebsite.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=60", "ajax": false, "filename": "AffiliateWebsite.php", "line": "60"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select * from `account_forms` left join ( SELECT  translations.object_id ,\nJSON_OBJECTAGG(translations.field_name, translations.value) as fields\nFROM translations\nWHERE translations.model_name = 'AccountForm'\nAND translations.locale = 'en'\nAND translations.value IS NOT NULL\nGROUP BY translations.object_id\n) as account_forms_trans on `account_forms_trans`.`object_id` = `account_forms`.`id` where `key` in ('pds', 'tmd', 'fsg', 'terms-and-conditions', 'privacy-policy', 'client-agreement', 'ib-agreement', 'ib-rules-and-conditions', 'client-service-agreement', 'complaint-handling-policy', 'best-execution-policy', 'referral-agreement', 'leverage-change-terms', 'cost-per-acquisition-agreement', 'addendum-to-the-ib-agreement', 'acknowledgment', 'jsc-legal-corporate-shared-docs') and (json_contains(`websites`, '[\\\"all\\\"]') or json_contains(`websites`, '\\\"3\\\"')) and (json_contains(`countries`, '[\\\"all\\\"]') or json_contains(`countries`, '\\\"109\\\"')) and `account_forms`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pds", "tmd", "fsg", "terms-and-conditions", "privacy-policy", "client-agreement", "ib-agreement", "ib-rules-and-conditions", "client-service-agreement", "complaint-handling-policy", "best-execution-policy", "referral-agreement", "leverage-change-terms", "cost-per-acquisition-agreement", "addendum-to-the-ib-agreement", "acknowledgment", "jsc-legal-corporate-shared-docs", "[\"all\"]", "\"3\"", "[\"all\"]", "\"109\""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 203}, {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 103}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.564401, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:203", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\AppServiceProvider.php", "line": 203}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FAppServiceProvider.php&line=203", "ajax": false, "filename": "AppServiceProvider.php", "line": "203"}, "connection": "ingot-multiserver", "explain": null}, {"sql": "select `app_wallets`.* from `app_wallets` inner join (select MIN(`app_wallets`.`id`) as `id_aggregate`, `app_wallets`.`application_id` from `app_wallets` where `app_wallets`.`application_id` = 1 and `app_wallets`.`application_id` is not null and `app_wallets`.`deleted_at` is null group by `app_wallets`.`application_id`) as `oldestOfMany` on `oldestOfMany`.`id_aggregate` = `app_wallets`.`id` and `oldestOfMany`.`application_id` = `app_wallets`.`application_id` where `app_wallets`.`application_id` = 1 and `app_wallets`.`application_id` is not null and `app_wallets`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 64}, {"index": 22, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\ingotbrokers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.573653, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "MembersServiceProvider.php:64", "source": {"index": 21, "namespace": null, "name": "app/Providers/MembersServiceProvider.php", "file": "D:\\projects\\ingotbrokers\\app\\Providers\\MembersServiceProvider.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FProviders%2FMembersServiceProvider.php&line=64", "ajax": false, "filename": "MembersServiceProvider.php", "line": "64"}, "connection": "ingot-multiserver", "explain": null}]}, "models": {"data": {"App\\Models\\Role": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Language": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\AccountForm": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAccountForm.php&line=1", "ajax": false, "filename": "AccountForm.php", "line": "?"}}, "App\\Models\\AffiliateWebsite": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAffiliateWebsite.php&line=1", "ajax": false, "filename": "AffiliateWebsite.php", "line": "?"}}, "App\\Models\\UserPreference": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FUserPreference.php&line=1", "ajax": false, "filename": "UserPreference.php", "line": "?"}}, "App\\Models\\Application": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FApplication.php&line=1", "ajax": false, "filename": "Application.php", "line": "?"}}, "App\\Models\\OrderType": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FOrderType.php&line=1", "ajax": false, "filename": "OrderType.php", "line": "?"}}, "App\\Models\\AppType": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAppType.php&line=1", "ajax": false, "filename": "AppType.php", "line": "?"}}, "App\\Models\\AppWallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FModels%2FAppWallet.php&line=1", "ajax": false, "filename": "AppWallet.php", "line": "?"}}}, "count": 108, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:27 [\n    \"id\" => 1\n    \"first_name\" => \"System\"\n    \"last_name\" => \"Admin\"\n    \"email\" => \"<EMAIL>\"\n    \"gender_id\" => 1\n    \"application_id\" => 1\n    \"country_id\" => 109\n    \"citizenship_id\" => 109\n    \"zip_code\" => null\n    \"birthdate\" => \"1991-05-12\"\n    \"phone\" => \"962-788287729\"\n    \"city\" => \"Amman\"\n    \"address\" => \"Jordan\"\n    \"profile_pic\" => null\n    \"role_id\" => 1\n    \"verified\" => \"1\"\n    \"created_at\" => \"2019-02-12T12:54:33.000000Z\"\n    \"updated_at\" => \"2025-03-13T12:30:37.000000Z\"\n    \"deleted_at\" => null\n    \"pref_lang\" => 2\n    \"timezone_id\" => null\n    \"not_deleted\" => 1\n    \"google2fa_secret\" => null\n    \"timezone\" => null\n    \"role\" => array:18 [\n      \"id\" => 1\n      \"name\" => \"admin\"\n      \"display_name\" => \"System Admin\"\n      \"ip_addresses\" => []\n      \"is_department\" => 0\n      \"department_email\" => \"<EMAIL>\"\n      \"is_manager\" => 0\n      \"permissions\" => \"[\"create-affiliate_website\", \"edit-affiliate_website\", \"delete-affiliate_website\", \"view-affiliate_website\", \"create-application\", \"edit-application\", \"delete-application\", \"view-application\", \"create-manage_application\", \"edit-manage_application\", \"delete-manage_application\", \"view-manage_application\", \"export-manage_application\", \"export-pdf-manage_application\", \"enable-disable-manage_application\", \"store-disable-manage_application\", \"duplicate-manage_application\", \"save-contact-reminder-manage_application\", \"archive-manage_application\", \"store-archive-manage_application\", \"suspend-manage_application\", \"activity-manage_application\", \"security-preferences-app-manage_application\", \"create-app_payment_bank\", \"edit-app_payment_bank\", \"delete-app_payment_bank\", \"view-app_payment_bank\", \"export-app_payment_bank\", \"create-app_payment_gateway\", \"edit-app_payment_gateway\", \"delete-app_payment_gateway\", \"view-app_payment_gateway\", \"export-app_payment_gateway\", \"create-app_type\", \"edit-app_type\", \"delete-app_type\", \"view-app_type\", \"export-app_type\", \"create-app_wallet\", \"edit-app_wallet\", \"delete-app_wallet\", \"view-app_wallet\", \"create-asic_answer\", \"edit-asic_answer\", \"delete-asic_answer\", \"view-asic_answer\", \"export-asic_answer\", \"create-asic_question\", \"edit-asic_question\", \"delete-asic_question\", \"view-asic_question\", \"export-asic_question\", \"create-company\", \"edit-company\", \"delete-company\", \"view-company\", \"create-company_holder\", \"edit-company_holder\", \"delete-company_holder\", \"view-company_holder\", \"create-contact_request\", \"edit-contact_request\", \"delete-contact_request\", \"view-contact_request\", \"view-assign-contact_request\", \"create-country\", \"edit-country\", \"delete-country\", \"view-country\", \"export-country\", \"create-currency\", \"edit-currency\", \"delete-currency\", \"view-currency\", \"export-currency\", \"create-feedback\", \"edit-feedback\", \"delete-feedback\", \"view-feedback\", \"create-mt_group\", \"edit-mt_group\", \"delete-mt_group\", \"view-mt_group\", \"export-mt_group\", \"create-ib_offer\", \"edit-ib_offer\", \"delete-ib_offer\", \"view-ib_offer\", \"create-index_dividend\", \"edit-index_dividend\", \"delete-index_dividend\", \"view-index_dividend\", \"create-contract\", \"edit-contract\", \"delete-contract\", \"view-contract\", \"create-mt_account\", \"edit-mt_account\", \"delete-mt_account\", \"view-mt_account\", \"hide-mt_account\", \"disable-mt_account\", \"create-manage_mt_account\", \"edit-manage_mt_account\", \"delete-manage_mt_account\", \"view-manage_mt_account\", \"export-manage_mt_account\", \"internal-transfer-manage_mt_account\", \"internal-transfer-request-manage_mt_account\", \"create-credit-request-manage_mt_account\", \"store-credit-request-manage_mt_account\", \"create-wl-real-account-manage_mt_account\", \"create-wl-demo-account-manage_mt_account\", \"export-deals-manage_mt_account\", \"export-positions-manage_mt_account\", \"export-orders-manage_mt_account\", \"export-order-history-manage_mt_account\", \"export-commissions-manage_mt_account\", \"internal-transfer-email-request-manage_mt_account\", \"export-transactions-manage_mt_account\", \"not-synced-filter-manage_mt_account\", \"request-swap-free-mt_account\", \"request-change-leverage-mt_account\", \"create-order\", \"edit-order\", \"delete-order\", \"view-order\", \"export-order\", \"create-manage_order\", \"edit-manage_order\", \"delete-manage_order\", \"view-manage_order\", \"export-manage_order\", \"create-page\", \"edit-page\", \"delete-page\", \"view-page\", \"create-payment_gateway\", \"edit-payment_gateway\", \"delete-payment_gateway\", \"view-payment_gateway\", \"export-payment_gateway\", \"create-product_category\", \"edit-product_category\", \"delete-product_category\", \"view-product_category\", \"export-product_category\", \"create-referral\", \"edit-referral\", \"delete-referral\", \"view-referral\", \"create-role\", \"edit-role\", \"delete-role\", \"view-role\", \"export-role\", \"create-subscription\", \"edit-subscription\", \"delete-subscription\", \"view-subscription\", \"create-swap_category\", \"edit-swap_category\", \"delete-swap_category\", \"view-swap_category\", \"create-swap\", \"edit-swap\", \"delete-swap\", \"view-swap\", \"create-temporary_fund\", \"edit-temporary_fund\", \"delete-temporary_fund\", \"view-temporary_fund\", \"execute-temporary_fund\", \"generate-bill-number-temporary_fund\", \"create-transaction\", \"edit-transaction\", \"delete-transaction\", \"view-transaction\", \"export-transaction\", \"edit-after-execute-transaction\", \"create-manage_transaction\", \"edit-manage_transaction\", \"delete-manage_transaction\", \"view-manage_transaction\", \"export-manage_transaction\", \"wallet-summary-recalculate-manage_transaction\", \"toggle-visibility-transaction\", \"create-missing-transaction\", \"create-user\", \"edit-user\", \"delete-user\", \"view-user\", \"login-as-user\", \"create-emails_crud\", \"edit-emails_crud\", \"delete-emails_crud\", \"view-emails_crud\", \"create-user_document\", \"edit-user_document\", \"delete-user_document\", \"view-user_document\", \"id-wise-user_document\", \"create-user_eligibility\", \"edit-user_eligibility\", \"delete-user_eligibility\", \"view-user_eligibility\", \"export-user_eligibility\", \"create-language\", \"edit-language\", \"delete-language\", \"view-language\", \"create-market_report\", \"edit-market_report\", \"delete-market_report\", \"view-market_report\", \"send-market_report\", \"export-market_report\", \"send-email-market_report\", \"create-market_report_type\", \"edit-market_report_type\", \"delete-market_report_type\", \"view-market_report_type\", \"create-market_report_instrument\", \"edit-market_report_instrument\", \"delete-market_report_instrument\", \"view-market_report_instrument\", \"create-slider\", \"edit-slider\", \"delete-slider\", \"view-slider\", \"export-slider\", \"create-trading_platform\", \"edit-trading_platform\", \"delete-trading_platform\", \"view-trading_platform\", \"create-instrument\", \"edit-instrument\", \"delete-instrument\", \"view-instrument\", \"export-instrument\", \"create-permission\", \"edit-permission\", \"delete-permission\", \"view-permission\", \"export-permission\", \"create-account_form\", \"edit-account_form\", \"delete-account_form\", \"view-account_form\", \"export-account_form\", \"create-account_form_category\", \"edit-account_form_category\", \"delete-account_form_category\", \"view-account_form_category\", \"create-app_relation\", \"edit-app_relation\", \"delete-app_relation\", \"view-app_relation\", \"view-bulk-assign-app_relation\", \"create-seos\", \"edit-seos\", \"delete-seos\", \"view-seos\", \"create-user_login_log\", \"edit-user_login_log\", \"delete-user_login_log\", \"view-user_login_log\", \"export-user_login_log\", \"view-api_key_access_event\", \"view-payment_method_webhook_log\", \"view-webhook_log\", \"view-cron_job_logs\", \"view-activity_log\", \"documents-activity-activity_log\", \"view-third_party_log\", \"view-dynamic_email_history\", \"create-app_relation_country_assign\", \"edit-app_relation_country_assign\", \"delete-app_relation_country_assign\", \"view-app_relation_country_assign\", \"view-group_analytic\", \"view-application_analytic\", \"view-mt_account_analytic\", \"view-transaction_analytic\", \"view-market_reports_analytic\", \"view-ticket_analytic\", \"view-market_alert\", \"view-fraud_dashboard\", \"create-faq\", \"edit-faq\", \"delete-faq\", \"view-faq\", \"export-faq\", \"create-contact_request_subject\", \"edit-contact_request_subject\", \"delete-contact_request_subject\", \"view-contact_request_subject\", \"export-contact_request_subject\", \"create-error_log\", \"edit-error_log\", \"delete-error_log\", \"view-error_log\", \"export-error_log\", \"create-affiliate_website_config\", \"edit-affiliate_website_config\", \"delete-affiliate_website_config\", \"view-affiliate_website_config\", \"create-mt_group_spec\", \"edit-mt_group_spec\", \"delete-mt_group_spec\", \"view-mt_group_spec\", \"create-mt_group_forward\", \"edit-mt_group_forward\", \"delete-mt_group_forward\", \"view-mt_group_forward\", \"create-ticket\", \"edit-ticket\", \"delete-ticket\", \"view-ticket\", \"export-ticket\", \"create-manage_ticket\", \"edit-manage_ticket\", \"delete-manage_ticket\", \"view-manage_ticket\", \"create-corporate_event\", \"edit-corporate_event\", \"delete-corporate_event\", \"view-corporate_event\", \"view-import-corporate_event\", \"create-trading_platform_numbering\", \"edit-trading_platform_numbering\", \"delete-trading_platform_numbering\", \"view-trading_platform_numbering\", \"create-promotion_slider\", \"edit-promotion_slider\", \"delete-promotion_slider\", \"view-promotion_slider\", \"export-promotion_slider\", \"create-regulation_entity\", \"edit-regulation_entity\", \"delete-regulation_entity\", \"view-regulation_entity\", \"create-app_opportunity_option\", \"edit-app_opportunity_option\", \"delete-app_opportunity_option\", \"view-app_opportunity_option\", \"create-opportunity_option\", \"edit-opportunity_option\", \"delete-opportunity_option\", \"view-opportunity_option\", \"create-comment\", \"edit-comment\", \"delete-comment\", \"view-comment\", \"create-contact\", \"edit-contact\", \"delete-contact\", \"view-contact\", \"view-assign-contact\", \"create-trading_platform_configuration\", \"edit-trading_platform_configuration\", \"delete-trading_platform_configuration\", \"view-trading_platform_configuration\", \"create-job\", \"edit-job\", \"delete-job\", \"view-job\", \"create-site_banking_detail\", \"edit-site_banking_detail\", \"delete-site_banking_detail\", \"view-site_banking_detail\", \"create-watch_list\", \"edit-watch_list\", \"delete-watch_list\", \"view-watch_list\", \"create-translation\", \"edit-translation\", \"delete-translation\", \"view-translation\", \"create-institutional_service\", \"edit-institutional_service\", \"delete-institutional_service\", \"view-institutional_service\", \"create-institutional_contact_request\", \"edit-institutional_contact_request\", \"delete-institutional_contact_request\", \"view-institutional_contact_request\", \"view-assign-institutional_contact_request\", \"create-contact_reminder\", \"edit-contact_reminder\", \"delete-contact_reminder\", \"view-contact_reminder\", \"view-calendar-contact_reminder\", \"create-user_session\", \"edit-user_session\", \"delete-user_session\", \"view-user_session\", \"view-equity_report\", \"export-equity_report\", \"view-open_position_report\", \"export-open_position_report\", \"view-liquidation_report\", \"export-liquidation_report\", \"view-scalping_report\", \"export-scalping_report\", \"store-comment-scalping_report\", \"view-wallet_report\", \"export-wallet_report\", \"view-duplicate_application_report\", \"view-index_dividends_reports\", \"view-closed_position_report\", \"export-closed_position_report\", \"view-portal_login_report\", \"export-portal_login_report\", \"view-deposit_and_withdrawal_report\", \"export-deposit_and_withdrawal_report\", \"view-payment_method_report\", \"export-payment_method_report\", \"view-ticket_report\", \"export-ticket_report\", \"view-referral_report\", \"export-referral_report\", \"view-retention_report\", \"export-retention_report\", \"view-campaign_report\", \"view-expired_account_report\", \"export-expired_account_report\", \"view-gainers_and_losers_report\", \"export-gainers_and_losers_report\", \"kpi-retention_report\", \"view-acquisition_report\", \"daily-kpi-acquisition_report\", \"monthly-kpi-acquisition_report\", \"view-client_timeline_report\", \"export-client_timeline_report\", \"view-corporate_events_report\", \"view-risk_kpi\", \"view-mt_pending_orders\", \"export-mt_pending_orders\", \"view-mt_wallet_report\", \"export-mt_wallet_report\", \"view-agent_transaction_report\", \"export-agent_transaction_report\", \"view-aml_report\", \"export-aml_report\", \"view-app_relation_report\", \"export-app_relation_report\", \"view-mt_prices\", \"view-sales_deposit_withdrawal_report\", \"export-sales_deposit_withdrawal_report\", \"view-payment_gateway_equity\", \"export-payment_gateway_equity\", \"view-client_retention_report\", \"export-client_retention_report\", \"view-leads_widgets\", \"view-leads_report\", \"export-leads_report\", \"view-agent_activity_report\", \"export-agent_activity_report\", \"view-sales_summary\", \"export-sales_summary\", \"view-call_center\", \"view-risk_hedging_kpi\", \"view-risk_product_kpi\", \"view-deposits_and_withdrawals_summary\", \"lead-dashboard-widget-leads_report\", \"view-clients_deposit_withdrawal_report\", \"export-clients_deposit_withdrawal_report\", \"view-dashboard_kpi\", \"create-user_log\", \"edit-user_log\", \"delete-user_log\", \"view-user_log\", \"create-mt_agent_restriction\", \"edit-mt_agent_restriction\", \"delete-mt_agent_restriction\", \"view-mt_agent_restriction\", \"export-mt_agent_restriction\", \"create-app_available_group\", \"edit-app_available_group\", \"delete-app_available_group\", \"view-app_available_group\", \"create-affiliate_website_translatable_config\", \"edit-affiliate_website_translatable_config\", \"delete-affiliate_website_translatable_config\", \"view-affiliate_website_translatable_config\", \"create-faq_category\", \"edit-faq_category\", \"delete-faq_category\", \"view-faq_category\", \"create-career\", \"edit-career\", \"delete-career\", \"view-career\", \"create-payment_gateway_exception\", \"edit-payment_gateway_exception\", \"delete-payment_gateway_exception\", \"view-payment_gateway_exception\", \"export-payment_gateway_exception\", \"view-call-center\", \"click-to-call-call-center\", \"create-voip_extension\", \"edit-voip_extension\", \"delete-voip_extension\", \"view-voip_extension\", \"create-campaign-lead\", \"edit-campaign-lead\", \"delete-campaign-lead\", \"view-campaign-lead\", \"export-campaign-lead\", \"view-assign-campaign-lead\", \"view-import-campaign-lead\", \"create-competition_ticket\", \"edit-competition_ticket\", \"delete-competition_ticket\", \"view-competition_ticket\", \"export-competition_ticket\", \"create-payment_gateway_config\", \"edit-payment_gateway_config\", \"delete-payment_gateway_config\", \"view-payment_gateway_config\", \"create-partnership_lead\", \"edit-partnership_lead\", \"delete-partnership_lead\", \"view-partnership_lead\", \"create-affiliate_website_exception\", \"edit-affiliate_website_exception\", \"delete-affiliate_website_exception\", \"view-affiliate_website_exception\", \"create-mt_group_spec_swap\", \"edit-mt_group_spec_swap\", \"delete-mt_group_spec_swap\", \"view-mt_group_spec_swap\", \"create-mt_group_allocation\", \"edit-mt_group_allocation\", \"delete-mt_group_allocation\", \"view-mt_group_allocation\", \"create-educational_material_category\", \"edit-educational_material_category\", \"delete-educational_material_category\", \"view-educational_material_category\", \"create-educational_material\", \"edit-educational_material\", \"delete-educational_material\", \"view-educational_material\", \"export-educational_material\", \"create-api_key\", \"edit-api_key\", \"delete-api_key\", \"view-api_key\", \"create-api_key_access_event\", \"edit-api_key_access_event\", \"delete-api_key_access_event\", \"create-email_tags\", \"edit-email_tags\", \"delete-email_tags\", \"view-email_tags\", \"export-email_tags\", \"create-bank_trans_specs\", \"edit-bank_trans_specs\", \"delete-bank_trans_specs\", \"view-bank_trans_specs\", \"create-educational_article\", \"edit-educational_article\", \"delete-educational_article\", \"view-educational_article\", \"export-educational_article\", \"create-order_type\", \"edit-order_type\", \"delete-order_type\", \"view-order_type\", \"create-gender\", \"edit-gender\", \"delete-gender\", \"view-gender\", \"export-gender\", \"create-user_title\", \"edit-user_title\", \"delete-user_title\", \"view-user_title\", \"export-user_title\", \"create-marital_status\", \"edit-marital_status\", \"delete-marital_status\", \"view-marital_status\", \"export-marital_status\", \"create-job_position\", \"edit-job_position\", \"delete-job_position\", \"view-job_position\", \"export-job_position\", \"create-business_sector\", \"edit-business_sector\", \"delete-business_sector\", \"view-business_sector\", \"export-business_sector\", \"create-professionalism\", \"edit-professionalism\", \"delete-professionalism\", \"view-professionalism\", \"export-professionalism\", \"create-source_of_fund\", \"edit-source_of_fund\", \"delete-source_of_fund\", \"view-source_of_fund\", \"export-source_of_fund\", \"create-company_type\", \"edit-company_type\", \"delete-company_type\", \"view-company_type\", \"export-company_type\", \"create-ticket_tag\", \"edit-ticket_tag\", \"delete-ticket_tag\", \"view-ticket_tag\", \"create-webinar\", \"edit-webinar\", \"delete-webinar\", \"view-webinar\", \"export-webinar\", \"view-assign-webinar\", \"create-campaign_template\", \"edit-campaign_template\", \"delete-campaign_template\", \"view-campaign_template\", \"create-campaign\", \"edit-campaign\", \"delete-campaign\", \"view-campaign\", \"export-campaign\", \"create-tutorial_video\", \"edit-tutorial_video\", \"delete-tutorial_video\", \"view-tutorial_video\", \"export-tutorial_video\", \"create-notification\", \"edit-notification\", \"delete-notification\", \"view-notification\", \"create-application_status_tag\", \"edit-application_status_tag\", \"delete-application_status_tag\", \"view-application_status_tag\", \"create-province\", \"edit-province\", \"delete-province\", \"view-province\", \"export-province\", \"create-city\", \"edit-city\", \"delete-city\", \"view-city\", \"export-city\", \"create-mt_tags\", \"edit-mt_tags\", \"delete-mt_tags\", \"view-mt_tags\", \"create-mail_templates\", \"edit-mail_templates\", \"delete-mail_templates\", \"view-mail_templates\", \"view-call_requests\", \"storeCallRequest-call_requests\", \"create-call_requests\", \"edit-call_requests\", \"delete-call_requests\", \"create-classification_option\", \"edit-classification_option\", \"delete-classification_option\", \"view-classification_option\", \"create-app_classification\", \"edit-app_classification\", \"delete-app_classification\", \"view-app_classification\", \"create-group_tag\", \"edit-group_tag\", \"delete-group_tag\", \"view-group_tag\", \"create-withdrawal_transaction_internal_systems\", \"edit-withdrawal_transaction_internal_systems\", \"delete-withdrawal_transaction_internal_systems\", \"view-withdrawal_transaction_internal_systems\", \"create-deposit_transaction_internal_systems\", \"edit-deposit_transaction_internal_systems\", \"delete-deposit_transaction_internal_systems\", \"view-deposit_transaction_internal_systems\", \"view-view_fcm_tokens\", \"clear-tokens-view_fcm_tokens\", \"create-payment_method_webhook_log\", \"edit-payment_method_webhook_log\", \"delete-payment_method_webhook_log\", \"create-seminar\", \"edit-seminar\", \"delete-seminar\", \"view-seminar\", \"export-seminar\", \"create-newsroom\", \"edit-newsroom\", \"delete-newsroom\", \"view-newsroom\", \"export-newsroom\", \"create-transaction_type\", \"edit-transaction_type\", \"delete-transaction_type\", \"view-transaction_type\", \"create-social_information\", \"edit-social_information\", \"delete-social_information\", \"view-social_information\", \"create-document_type\", \"edit-document_type\", \"delete-document_type\", \"view-document_type\", \"create-sub_branch\", \"edit-sub_branch\", \"delete-sub_branch\", \"view-sub_branch\", \"create-contact_branch_detail\", \"edit-contact_branch_detail\", \"delete-contact_branch_detail\", \"view-contact_branch_detail\", \"create-branch\", \"edit-branch\", \"delete-branch\", \"view-branch\", \"create-whitelabel_transaction\", \"edit-whitelabel_transaction\", \"delete-whitelabel_transaction\", \"view-whitelabel_transaction\", \"transactions-history-whitelabel_transaction\", \"pending-transactions-whitelabel_transaction\", \"execute-whitelabel_transaction\", \"internal-transfer-request-whitelabel_transaction\", \"create-whitelabel_crud\", \"edit-whitelabel_crud\", \"delete-whitelabel_crud\", \"view-whitelabel_crud\", \"create-popular_instruments\", \"edit-popular_instruments\", \"delete-popular_instruments\", \"view-popular_instruments\", \"create-mt_group_shared\", \"edit-mt_group_shared\", \"delete-mt_group_shared\", \"view-mt_group_shared\", \"create-account_type\", \"edit-account_type\", \"delete-account_type\", \"view-account_type\", \"create-webhook_log\", \"edit-webhook_log\", \"delete-webhook_log\", \"create-user_archived_chats\", \"edit-user_archived_chats\", \"delete-user_archived_chats\", \"view-user_archived_chats\", \"all-chats-user_archived_chats\", \"create-flag_type\", \"edit-flag_type\", \"delete-flag_type\", \"view-flag_type\", \"export-flag_type\", \"create-new_license_confirmation\", \"edit-new_license_confirmation\", \"delete-new_license_confirmation\", \"view-new_license_confirmation\", \"create-user_term_confirmation\", \"edit-user_term_confirmation\", \"delete-user_term_confirmation\", \"view-user_term_confirmation\", \"create-cron_job_logs\", \"edit-cron_job_logs\", \"delete-cron_job_logs\", \"view-application-activity_log\", \"create-material_category\", \"edit-material_category\", \"delete-material_category\", \"view-material_category\", \"export-material_category\", \"create-material_dimension\", \"edit-material_dimension\", \"delete-material_dimension\", \"view-material_dimension\", \"create-marketing_material\", \"edit-marketing_material\", \"delete-marketing_material\", \"view-marketing_material\", \"create-internal_requests\", \"edit-internal_requests\", \"delete-internal_requests\", \"view-internal_requests\", \"create-timezone\", \"edit-timezone\", \"delete-timezone\", \"view-timezone\", \"view-commission_system_config\", \"create-commission_system_config\", \"edit-commission_system_config\", \"delete-commission_system_config\", \"create-country_entity_risk_level\", \"edit-country_entity_risk_level\", \"delete-country_entity_risk_level\", \"view-country_entity_risk_level\", \"create-affiliate_website_config_key\", \"edit-affiliate_website_config_key\", \"delete-affiliate_website_config_key\", \"view-affiliate_website_config_key\", \"edit-website-keys-affiliate_website_config_key\", \"update-website-keys-affiliate_website_config_key\", \"create-media\", \"edit-media\", \"delete-media\", \"view-media\", \"view-bulk_sms\", \"send-sms-bulk_sms\", \"create-bulk_sms\", \"edit-bulk_sms\", \"delete-bulk_sms\", \"create-offer_term\", \"edit-offer_term\", \"delete-offer_term\", \"view-offer_term\", \"create-security\", \"edit-security\", \"delete-security\", \"view-security\", \"create-symbol\", \"edit-symbol\", \"delete-symbol\", \"view-symbol\", \"create-symbol_group\", \"edit-symbol_group\", \"delete-symbol_group\", \"view-symbol_group\", \"create-offer_scenario\", \"edit-offer_scenario\", \"delete-offer_scenario\", \"view-offer_scenario\", \"clone-offer_scenario\", \"create-app_offer\", \"edit-app_offer\", \"delete-app_offer\", \"view-app_offer\", \"export-pdf-app_offer\", \"send-offer-app_offer\", \"clone-app_offer\", \"change-deduct-scalping-app_offer\", \"store-agent-category-app_offer\", \"view-agent-category-app_offer\", \"deactivate-offer-app_offer\", \"view-tree-app_offer\", \"export-app_offer\", \"create-commission_transaction\", \"edit-commission_transaction\", \"delete-commission_transaction\", \"view-commission_transaction\", \"export-commission_transaction\", \"create-application_sales_commission\", \"edit-application_sales_commission\", \"delete-application_sales_commission\", \"view-application_sales_commission\", \"create-offer_scenario_detail\", \"edit-offer_scenario_detail\", \"delete-offer_scenario_detail\", \"view-offer_scenario_detail\", \"index-details-offer_scenario_detail\", \"create-offer_scenario_groups\", \"edit-offer_scenario_groups\", \"delete-offer_scenario_groups\", \"view-offer_scenario_groups\", \"create-agent_categories\", \"edit-agent_categories\", \"delete-agent_categories\", \"view-agent_categories\", \"create-webinar-leads\", \"edit-webinar-leads\", \"delete-webinar-leads\", \"view-webinar-leads\", \"export-webinar-leads\", \"view-assign-webinar-leads\", \"view-import-webinar-leads\", \"create-seminar-leads\", \"edit-seminar-leads\", \"delete-seminar-leads\", \"view-seminar-leads\", \"export-seminar-leads\", \"view-assign-seminar-leads\", \"view-import-seminar-leads\", \"create-app_available_offer\", \"edit-app_available_offer\", \"delete-app_available_offer\", \"view-app_available_offer\", \"create-cellxpert-default-rules\", \"edit-cellxpert-default-rules\", \"delete-cellxpert-default-rules\", \"view-cellxpert-default-rules\", \"create-cellxpert-sales-rules\", \"edit-cellxpert-sales-rules\", \"delete-cellxpert-sales-rules\", \"view-cellxpert-sales-rules\", \"create-bitcoin_address\", \"edit-bitcoin_address\", \"delete-bitcoin_address\", \"view-bitcoin_address\", \"create-department\", \"edit-department\", \"delete-department\", \"view-department\", \"create-political_questions\", \"edit-political_questions\", \"delete-political_questions\", \"view-political_questions\", \"export-political_questions\", \"create-cronjob_configuration\", \"edit-cronjob_configuration\", \"delete-cronjob_configuration\", \"view-cronjob_configuration\", \"create-trading_server\", \"edit-trading_server\", \"delete-trading_server\", \"view-trading_server\", \"create-trading_server_config\", \"edit-trading_server_config\", \"delete-trading_server_config\", \"view-trading_server_config\", \"view-mt_sync_exception\", \"create-oauth_client\", \"edit-oauth_client\", \"delete-oauth_client\", \"view-oauth_client\", \"create-oauth_client_config_key\", \"edit-oauth_client_config_key\", \"delete-oauth_client_config_key\", \"view-oauth_client_config_key\", \"edit-client-keys-oauth_client_config_key\", \"update-client-keys-oauth_client_config_key\", \"create-whitelabel_trading_platform\", \"edit-whitelabel_trading_platform\", \"delete-whitelabel_trading_platform\", \"view-whitelabel_trading_platform\", \"create-exchange_rate\", \"edit-exchange_rate\", \"delete-exchange_rate\", \"view-exchange_rate\", \"view-migrate_mt_group\", \"view-migrate_app_available_group\", \"view-migrate_app_cellxpert\", \"view-migrate_application\", \"view-migrate_id_application\", \"view-migrate_sales_application\", \"view-migrate_disabled_id_application\", \"view-migrate_hide_id_mt_accounts\", \"view-migrate_delete_id_application\", \"create-flag_per\", \"edit-flag_per\", \"delete-flag_per\", \"view-flag_per\", \"create-university\", \"edit-university\", \"delete-university\", \"view-university\", \"create-university_lead\", \"edit-university_lead\", \"delete-university_lead\", \"view-university_lead\", \"export-university_lead\", \"create-crypto_internal_transaction\", \"edit-crypto_internal_transaction\", \"delete-crypto_internal_transaction\", \"view-crypto_internal_transaction\", \"create-glossary_term\", \"edit-glossary_term\", \"delete-glossary_term\", \"view-glossary_term\", \"create-promotion\", \"edit-promotion\", \"delete-promotion\", \"view-promotion\", \"enable-for-app-promotion\", \"create-user_restriction\", \"edit-user_restriction\", \"delete-user_restriction\", \"view-user_restriction\", \"view-risk_activity\", \"view-manaf_bank\", \"create-manaf_bank\", \"edit-manaf_bank\", \"delete-manaf_bank\", \"view-manaf_bank_account\", \"create-manaf_bank_account\", \"edit-manaf_bank_account\", \"delete-manaf_bank_account\", \"create-exchanger\", \"edit-exchanger\", \"delete-exchanger\", \"view-exchanger\", \"create-check_list\", \"edit-check_list\", \"delete-check_list\", \"view-check_list\", \"create-mail_notifier\", \"edit-mail_notifier\", \"delete-mail_notifier\", \"view-mail_notifier\", \"send-email-mail_notifier\", \"create-dynamic_email_templates\", \"edit-dynamic_email_templates\", \"delete-dynamic_email_templates\", \"view-dynamic_email_templates\", \"create-dynamic_email_contents\", \"edit-dynamic_email_contents\", \"delete-dynamic_email_contents\", \"view-dynamic_email_contents\", \"mail-key-tags-dynamic_email_contents\", \"send-test-email-dynamic_email_contents\", \"preview-dynamic_email_contents\", \"create-dynamic_email_tags\", \"edit-dynamic_email_tags\", \"delete-dynamic_email_tags\", \"view-dynamic_email_tags\", \"create-account_form_version\", \"edit-account_form_version\", \"delete-account_form_version\", \"view-account_form_version\", \"view-user_request\", \"internal-transfer-user_request\", \"create-university_college\", \"edit-university_college\", \"delete-university_college\", \"view-university_college\", \"create-complaint\", \"edit-complaint\", \"delete-complaint\", \"view-complaint\", \"export-complaint\", \"create-manage_complaint\", \"edit-manage_complaint\", \"delete-manage_complaint\", \"view-manage_complaint\", \"create-asic_answer_advanced_logics\", \"edit-asic_answer_advanced_logics\", \"delete-asic_answer_advanced_logics\", \"view-asic_answer_advanced_logics\", \"create-internal_notifications\", \"edit-internal_notifications\", \"delete-internal_notifications\", \"view-internal_notifications\"]\"\n      \"settings\" => \"[\"has_authorized_regions\", \"has_assigned_view_ams\", \"has_managed_roles\"]\"\n      \"website_ids\" => array:1 [\n        0 => \"all\"\n      ]\n      \"created_at\" => \"2019-02-12T12:54:33.000000Z\"\n      \"updated_at\" => \"2025-05-12T11:29:37.000000Z\"\n      \"deleted_at\" => null\n      \"authorized_regions\" => \"[\"all\"]\"\n      \"authorized_roles\" => \"[\"all\"]\"\n      \"authorized_ams\" => []\n      \"registrable\" => 0\n      \"managed_roles\" => array:1 [\n        0 => \"all\"\n      ]\n    ]\n    \"application\" => array:16 [\n      \"id\" => 1\n      \"app_type_id\" => 1\n      \"approved\" => \"2\"\n      \"completed\" => \"2\"\n      \"profile_strength\" => \"{\"profile_information\": {\"1\": \"1\"}, \"term_and_conditions\": \"0\"}\"\n      \"affiliate_website_id\" => 3\n      \"referral_id\" => 10\n      \"referral_value_id\" => \"direct\"\n      \"referral_hash\" => null\n      \"created_at\" => \"2019-02-12T12:54:33.000000Z\"\n      \"updated_at\" => \"2023-02-26T10:54:37.000000Z\"\n      \"deleted_at\" => null\n      \"profile_strength_percentage\" => 50.0\n      \"affiliate_website\" => array:10 [\n        \"id\" => 3\n        \"name\" => \"portal.ingotbrokers.local\"\n        \"crm_name\" => \"crm.ingotbrokers.com\"\n        \"site_name\" => \"www.ingotbrokers.com\"\n        \"aliases\" => array:1 [\n          0 => \"portal-fa.ingotbrokers.com\"\n        ]\n        \"iso\" => \"global\"\n        \"regulation_entity_id\" => 2\n        \"created_at\" => \"2019-05-30T10:02:04.000000Z\"\n        \"updated_at\" => \"2023-08-13T16:26:00.000000Z\"\n        \"deleted_at\" => null\n      ]\n      \"app_type\" => array:8 [\n        \"id\" => 1\n        \"name\" => \"admin\"\n        \"display_name\" => \"Admin\"\n        \"description\" => null\n        \"websites\" => \"[]\"\n        \"created_at\" => \"2019-02-12T12:54:32.000000Z\"\n        \"updated_at\" => \"2019-10-10T09:49:20.000000Z\"\n        \"deleted_at\" => null\n      ]\n      \"main_app_wallet\" => array:7 [\n        \"id\" => 55534\n        \"balance\" => \"0\"\n        \"application_id\" => 1\n        \"currency_id\" => 1\n        \"created_at\" => \"2022-11-20T08:13:01.000000Z\"\n        \"updated_at\" => \"2022-11-20T08:13:01.000000Z\"\n        \"deleted_at\" => null\n      ]\n    ]\n    \"two_factor_devices\" => []\n  ]\n]"}, "names": "web: <EMAIL>"}, "gate": {"count": 230, "messages": [{"message": "[\n  ability => view-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1055175273 data-indent-pad=\"  \"><span class=sf-dump-note>view-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055175273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137614, "xdebug_link": null}, {"message": "[\n  ability => view-permission,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1267577681 data-indent-pad=\"  \"><span class=sf-dump-note>view-permission </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view-permission</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267577681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.307445, "xdebug_link": null}, {"message": "[\n  ability => export-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-955564112 data-indent-pad=\"  \"><span class=sf-dump-note>export-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">export-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955564112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.308115, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-183693758 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183693758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.30867, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-997008552 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-997008552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311118, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1391538603 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391538603\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311697, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1357283220 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357283220\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31233, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1902869009 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902869009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.312858, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944252590 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944252590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31359, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2057125196 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057125196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.314135, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1760257506 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760257506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.314742, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-171150650 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-171150650\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31526, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1733709808 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733709808\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316031, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-227032863 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227032863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316579, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1724922697 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724922697\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317189, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-460691701 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460691701\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317729, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-433270234 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433270234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.318466, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1734305250 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734305250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.319008, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1589175106 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1589175106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.319621, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-483780873 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483780873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.320146, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-553731325 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553731325\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.320889, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1356607594 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356607594\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.321428, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-209196447 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209196447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.322146, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2013748247 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013748247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.322696, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-829057902 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829057902\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.323436, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-256664403 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256664403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.323966, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-83986838 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83986838\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.324572, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1238289955 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238289955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.325086, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1747066690 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747066690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.32584, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2141761219 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141761219\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.32637, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-342133577 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342133577\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.326972, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-19486831 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19486831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.327487, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-533230478 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533230478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328201, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1974269978 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974269978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328729, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-877186925 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877186925\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.329334, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2100692134 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100692134\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.32985, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-576681236 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576681236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.330603, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1124387834 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124387834\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.33113, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1086720001 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086720001\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.331735, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1274537887 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274537887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.332252, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1408721269 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408721269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.333005, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-491388213 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491388213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.333561, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1906094633 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906094633\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.334823, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1623680065 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623680065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.335799, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1107201506 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107201506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.339616, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1613723718 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613723718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.3403, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-873566173 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873566173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.341082, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1778417796 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778417796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.341632, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-581546111 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581546111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.342419, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1225159025 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225159025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.342956, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-828960250 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828960250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.343565, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1544179450 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544179450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.344127, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1887289937 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887289937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.344856, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1819902054 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819902054\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.345385, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1602783567 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602783567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.345993, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-440851328 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440851328\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.346505, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-821648793 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821648793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.34723, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-970366282 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-970366282\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.34776, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-221821390 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221821390\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.348364, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-381504614 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381504614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.348881, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1987551039 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987551039\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.349602, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-139209279 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139209279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.35013, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1614535686 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1614535686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351051, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1169941546 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169941546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.35175, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-664469529 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664469529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.352588, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1075247526 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075247526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353161, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1861232787 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861232787\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353825, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-284118908 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284118908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.354418, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1386574548 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386574548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355154, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-666925566 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666925566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355691, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-77935083 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77935083\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356305, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1994855536 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994855536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356828, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-105368818 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105368818\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.357559, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-708957635 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708957635\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.358096, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1365626377 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365626377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.358708, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1840356137 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840356137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.359232, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-499161609 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499161609\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.360055, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-199527003 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199527003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.360608, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-741886978 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741886978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.361231, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1588079978 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588079978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.361761, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-500187057 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500187057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.362491, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1847356201 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847356201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.363028, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1484148919 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484148919\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.363642, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1595491407 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595491407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.364166, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-507448954 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507448954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.364895, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1967744049 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967744049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.365429, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-708490410 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708490410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.366036, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-935704651 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935704651\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.366555, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1907235678 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907235678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.367457, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-903177000 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903177000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.368059, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1169244419 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169244419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.368747, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1789270288 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789270288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.369297, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-554868910 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554868910\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.370119, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1203948866 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203948866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.37067, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-386113662 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-386113662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.371284, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1321147526 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321147526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.371803, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1835847668 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835847668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.372535, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-492831690 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492831690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.373074, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-983551131 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983551131\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.373681, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1874585040 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874585040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.3742, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-273322108 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273322108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.374916, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1856664699 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856664699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.37545, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1907578737 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907578737\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.376058, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-256033657 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256033657\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.376575, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1778321213 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778321213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378894, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-89501104 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89501104\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.379435, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1643092609 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643092609\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.380055, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1012330953 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012330953\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.380576, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-676872690 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676872690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.382847, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-720401061 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720401061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.383397, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1474574459 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474574459\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384252, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-800548504 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800548504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384804, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2121751061 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121751061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.387356, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-750210270 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750210270\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.387907, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-914624277 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914624277\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.388537, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1859822038 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859822038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.38907, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1347025604 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347025604\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.391234, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-573127407 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573127407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.39178, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-221272007 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221272007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.392409, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1525507623 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525507623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.392941, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1169653852 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169653852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.393679, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2013432941 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013432941\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.394225, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1645566443 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645566443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.394847, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-711885221 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711885221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.395378, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-961860972 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961860972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.396178, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1264330589 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264330589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.396726, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-386640668 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-386640668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.397338, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-642909004 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642909004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.397859, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1465098333 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465098333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.39864, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-298580031 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298580031\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.399183, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1827762080 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827762080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.399855, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-922447437 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-922447437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.400441, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-432344018 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432344018\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.401295, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1079220412 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079220412\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.401929, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1428135773 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428135773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.402584, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1440261956 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1440261956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.403119, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1373115860 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373115860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.403863, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1922528041 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922528041\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.404402, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1546640102 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546640102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.405268, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1217055166 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217055166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.405813, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-462908866 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462908866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.406555, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-612911815 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612911815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.407099, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-226731945 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226731945\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.40771, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1565633372 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565633372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.40823, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1520755638 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520755638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.409063, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-324067736 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324067736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.409599, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1711176316 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711176316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.410209, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-240503951 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240503951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.410728, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-579857877 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579857877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.411496, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1278294593 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278294593\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.412025, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1568403486 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568403486\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.412631, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-719149383 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719149383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.413147, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-618780885 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618780885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.413863, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648444271 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648444271\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414393, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-562116313 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562116313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414999, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1732555418 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732555418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415512, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1940208560 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940208560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.416225, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1542450791 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542450791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.416757, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-107548385 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107548385\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.417572, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-590313944 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590313944\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418128, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1921054764 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921054764\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418894, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1331325977 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331325977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.419437, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-522792533 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-522792533\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.420137, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1084449572 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084449572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.420811, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1434236265 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434236265\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.421705, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-592218962 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592218962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.422496, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-775176054 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775176054\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.423234, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1394844624 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394844624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.423784, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1057767909 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057767909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424527, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1406045439 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406045439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.425113, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-162935481 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162935481\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.425753, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1520293607 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520293607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.426344, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1504111051 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504111051\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.427095, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-172495461 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172495461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.427627, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-949465955 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949465955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.428239, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1663763713 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663763713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.428756, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-890340536 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890340536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.42948, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1127375925 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1127375925\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.430026, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1578134982 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578134982\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.430651, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1014197665 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014197665\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.431176, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-123104039 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123104039\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.433587, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1724915270 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724915270\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.434273, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-79096679 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79096679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.43491, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-381411587 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381411587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.435434, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-393217620 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393217620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.436175, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1991916372 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991916372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.436709, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1503327132 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503327132\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.437324, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1828186470 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828186470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.437844, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-994530726 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994530726\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.438565, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-678860305 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-678860305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439099, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1002284311 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002284311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439709, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-273684141 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273684141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.44023, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-913983855 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913983855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.441016, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-539544786 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539544786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.441879, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-825741157 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825741157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.442512, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-611000758 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611000758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.443041, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-619519704 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619519704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.443766, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1793749869 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793749869\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.444303, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-701172194 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701172194\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.444915, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-211363958 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211363958\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.445438, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1970016367 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970016367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.44626, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1359450727 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359450727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.446838, "xdebug_link": null}, {"message": "[\n  ability => create-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-353101043 data-indent-pad=\"  \"><span class=sf-dump-note>create-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353101043\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.447461, "xdebug_link": null}, {"message": "[\n  ability => delete-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1859878758 data-indent-pad=\"  \"><span class=sf-dump-note>delete-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859878758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.447997, "xdebug_link": null}, {"message": "[\n  ability => view-calendar-contact_reminder,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1231576718 data-indent-pad=\"  \"><span class=sf-dump-note>view-calendar-contact_reminder </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">view-calendar-contact_reminder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231576718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529805, "xdebug_link": null}, {"message": "[\n  ability => view-my-requests,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view-my-requests </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view-my-requests</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.535935, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.538225, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.539196, "xdebug_link": null}, {"message": "[\n  ability => view-role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view-role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view-role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540437, "xdebug_link": null}, {"message": "[\n  ability => view-error_log,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-254883574 data-indent-pad=\"  \"><span class=sf-dump-note>view-error_log </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-error_log</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254883574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541034, "xdebug_link": null}, {"message": "[\n  ability => view-promotion,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-174507396 data-indent-pad=\"  \"><span class=sf-dump-note>view-promotion </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-promotion</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174507396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541547, "xdebug_link": null}, {"message": "[\n  ability => view-migrate_mt_group,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1677951398 data-indent-pad=\"  \"><span class=sf-dump-note>view-migrate_mt_group </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view-migrate_mt_group</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677951398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542033, "xdebug_link": null}, {"message": "[\n  ability => view-deposit_transaction_internal_systems,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1425327712 data-indent-pad=\"  \"><span class=sf-dump-note>view-deposit_transaction_internal_systems </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"41 characters\">view-deposit_transaction_internal_systems</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425327712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542518, "xdebug_link": null}, {"message": "[\n  ability => view-transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2072931058 data-indent-pad=\"  \"><span class=sf-dump-note>view-transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view-transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072931058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543, "xdebug_link": null}, {"message": "[\n  ability => view-manage_transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1907612137 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907612137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543481, "xdebug_link": null}, {"message": "[\n  ability => view-manage_application,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-420425144 data-indent-pad=\"  \"><span class=sf-dump-note>view-manage_application </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view-manage_application</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420425144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543965, "xdebug_link": null}, {"message": "[\n  ability => view-campaign-lead,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1480792627 data-indent-pad=\"  \"><span class=sf-dump-note>view-campaign-lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-campaign-lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480792627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544447, "xdebug_link": null}, {"message": "[\n  ability => view-contact,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-347374367 data-indent-pad=\"  \"><span class=sf-dump-note>view-contact </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view-contact</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347374367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54493, "xdebug_link": null}, {"message": "[\n  ability => view-deposits_and_withdrawals_summary,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-796618403 data-indent-pad=\"  \"><span class=sf-dump-note>view-deposits_and_withdrawals_summary </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"37 characters\">view-deposits_and_withdrawals_summary</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-796618403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545421, "xdebug_link": null}, {"message": "[\n  ability => view-wallet_report,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view-wallet_report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-wallet_report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545903, "xdebug_link": null}, {"message": "[\n  ability => access-account-manager-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-account-manager-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">access-account-manager-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.550228, "xdebug_link": null}, {"message": "[\n  ability => access-affiliate-links,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-affiliate-links </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">access-affiliate-links</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.553754, "xdebug_link": null}, {"message": "[\n  ability => transactions-history-whitelabel_transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>transactions-history-whitelabel_transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"43 characters\">transactions-history-whitelabel_transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.554284, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.557166, "xdebug_link": null}, {"message": "[\n  ability => access-security-preferences,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-security-preferences </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">access-security-preferences</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.558177, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.560428, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.576079, "xdebug_link": null}, {"message": "[\n  ability => access-user-account,\n  target => null,\n  result => false,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>access-user-account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">access-user-account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.577093, "xdebug_link": null}, {"message": "[\n  ability => create-watch_list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>create-watch_list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create-watch_list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.577611, "xdebug_link": null}, {"message": "[\n  ability => create-missing-transaction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1861115689 data-indent-pad=\"  \"><span class=sf-dump-note>create-missing-transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">create-missing-transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861115689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.579618, "xdebug_link": null}]}, "session": {"_token": "Hkq9N6gyL5k71pOWnQhpufOpBAB19Ap0LOEPkYo4", "locale": "en", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://portal.ingotbrokers.local/media-assets?f=storage%2Fuploads%2F%242y%2410%24yA6oWVSfXJuIUlyCQWtZ.K.FEBOtSeIE2a.gTo.2njzELlbaIRwC.png\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$LeRJ3FTYSs.N/cnxap60jus42Cwf/wNPWVAD4QmZvzn/nDi9zzbLm", "password_expired": "false"}, "request": {"data": {"status": "200 OK", "full_url": "http://portal.ingotbrokers.local/en/roles", "action_name": "roles.index", "controller_action": "App\\Http\\Controllers\\Admin\\RoleController@index", "uri": "GET en/roles", "controller": "App\\Http\\Controllers\\Admin\\RoleController@index<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FRoleController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/en/roles", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2Fingotbrokers%2Fapp%2FHttp%2FControllers%2FAdmin%2FRoleController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/RoleController.php:27-64</a>", "middleware": "web, auth, password.expiry, localeSessionRedirect, localizationRedirect, localeViewPath, can:view-role, Ingotbrokers", "telescope": "<a href=\"http://portal.ingotbrokers.local/_debugbar/telescope/9f02a3fd-a091-4711-9df2-1906054ff473\" target=\"_blank\">View in Telescope</a>", "duration": "833ms", "peak_memory": "18MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1216945538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1216945538\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1044504731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1044504731\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1396233095 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">portal.ingotbrokers.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://portal.ingotbrokers.local/en/manage-applications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">en-AU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">ingot_brokers_session=V6oHfZ16YWAe8vSxjTTDNfF7bFJ9O0ygT6BXeqtx; XSRF-TOKEN=eyJpdiI6ImFZN1V3VXJXZGhPQlZLN2VEa2p1d2c9PSIsInZhbHVlIjoiMk5nVmxKbDdjZ2tJWWxPWTZDRG5rdmxnVkpuY3g2WXRZVWdaWC9nWHE0NWp5N0t3N2hPWkUxc3VKQXIzU2lJSjF2Tjc0TXF6M0cwMWZkRWV0QTYzdmxBQ2xsQStLMVBPai93akhwZUt0dzgvOEd3bWl6UXkvdGhmN3h4UHJxc1EiLCJtYWMiOiI4NTRhMDYyMzA3YzRmNmViYzk2MjUzN2E4YzZiNTJmZjkxM2VjNTY0MWE0YTZmZWE1Zjg5MjAwYzVhMzM0ZjUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396233095\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1299634971 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ingot_brokers_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hkq9N6gyL5k71pOWnQhpufOpBAB19Ap0LOEPkYo4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299634971\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-379392099 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 09:08:20 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379392099\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1599215950 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hkq9N6gyL5k71pOWnQhpufOpBAB19Ap0LOEPkYo4</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"137 characters\">http://portal.ingotbrokers.local/media-assets?f=storage%2Fuploads%2F%242y%2410%24yA6oWVSfXJuIUlyCQWtZ.K.FEBOtSeIE2a.gTo.2njzELlbaIRwC.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$LeRJ3FTYSs.N/cnxap60jus42Cwf/wNPWVAD4QmZvzn/nDi9zzbLm</span>\"\n  \"<span class=sf-dump-key>password_expired</span>\" => <span class=sf-dump-const>false</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599215950\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://portal.ingotbrokers.local/en/roles", "action_name": "roles.index", "controller_action": "App\\Http\\Controllers\\Admin\\RoleController@index"}, "badge": null}}