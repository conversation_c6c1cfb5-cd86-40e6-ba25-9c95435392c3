<?php

namespace App\Exports;

use App\Http\Controllers\Admin\AmlReportController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AmlReportExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(AmlReportController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Login',
            'Client Name',
            'Entity',
            'Application ID',
            'Country Of Residence',
            'Account Type',
            'Nationality',
            'AML Flag',
            'Referral',
            'Age',
            'Active',
            'Created At',
            'Dataset',
            'Score',
            'Country',
        ];
    }

    public function map($row): array
    {
        $data[] = [
            $row->logins,
            $row->mainUser->full_name,
            $row->getRegulatedEntity(),
            $row->appIdentifierId(),
            $row->mainUser?->country->name,
            $row->appType?->name,
            $row->mainUser?->userCitizenship->nationality ?? '-',
            $row->mainUser?->amlResult != null ? 'Yes' : 'No',
            $row->referral_id != null ? 'Yes' : 'No',
            $row->mainUser?->age ?? '-',
            $row->isActive == 'true' ? 'Yes' : 'No',
            $row->created_at
        ];

        foreach ($row->mainUser?->amlResult?->result ?? [] as $result) {
            $data[] = [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                $result['dataset'],
                $result['score'],
                implode(', ', (array) $result['countries']),
            ];
        }

        $data[] = [];

        return $data;
    }
}
