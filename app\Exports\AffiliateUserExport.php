<?php

namespace App\Exports;

use App\Http\Controllers\Admin\IngoteersClubController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AffiliateUserExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(IngoteersClubController::class)->affiliateUsers(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Email',
            'Phone',
            'Country',
            'Application Type',
            'Status',
            'Registered Data',
        ];
    }

    public function map($row): array
    {
        $mainUser = $row->mainUser;

        return [
            $mainUser->full_name ?? '-',
            $mainUser->email ?? '-',
            $mainUser->phone ?? '-',
            $mainUser->country?->name ?? '-',
            $row->appType->display_name ?? '-',
            $row->getStatus() ?? '-',
            $row->created_at ?? '-',
        ];
    }
}
