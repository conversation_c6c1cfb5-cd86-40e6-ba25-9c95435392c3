<?php

namespace App\Exports;

use App\Http\Controllers\Admin\RoleController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class RolesExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(RoleController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'Name',
            'Display Name',
            'Notification Email',
            'Count All Users',
            'Count Active Users',
            'Restrected IPs',
            'Permissions',
            'Created Date',
            'Updated On',
        ];
    }

    public function map($row): array
    {
        $permissions = implode(', ', json_decode($row->permissions ?? [], true) ?? []);

        return [
            $row->name,
            $row->display_name,
            $row->department_email ?? '-',
            $row->users_count .' users' ?? '-',
            $row->active_users_count .' users' ?? '-',
            implode(', ', (array) $row->ip_addresses) ?? '-',
            $permissions ?? '-',
            $row->created_at ?? '-',
            $row->updated_at ?? '-'
        ];
    }
}
