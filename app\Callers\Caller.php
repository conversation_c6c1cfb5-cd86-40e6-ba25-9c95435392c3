<?php

namespace App\Callers;

abstract class Caller
{
    protected $instance;

    public function __construct($instance)
    {
        $this->instance = $instance;
    }

    public function beforeCallMethod($method, $params)
    {
    }

    public function afterCallMethod($method, $params)
    {
    }

    public function __call($method, $params)
    {
        $this->beforeCallMethod($method, $params);
        $result = $this->instance->$method(...$params);
        $this->afterCallMethod($method, $params);

        return $result;
    }
}
