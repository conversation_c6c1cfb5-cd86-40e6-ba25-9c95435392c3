<?php

namespace App\Exports;

use App\Http\Controllers\Admin\CryptoInternalTransactionController;
use Illuminate\Bus\Queueable;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class CryptoInternalTransactionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;
    use Queueable;

    private $user;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->user = auth()->user();
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(CryptoInternalTransactionController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        $titles = [
            'Reference ID',
            'Type',
            'From',
            'To',
            'TxHash',
            'Amount',
            'Comment',
            'Payment Gateway',
            'Created By',
            'Created Date',
            'Updated On',
        ];

        return $titles;
    }

    public function map($row): array
    {
        $data = [
            $row->id,
            $row->type,
            $row->from,
            $row->to,
            $row->tx_hash,
            $row->amount,
            $row->comment,
            $row->payment_gateway_name,
            $row->user_first_name . ' ' . $row->user_last_name ?? '',
            $row->created_at,
            $row->updated_at,
        ];

        return $data;
    }
}
