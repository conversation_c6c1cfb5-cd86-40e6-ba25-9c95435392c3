<?php

namespace App\Exceptions;

use Throwable;
use ArgumentCountError;
use Illuminate\Http\Response;
use Sentry\Laravel\Integration;
use Illuminate\Http\JsonResponse;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use League\OAuth2\Server\Exception\OAuthServerException;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Symfony\Component\Mime\Exception\RfcComplianceException;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Laravel\Passport\Exceptions\AuthenticationException as PassportAuthenticationException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        // add RFC compliance exception to the list of exceptions that are not reported
        RfcComplianceException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    public function register()
    {
        $this->reportable(function (Throwable $e) {
            Integration::captureUnhandledException($e);
        });
    }

    /**
     * Report or log an exception.
     *
     * @param Throwable $exception
     * @return void
     *
     * @throws Throwable
     */
    public function report(Throwable $exception)
    {
        if ($exception instanceof ArgumentCountError) {
            abort(404);
        }

        // Kill reporting if this is an "access denied" (code 9) OAuthServerException.
        if ($exception instanceof OAuthServerException && $exception->getCode() == 9) {
            return;
        }

        parent::report($exception);
    }

    public function sendError(mixed $data = [], string|null $message = null, int|string|null $code = null): JsonResponse
    {
        $customCode = null;

        if (is_null($code)) {
            $code = Response::HTTP_BAD_REQUEST;
        }

        if (!array_key_exists($code, Response::$statusTexts)) {
            $customCode = $code;
        }

        return response()->json([
            'code' => !is_null($customCode) ? $customCode : $code,
            'message' => $message,
            'details' => $data,
        ], !is_null($customCode) ? ResponseAlias::HTTP_BAD_REQUEST : $code);
    }

    public function render($request, Throwable $e)
    {
        if (!$request->wantsJson()) {
            return parent::render($request, $e);
        }

        return match (true) {
            $e instanceof ModelNotFoundException, $e instanceof RouteNotFoundException => $this->sendError(
                code: Response::HTTP_NOT_FOUND
            ),
            $e instanceof NotFoundHttpException, $e instanceof MethodNotAllowedHttpException => $this->sendError(
                message: 'Not Found',
                code: Response::HTTP_NOT_FOUND
            ),
            $e instanceof AuthenticationException, => $this->sendError(
                code: Response::HTTP_FORBIDDEN
            ),
            $e instanceof PassportAuthenticationException, => $this->sendError(
                message: 'Invalid client credentials',
                code: Response::HTTP_FORBIDDEN
            ),
            $e instanceof AuthorizationException => $this->sendError(
                code: Response::HTTP_UNAUTHORIZED
            ),
            $e instanceof ValidationException => $this->sendError(
                data: $e->errors(),
                message: 'Validation failed.',
                code: Response::HTTP_UNPROCESSABLE_ENTITY
            ),
            $e instanceof ThrottleRequestsException => $this->sendError(
                message: __('auth.throttle', ['seconds' => 60]),
                code: Response::HTTP_TOO_MANY_REQUESTS
            ),
            $e instanceof RequestException => $this->sendError(
                message: $e->getMessage(),
                code: $e->getCode()
            ),
            default => $this->sendError(
                message: $e->getMessage() . ' |' . $e->getFile() . ':' . $e->getLine(),
                code: Response::HTTP_INTERNAL_SERVER_ERROR
            )
        };
    }
}
