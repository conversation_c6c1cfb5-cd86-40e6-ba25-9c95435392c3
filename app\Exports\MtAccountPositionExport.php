<?php

namespace App\Exports;

use App\Enums\MetaTrader4Enum;
use App\Enums\MetaTrader5Enum;
use App\Http\Controllers\Admin\MtAccountManagementController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MtAccountPositionExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private string $platform;

    private array $actions;

    private array $reasons;

    public function __construct(protected string $userId, protected array $requestData, protected array $actionParameters)
    {
        $this->platform = $this->mtPlatform();

        if ($this->platform == 'mt5') {
            $this->reasons = MetaTrader5Enum::REASON;
            $this->actions = MetaTrader5Enum::DEAL_ACTION;
        } else {
            $this->reasons = MetaTrader4Enum::REASON;
            $this->actions = MetaTrader4Enum::DEAL_ACTION;
        }
    }

    public function collection()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->accountPositions(
            request: request()->merge($this->requestData),
            login_id: $this->actionParameters['login_id'],
            export: true
        )['data'];
    }

    public function mtPlatform()
    {
        auth()->loginUsingId($this->userId);

        return app(MtAccountManagementController::class)->accountPositions(
            request: request()->merge($this->requestData),
            login_id: $this->actionParameters['login_id'],
            export: true
        )['platform'];
    }

    public function headings(): array
    {
        return [
            'Ticket',
            'Symbol',
            'Action',
            'Volume',
            'Reason',
            'Swap',
            'Open Price',
            'Open Time',
            'Profit',
        ];
    }

    public function map($row): array
    {
        return [
            $row->ticket,
            $row->symbol,
            $this->actions[$row->action] ?? '-',
            number_format($row->volume, 2, '.', ''),
            $this->reasons[$row->reason] ?? '-',
            number_format($row->swaps, 2, '.', ''),
            number_format($row->open_price, 2, '.', ''),
            $row->time_created,
            number_format($row->profit, 2, '.', ''),
        ];
    }
}
