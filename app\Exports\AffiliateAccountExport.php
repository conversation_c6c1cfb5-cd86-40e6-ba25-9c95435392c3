<?php

namespace App\Exports;

use App\Http\Controllers\Admin\IngoteersClubController;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class AffiliateAccountExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected string $userId, protected array $requestData)
    {
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(IngoteersClubController::class)->affiliateAccounts(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'App. ID',
            'Login',
            'Name',
            'Agent Profile Name',
            'Application Type',
            'Platform',
            'Group',
            'Currency',
            'Created Date',
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId(),
            $row->login,
            $row->application->mainUser?->full_name ?? '-',
            $row->mtGroup->mtAccount->application->mainUser->full_name ?? '-',
            $row->mtGroup->account_type ?? '-',
            $row->mtGroup->tradingPlatform->name ?? '-',
            $row->mtGroup->name ?? '-',
            $row->mtGroup->currency->code ?? '-',
            $row->created_at,
        ];
    }
}
