<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class MakeRepositoryCommand extends Command
{
    public $class;

    public $namespace;

    public $repositoryDirectoryPath;

    public $interfaceDirectoryPath;

    public $modelClass;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:repo {class}';

    /**
     * @var bool
     */
    protected $hidden = true;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Make repository command';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->class = $this->argument('class');
        $this->interfaceDirectoryPath = 'App/Repositories/Interfaces';
        $this->repositoryDirectoryPath = 'App/Repositories';

        try {
            $this->modelClass = resolve("App\Models\\{$this->class}");
        } catch (Exception $exception) {
            $this->newLine();
            $this->warn("The class model for ({$this->class}) not exists ! ");
            $this->newLine();
        }

        try {
            $this->createRepository();
            $this->createInterface();

            $this->info('Repository created successfully !');
        } catch (Exception $exception) {
            $this->error('There is error when create repository !');
        }
    }

    public function createInterface(): void
    {
        $data = $this->getStubContents(base_path('stubs/repository-interface.stub'), 'RepositoryInterface');

        $filePath = $this->interfaceDirectoryPath.'/'.$this->class.'RepositoryInterface.php';

        if (!File::put($filePath, $data)) {
            $this->error('There is error when create interface !');
        }
    }

    private function createRepository(): void
    {
        $data = $this->getStubContents(base_path('stubs/repository.stub'), 'Repository');

        $filePath = $this->repositoryDirectoryPath.'/'.$this->class.'Repository.php';

        if (!File::put($filePath, $data)) {
            $this->error('There is error when create repository !');
        }
    }

    private function getStubContents($stub, $className): array|bool|string
    {
        $interfaceName = ucwords($this->class.$className).'Interface';

        $variables = [
            'class' => ucwords($this->class.$className),
            'namespace' => str_replace('/', '\\', $className === 'Repository' ? $this->repositoryDirectoryPath : $this->interfaceDirectoryPath),
            'interface' => $interfaceName,
            'modelClass' => "{$this->class}::class",
            'modelClassImport' => $this->modelClass ? $this->modelClass::class : null,
            'interfaceImport' => str_replace('/', '\\', $this->interfaceDirectoryPath).'\\'.$interfaceName,
        ];

        $contents = file_get_contents($stub);

        foreach ($variables as $search => $replace) {
            if (is_array($replace)) {
                $contents = strtr($contents, [
                    '{{'.$search.'}}' => var_export($replace, true),
                ]);
            } else {
                $contents = str_replace('{{ '.$search.' }}', $replace, $contents);
            }
        }

        return $contents;
    }
}
