<?php

namespace App\Helpers\ApiCall\Src\CallersTypes;

use App\Helpers\ApiCall\Src\ApiCaller;
use App\Helpers\ApiCall\Src\Response\Response;

class POST extends Caller
{
    protected $body;

    public function call(): Response
    {
        $request = ApiCaller::getInstance()->getRequest();
        $res = $request->withHeaders($this->headers)->post($this->url, $this->body ?? []);

        return new Response($res);
    }

    /**
     * @param  mixed  $body
     */
    public function setBody($body): static
    {
        $this->body = $body;

        return $this;
    }
}
