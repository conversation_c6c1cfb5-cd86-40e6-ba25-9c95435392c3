<?php

namespace App\Exports;

use App\Http\Controllers\Admin\OrderManagementController;
use App\Models\Order;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PendingOrderExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private array $statuses;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->statuses = Order::$statuses;
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(OrderManagementController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'ID Application',
            'Name',
            'Order',
            'Account Manager',
            'Status',
            'Entity',
            'Created Date',
            'Updated On',
            'Comment',
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId(),
            $row->user->full_name,
            $row->order_type_name,
            $row->application->getAppAccountManagerName() ?? __('content.not_assigned'),
            $this->statuses[$row->status],
            $row->application->getRegulatedEntity() ?? '-',
            $row->created_at,
            $row->updated_at,
            $row->comment,
        ];
    }
}
