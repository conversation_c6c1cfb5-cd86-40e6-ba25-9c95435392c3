<?php

namespace App\Exports;

use App\Jobs\MailingJob;
use App\Mail\ExportAttachment;

class MailingExport
{
    public static function attach(CSV $csv, $email = null)
    {
        if (!$email) {
            if (auth()->user()) {
                $email = auth()->user()?->email;
            }
        }

        if ($email) {
            dispatch(new MailingJob($email, new ExportAttachment($csv), null));
            Request()->session()->flash('info', sprintf(trans('content.export_send_by_mail'), $email));
        } else {
            Request()->session()->flash('info', trans('content.request_failed'));
        }
    }
}
