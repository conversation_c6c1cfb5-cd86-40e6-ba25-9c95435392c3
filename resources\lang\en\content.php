<?php

return [
    'add_flag_type' => 'Add Flag',
    'flag_type' => 'Flag Type',
    'otp_request' => 'Your OTP request has been sent',
    'card_expiry_date' => 'Card Expiry Date',
    'otp' => 'OTP',
    'request_otp' => 'Request an OTP',
    'example' => 'Example',
    'invalid_phone_number' => 'This phone number is invalid',
    'company_country' => 'Company Country',
    'memorandum_of_association' => 'Memorandum of association',
    'articles_of_association' => 'Articles of association',
    'certificate_of_good_standing' => 'Certificate of good standing',
    'board_resolution' => 'Board resolution',
    'list_of_directors' => 'List of directors',
    'proof_of_listing' => 'Proof of listing',
    'body_content' => 'Body Content',
    'footer_content' => 'Footer Content',
    'show_in_account_forms' => 'Show in account forms?',
    'signal_center_title' => 'Signal Center - Technical Analysis',
    'signal_center_desc' => 'Prepared by leading industry experts specifically for INGOT Brokers clients, this daily market report includes a comprehensive analysis of the most popular instruments worldwide, consequently presenting traders with a general outlook over their potential next trade idea.',
    'cheque_receiving_present' => 'It is necessary for you to provide us with your National Identification (ID) card or valid passport in person.',
    'cheque_receiving_notes' => 'Receipt of the check requires your presence at the INGOT Brokers headquarters (HQ) located in Um Uthainah, 5 Saeed Abu Jaber Street, Amman, Jordan, during our official business hours (Sunday to Thursday from 11:00 a.m. to 2:00 p.m.) after the request approval.',
    'action_chq_only' => 'Sorry, this action is only available for Jordan Cheque',
    'action_jordanian_only' => 'Sorry, this service is only available for Jordanian citizens.',
    'cannot_complete_process_invalid_currency' => 'Sorry , invalid currency code.',
    'refer_and_earn' => 'Refer and earn',
    'refer_and_earn_desc' => 'INGOT will provide you with your own private registration link in order to invite your network. Refer your network to INGOT Brokers and instantly earn points.',
    'refer_and_earn_desc_2' => 'INGOT Brokers highly values the relationships with our Partners and their success is important to us',
    'refer_and_earn_desc_3' => 'Find out how you can <a href=":PARTNERSHIPS_URL" class="dashed-border">become a Partner</a> and start earning today',
    'funds_management' => 'Fund Management',
    'live_account' => 'Trading Account',
    'webhooks' => 'Webhooks',
    'webhook_name' => 'Webhook Name',
    'response' => 'Response',
    'webhook_url' => 'Webhook Url',
    'financial_information' => 'Financial Information',
    'date' => 'Date',
    'dashboard' => 'Dashboard',
    'login' => 'Login',
    'sign_up' => 'Sign Up',
    'continue_with_facebook' => 'Continue with Facebook',
    'continue_with_google' => 'Continue with Google',
    'continue_with_linkedin' => 'Continue with linkedin',
    'reset_password' => 'Reset Password',
    'remembered_password' => 'Remembered your password?',
    'reset' => 'Reset',
    'logout' => 'Logout',
    'search' => 'Search',
    'currency' => 'Currency',
    'account_type' => 'Account Type',
    'account_types' => 'Account Types',
    'no_data_available' => 'There is no data available!',
    'individual' => 'Individual',
    'corporate' => 'Corporate',
    'gender' => 'Gender',
    'birthdate' => 'Date of Birth',
    'citizenship' => 'Citizenship',
    'city' => 'City',
    'address' => 'Address',
    'order' => 'Order',
    'usd' => 'USD',
    'amount' => 'Amount',
    'receipt' => 'Receipt',
    'upload' => 'Upload',
    'transfer' => 'Transfer',
    'note' => 'Note',
    'failed' => 'Failed!',
    'browser' => 'Browser',
    'offer' => 'Offer',
    'request' => 'Request',
    'new_password' => 'New Password',
    'new_password_rule' => 'Password should contains at least 8 characters: digits, lowercase characters and special characters.',
    'confirm_password' => 'Confirm Password',
    'from' => 'From',
    'to' => 'To',
    'transaction' => 'Transaction',
    '419_session_expired' => 'Session has expired',
    '413_request_entity_too_larg' => 'Request Entity Too Large',
    '404_not_found' => 'OOPS! PAGE NOT FOUND',
    '403_forbidden' => 'Access Denied',
    '401_unauthorized' => 'Unauthorized Page',
    '503_unauthorized' => 'Under Construction',
    'forgot_your_password' => 'Forgot Your Password?',
    'long' => 'Long',
    'short' => 'Short',
    'subject' => 'Subject',
    'hello' => 'Hello',
    'profile_picture' => 'Profile Picture',
    'distribution_date' => 'Distribution Date',
    'instruments' => 'Instruments',
    'ingot_brokers' => 'INGOT Brokers',
    'new' => 'New',
    'open' => 'Open',
    'demo_account' => 'Demo account',
    'subscribe' => 'Subscribe',
    'complete_your' => 'Complete your',
    'complete_the' => 'Complete the',
    'complete_account_details_message_1' => 'In order to complete your application and withdraw your commissions, please <a href=":LINK">complete your profile</a>',
    'complete_ib_account_message_2' => 'Your application is being reviewed.',
    'complete_ib_account_message_3' => 'You will be provided with your offer shortly, please fill out, sign, and upload your offer.',
    'uplaod_the' => 'Upload the',
    'ib_offer' => 'Offer',
    'bonus' => 'Bonus',
    'offer_under_review_message' => 'Your offer is being reviewed.',
    'affliate_program' => 'Refer and Earn',
    'ask' => 'Ask',
    'to_complete_their' => 'to complete their',
    'client_eligibility' => 'CFD Knowledge Test',
    'question' => 'Question',
    'payment_gateway' => 'Payment Method',
    'account_number' => 'Account Number',
    'bank_name' => 'Bank Name',
    'beneficiary_name' => 'Beneficiary Name',
    'payment_gateway_details' => 'Payment Methods Details',
    'payment' => 'Payment',
    'banking_details' => 'Payment Banking Details',
    'submit' => 'Submit',
    'copy_right_signature' => ':date :COMPANY_NAME All Rights Reserved.',
    'please_mention' => 'Please mention',
    'user_profile' => 'User Profile',
    'real_account' => 'Trading Account',
    'company_holders' => 'Shareholders and directors',
    'holder' => 'Holder',
    'owner' => 'Owner',
    'percentage_shareholding' => 'Shareholding Percentage',
    'ABN_ACN_Company_No' => 'ABN / ACN / Company No.',
    'registered_address' => 'Registered Address',
    'business_phone' => 'Business phone',
    'login_log' => 'Logins log',
    'uploaded_documents' => 'Uploaded Documents',
    'corporate_events' => 'Corporate Events',
    'corporate_events_report' => 'Corporate Events Report',
    'market' => 'Market',
    'beneficiary_bank' => 'Beneficiary Bank',
    'bank_address' => 'Bank Address',
    'trading_account_bsb' => 'Trading Account BSB',
    'beneficiary_account_number' => 'Beneficiary Account Number',
    'upload_receipt_bank_deposit' => 'Please upload the confirmation of your payment after you deposit via Wire Transfer',
    'please_make_sure_your' => 'Please fill your',
    'iban' => 'IBAN',
    'bsb_code' => 'BSB Code',
    'deposit_information' => 'Deposit Information',
    'province' => 'State',
    'zip_code' => 'Zip code',
    'payment_options' => 'Payment options',
    'ewallet_balance' => 'E-wallet balance',
    'NL_payment_description' => 'NL payment description',
    'click_here_for_registration' => 'click here for registration',
    'online_payment_by_domestic_bank_card' => 'Online payment by domestic bank card',
    'internet_banking' => 'Internet Banking',
    'internet_banking_or_banks_counter' => 'Internet Banking or Banks counter',
    'ATM' => 'ATM',
    'visa_or_mastercard' => 'Visa or MasterCard',
    'visa_or_mastercard_prepaid' => 'Visa or MasterCard prepaid',
    'successfully' => 'Successful',
    'successfully_transferred' => 'The amount has been transferred successfully.',
    'you_have_successfully_made_a_deposit_an_amount' => 'You have successfully made a deposit.',
    'will_be_added_to_your_wallet' => 'Your wallet will fund with the deposited amount.',
    'index_dividends' => 'Index Dividends',
    'search_filter' => 'Search',
    'open_time' => 'Open Time',
    'type' => 'Type',
    'volume' => 'Volume',
    'open_price' => 'Open Price',
    'S/L' => 'S/L',
    'T/P' => 'T/P',
    'close_time' => 'Close Time',
    'close_price' => 'Close Price',
    'swap' => 'Swap',
    'swaps' => 'Swaps',
    'profit' => 'Profit',
    'mt_password_pattern' => 'The password must be composed of a minimum of 8 and a maximum of 15 characters. It should include at least 5 letters, both in uppercase and lowercase, 1 digit, and 1 special character.',
    'account_status_report' => 'Subscribe to the confirmation report',
    'documents' => 'Documents',
    'residence' => 'residence',
    'transfer_fund' => 'Transfer funds',
    'my_wallet' => 'My E-Wallet',
    'fund_transfer_note' => 'Note: INGOT will not be held responsible for any conflict between the two accounts, as far as the authorized person(s) submit this request; it is the clients’ responsibility to organize the funding process.',
    'change_metatrader_password' => 'Change Password',
    'new_pass_letters_limit' => 'The new password should contain more than :count characters with a mix of numbers and letters',
    'wallet_summary' => 'Wallet Summary',
    'withdraw_fund' => 'Withdraw funds',
    'updated_successfully' => 'Changes have been applied successfully!',
    'created_successfully' => 'Added successfully!',
    'no_accounts_available' => 'There are no accounts!',
    'real_account_created_successfully' => '<b class="text-success h3">Almost ready!</b><p>Fund your account now and start trading today.</p><a class="dashed-border" href=":DEPOSIT_URL">Deposit Now</a>',
    'demo_account_created_successfully' => 'The demo account has been created successfully! Please check your email for the login details',
    'cant_create_an_account' => 'MetaTrader account could not be created right now!',
    'current_password_not_correct' => 'The current password is not correct.',
    'no_enough_amount' => 'The balance is less than the required amount',
    'no_limit' => 'No limit',
    'demo_account_details' => 'Demo account details',
    'read_more' => 'Read More',
    'symbol' => 'Symbol',
    'name' => 'Name',
    'account_forms' => 'Legal Forms',
    'home' => 'Home',
    'trading_accounts' => 'Trading Accounts',
    'deposit' => 'Deposit',
    'deposit_tab_th1' => 'Payment Method',
    'deposit_tab_th2' => 'Currency',
    'deposit_tab_th4' => 'Max. Deposit',
    'withdraw' => 'Withdraw',
    'no_fees' => 'No Fees',
    'comment' => 'Comment',
    'resistance' => 'Resistance',
    'support' => 'support',
    'take_profit' => 'Take Profit',
    'stop_loss' => 'Stop Loss',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'email' => 'Email',
    'post_code' => 'Country code',
    'phone_number' => 'Phone Number',
    'password' => 'Password',
    'wallet_currency' => 'Wallet Currency',
    'open_account_checkbox_1.2' => 'Client Agreement',
    'country' => 'Country',
    'company_name' => 'Company Name',
    'company_type' => 'Company Type',
    'country_of_registration' => 'Country Of Registration',
    'main_contact_details' => 'Main Contact Details',
    'platform' => 'Platform',
    'group' => 'Group',
    'balance' => 'Balance',
    'spread' => 'Spread',
    'no_result' => 'no result',
    'open_an_account' => 'Open an Account',
    'no_changes' => 'There is no data changed!',
    'request_was_sent_for_review' => 'Your request has been sent for review.',
    'terms_and_conditions' => 'Terms & Conditions',
    'pds' => 'Product Disclosure Statement',
    'fsg' => 'Financial Services Guide',
    'privacy_policy' => 'Privacy Policy',
    'Language' => 'Language',
    'ingot_popup_modal_message' => 'Are you sure that you wish to continue with this operation?',
    'confirm' => 'Confirm',
    'showing_results_paginator' => 'showing results (:start - :end) / :total',
    'max_real_accounts_reached' => 'You have reached the maximum number of trading accounts (:number).',
    'redirected_soon_wait' => 'You are being redirected, please wait...',
    'Aum' => 'AUM',
    'Margin' => 'Margin',
    'Spread' => 'Spread',
    'Order Level' => 'Order Level',
    'Minimum Fluctuation' => 'Tick Size',
    'Trading Months' => 'Trading Months',
    'Trading Days' => 'Trading Days',
    'Trading Hours' => 'Trading Hours',
    'Execution' => 'Execution',
    'Commission' => 'Commission',
    'Margin Call' => 'Margin Call',
    'Sector' => 'Sector',
    'dear_client' => 'Dear Valued Client,',
    'deposit_tab_th5' => 'Min. Deposit',
    'proof_of_residence' => 'Proof of residence',
    'proof_of_signature' => 'Proof of Signature',
    'exchange_agencies' => 'Exchange agencies',
    'Banks_agencies' => 'Banks agencies',
    'affiliate_links' => 'Referral links',
    '500_error_page' => 'Something went wrong!',
    'cant_transfer_now' => 'We can\'t process your transaction right now, try again later.',
    'failed_eligibility_test' => 'You have failed in our eligibility test, you can try again.',
    'update_leverage_limitation' => 'Please note that you are only allowed to change your leverage ratio once a day, up to five times per month.',
    'currencies' => 'Currencies',
    'service' => 'Service',
    'account_name' => 'Account Name',
    'minimum_deposit' => 'Minimum Deposit',
    'available_products' => 'Available Products',
    'commissions_FX_and_metals' => 'Commissions FX & Metals',
    'minimum_lot_size' => 'Minimum Lot Size',
    'hedging_allowed' => 'Hedging Allowed',
    'negative_balance_protection' => 'Negative Balance Protection',
    'swap_free_islamic_option' => 'SWAP Free Account',
    'stop_out_level' => 'Stop Out Level',
    'mt4' => 'MT4',
    'mt5' => 'MT5',
    'crypto' => 'Crypto',
    'up_to' => 'Up to',
    'optional' => 'Optional',
    'fundamental' => 'Fundamentals',
    'technical' => 'Technical',
    'contact_us' => 'Contact Us',
    'telephone' => 'Tel',
    'fax' => 'Fax',
    'only_english_attribute' => 'The :attribute format is invalid. Only English language is allowed.',
    'comma' => ',',
    'card_number' => 'Card Number',
    'pending_orders' => 'Pending Orders',
    'pending_transactions' => 'Pending Transactions',
    'orders_history' => 'Orders History',
    'transactions_history' => 'Transactions History',
    'mt_sender' => 'MetaTrader Sender',
    'mt_reciver' => 'MetaTrader Receiver',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'disabled' => 'Disabled',
    'faqs' => 'FAQs',
    'my_tickets' => 'Tickets',
    'profile_preferences' => 'Profile Preferences',
    'tickets' => 'User Tickets',
    'pending_tickets' => 'Pending Tickets',
    'tickets_history' => 'Tickets History',
    'attachment' => 'Attachment',
    'description' => 'Description',
    'create_ticket' => 'New Support Ticket',
    'ticket_created_successfully' => 'Your ticket has been created successfully!',
    'comments' => 'Comments',
    'id' => 'ID',
    'ticket_details' => 'Ticket Details',
    'edit_ticket' => 'Edit Ticket',
    'write_comment' => 'Write a comment...',
    'partnership' => 'Partnership',
    'problem_description' => 'Ticket description',
    'case_content' => 'Case content',
    'ticket_description' => 'Describe your issue ..',
    'communication_history' => 'Communication history',
    'is_the_ticket_resolved' => 'Is the ticket resolved?',
    'department' => 'Department',
    'specifications' => 'Account Type Specifications',
    'client_eligibility_failed_message' => 'Unfortunately, you have not been successful, please click <a class="dashed-border" href=":url" target="_blank">here</a> to learn more and try again after 24 hours.',
    'account_rejected_message' => 'Your application is rejected!',
    'add_agent_login_to_ib_message' => 'There are no referral links!',
    'profile_information' => 'Profile Information',
    'clients_all_over_the_world' => 'Clients All Over The World!',
    'applications' => 'Applications',
    'accounts' => 'Accounts',
    'total_new_mt_accounts' => 'Registered MetaTrader Accounts',
    'total_deposits' => 'Total Deposits',
    'total_deposits_mtd' => 'Total Deposits (MTD)',
    'net_deposits_mtd' => 'Net Deposit (MTD)',
    'net_deposits' => 'Net Deposit',
    'latest_tickets' => 'Account Support Tickets',
    'recently_assigned' => 'Recently Assigned Applications',
    'deposit_fund' => 'Deposit Funds',
    'total_withdrawals' => 'Total Withdrawals',
    'total_withdrawals_mtd' => 'Total Withdrawals (MTD)',
    'equity' => 'Equity',
    'margin' => 'Margin',
    'open_a_demo_account' => 'Open a Demo Account',
    'open_a_real_account' => 'Open a Trading Account',
    'equity_chart' => 'Equity chart history',
    'referrals' => 'Referrals',
    'referral' => 'Referral',
    'wallet_balance' => 'Wallet Balance',
    'profile' => 'Profile',
    'account_manager' => 'Account Manager',
    'not_assigned' => 'Not Assigned',
    'ticket_number' => 'Ticket No.',
    'user' => 'User',
    'submission_date' => 'Submission date',
    'status' => 'Status',
    'show_all_applications' => 'All Applications',
    'latest_transactions' => 'Account Transactions',
    'latest_orders' => 'Account Orders',
    'personal_information' => 'Profile Information',
    'ip_address' => 'IP Address',
    'change_password' => 'Change password',
    'current_password' => 'Current password',
    'subscription' => 'Subscription',
    'subscribe_our_daily_reports' => 'Subscribe to our market alerts:',
    'reports' => 'Reports',
    'create' => 'Create',
    'cancel' => 'Cancel',
    'actions' => 'Actions',
    'show' => 'Show',
    'display_name' => 'Display Name',
    'english' => 'English',
    'ID' => 'ID',
    'edit_role' => 'Update Role',
    'notification_email' => 'Notification Email',
    'department_role' => 'Department Role',
    'department_manager' => 'Department Manager',
    'permissions' => 'Permissions',
    'select_all' => 'Select All',
    'update' => 'Update',
    'create_role' => 'Create Role',
    'yes' => 'Yes',
    'no' => 'No',
    'show_role' => 'Role Details',
    'action' => 'Action',
    'update_permission' => 'Update permission',
    'table' => 'Table',
    'create_permissions' => 'Create permissions',
    'permission_name' => 'Permission Name',
    'reference_table' => 'Reference Table',
    'show_permission' => 'Show Permission',
    'affiliate_websites' => 'Affiliate Websites',
    'affiliate_website' => 'Affiliate Website',
    'created_at' => 'Created On',
    'updated_at' => 'Updated On',
    'create_affiliate_website' => 'Create Affiliate Website',
    'edit_affiliate_website' => 'Update Affiliate Website',
    'create_trading_platform' => 'Create Trading Platform',
    'make_it_visible_in' => 'Make it visible in',
    'show_trading_platform' => 'Show Trading platform',
    'short_name' => 'Short Name',
    'create_referrals' => 'Create Referrals',
    'edit_referral' => 'Update Referral',
    'show_referral' => 'Show Referral',
    'abbreviation' => 'Abbreviation',
    'created_date' => 'Created On',
    'create_language' => 'Create Language',
    'edit_language' => 'Update Language',
    'show_language' => 'Show Language',
    'phone_code' => 'Phone code',
    'region' => 'Region',
    'create_countries' => 'Create countries',
    'country_code' => 'Country code',
    'MT_region' => 'MetaTrader Region',
    'select' => 'Select',
    'show_country' => 'Show Country',
    'edit_country' => 'Edit Country',
    'edit' => 'Edit',
    'currency_name' => 'Currency Name',
    'code' => 'Code',
    'edit_currency' => 'Update Currency',
    'currency_type' => 'Currency Type',
    'is_walletable' => 'Is Walletable',
    'create_new_currency' => 'Create New Currency',
    'show_currency' => 'Show Currency',
    'application_types' => 'Application Types',
    'create_application_types' => 'Create Application Types',
    'key' => 'Key',
    'show_application_type' => 'Show Application Type',
    'membership_types' => 'Membership Types',
    'min_points' => 'Min Points',
    'max_points' => 'Max Points',
    'show_membership_type' => 'Show Membership Type',
    'update_membership_type' => 'Update Membership Type',
    'icon' => 'Icon',
    'create_report_types' => 'Create Report Type',
    'edit_report_types' => 'Update Report Type',
    'types' => 'types',
    'show_report_types' => 'Show Report Type',
    'title' => 'Title',
    'contact_request_subjects_list' => 'Contact Request Subjects',
    'delete' => 'Delete',
    'edit_contact_request_subject' => 'Update Contact Request Subject',
    'show_contact_request_subject' => 'Show Contact  Request Subject',
    'create_new_contact_request_subject' => 'Create New Contact Request Subject',
    'account_form_categories' => 'Account form categories',
    'create_account_form_categories' => 'Create Account Form category',
    'edit_account_form_categories' => 'Update Account Form category',
    'show_account_form_categories' => 'show Account form category',
    'product_categories' => 'Product Categories',
    'parent' => 'Parent',
    'create_product_category' => 'Create Product Category',
    'parent_category' => 'Parent Category',
    'trading_platform' => 'Trading Platform',
    'edit_product_category' => 'Update Product Category',
    'create_new_swap_category' => 'Create New Swap Category',
    'show_swap_category' => 'Show Swap Category',
    'edit_swap_category' => 'Update Swap Category',
    'category' => 'Category',
    'errors_list' => 'Errors',
    'model' => 'Model',
    'error' => 'Error',
    'edit_error_log' => 'Update Error',
    'table_name' => 'table name',
    'file' => 'File',
    'line' => 'Line',
    'resolved' => 'Resolved',
    'list' => 'List',
    'add_to_favorite' => 'Add to Watchlist',
    'favorite' => 'Favorite',
    'export' => 'Export',
    'or_create_one' => 'Or create one',
    'send_email' => 'Send Email',
    'assign' => 'Assign',
    'edit_relation' => 'Update Relation',
    'disable' => 'Disable',
    'enable' => 'Enable',
    'create_application' => 'Create Application',
    'application_information' => 'Application Information',
    'referral_group' => 'Referral Offer',
    'holder_1' => 'Holder 1',
    'company_details' => 'Company Details',
    'company_number' => 'Company number',
    'watch_lists' => 'Watch Lists',
    'list_name' => 'List Name',
    'mt_account' => 'MetaTrader Login',
    'create_watch_list' => 'create watch',
    'follow_up' => 'Follow Up',
    'kindly_add_your_comments_about' => 'There are no follow-up comments!',
    'write_comments' => 'What\'s up today?',
    'verified' => 'Verified',
    'passport_id' => 'Passport ID',
    'metatrader_accounts' => 'MetaTrader Accounts',
    'show_application' => 'show application',
    'order_details' => 'Order Details',
    'holder_details' => 'Holder Details',
    'account_number_IBAN' => 'Account Number / IBAN',
    'swift_code' => 'Swift Code',
    'data_changed' => 'Data Changed',
    'field' => 'Field',
    'old' => 'Old',
    'transfer_to' => 'Accountability',
    'type_your_comment' => 'Type your comment..',
    'this_comment_will_be_saved_in_the_archive' => 'This comment will be saved in the archive, if you reject this order .. the comment will be sent to the user!',
    'if_you_want_to_transfer' => 'If you want to transfer the order to another department, leave the status is pending.',
    'accepted_fields' => 'Accepted fields',
    'if_this_application_is_rejected' => 'Kindly remove the check for the rejected field to inform the client to re-fill the information. if you don\'t remove any checkbox. that means INGOT doesn\'t accept this client for the reason you already mentioned in the comment field.',
    'received_amount_note' => 'This field is the approved amount you will transfer to the client.',
    'transaction_type' => 'Transaction Type',
    'requested' => 'Requested',
    'received' => 'Received',
    'user_details' => 'User Details',
    'user_name' => 'Username',
    'transaction_details' => 'Transaction Details',
    'payment_type' => 'Payment Type',
    'exchange_rate' => 'Exchange Rate',
    'fees' => 'Fees',
    'requested_amount' => 'Requested Amount',
    'received_amount' => 'Received Amount',
    'received_currency' => 'Received Currency',
    'select_account' => 'Select Account',
    'recent_transactions' => 'Recent Transactions',
    'there_are_no_transactions' => 'There are no Transactions!',
    'create_mt_account' => 'Create MetaTrader Account',
    'mt_group' => 'MetaTrader Group',
    'send_reports' => 'Send Reports',
    'application' => 'Application',
    'edit_mt_account' => 'Update MetaTrader Account',
    'registration_date' => 'Registration Date',
    'commissions' => 'Commissions',
    'transactions' => 'Transactions',
    'total' => 'Total',
    'from_date' => 'From Date',
    'to_date' => 'To Date',
    'free_margin' => 'Free Margin',
    'ticket' => 'Ticket',
    'time' => 'Time',
    'entry' => 'Entry',
    'position_id' => 'Position ID',
    'seos_list' => 'SEO',
    'create_seo' => 'Create Seo',
    'keywords' => 'Keywords',
    'image' => 'Image',
    'show_seo' => 'Show Seo',
    'slug' => 'Slug',
    'edit_seo' => 'Update Seo',
    'asic_questions_list' => 'Eligibility Questions',
    'create_new_ASIC_questions' => 'Create New Eligibility Questions',
    'edit_eligibility_question' => 'Update Eligibility Question',
    'show_eligibility_question_details' => 'Show ASIC Question',
    'search_question_answer' => 'Search Question/Answer',
    'ASIC_answers_list' => 'ASIC Answers',
    'answer' => 'Answer',
    'correct' => 'Correct',
    'edit_ASIC_answers' => 'Update ASIC Answers',
    'ASIC_questions_id' => 'ASIC Question',
    'question_answer' => 'Question Answer',
    'create_ASIC_answers' => 'Create ASIC Answers',
    'show_answer_details' => 'Show ASIC Answer Details',
    'instrument' => 'Instrument',
    'attach_sheet' => 'Attach Sheet',
    'registration_buttons' => 'Registration Buttons',
    'alignment' => 'Alignment',
    'create_slider' => 'Create slider',
    'allow_registration_buttons' => 'Allow Registration Buttons',
    'left' => 'Left',
    'center' => 'Center',
    'update_slider' => 'Update Slider',
    'show_slider' => 'Show Slider',
    'create_instruments' => 'Create Instruments',
    'stop_level' => 'Stop Level',
    'update_instrument' => 'Update Instrument',
    'show_instrument' => 'Show Instrument',
    'contracts_list' => 'Contracts',
    'create_contracts' => 'Create contracts',
    'future' => 'Future',
    'spot' => 'Spot',
    'shares' => 'Shares',
    'listed_companies' => 'Listed Companies',
    'tick_price' => 'Tick price',
    'edit_contracts' => 'Update contracts',
    'contract_list' => 'Contract',
    'all_type' => 'All Type',
    'report_date' => 'Report Date',
    'show_sections' => 'Show Sections',
    'market_report_type' => 'Market Report Type',
    'contents' => 'Contents',
    'create_reports' => 'Create Reports',
    'edit_reports' => 'Update Reports',
    'report_sections_list' => 'Report Sections',
    'report' => 'Report',
    'create_sections_for_market_report' => 'Create Sections for Market Report',
    'edit_report_instruments' => 'Update Report Section',
    'show_report_instruments' => 'Show Report Instruments',
    'create_index_dividend' => 'Create Index dividend',
    'update_index_dividend' => 'Update index Dividend',
    'show_index_dividend' => 'Show index Dividend',
    'publish' => 'Publish',
    'create_new_swap_data' => 'Create New Swap Data',
    'you_can_attach_CSV_sheet' => 'You can attach CSV Sheet or add it one by one.',
    'show_swap_data' => 'Show Swap Data',
    'account_form_category' => 'Account Form Category',
    'create_account_forms' => 'Create Account Forms',
    'all_countries' => 'All Countries',
    'edit_account_form' => 'Update Account Form',
    'enable_in' => 'Enable In Countries',
    'show_account_form' => 'Show Account Form',
    'create_FAQ' => 'Create FAQ',
    'update_faqs' => 'Update FAQ',
    'show_trashed' => 'Show Trashed',
    'role' => 'Role',
    'completed' => 'Completed',
    'edit_user' => 'Update User',
    'user_documents' => 'User Documents',
    'applications_comments_list' => 'Applications comments',
    'update_application_comment' => 'Update application comment',
    'affiliate_website_configuration_details' => 'Affiliate Website Configuration Details',
    'application_wallets' => 'Application Wallets',
    'create_wallet' => 'Create Wallet',
    'edit_wallet' => 'Update Wallet',
    'show_wallet' => 'Show Wallet',
    'users_list' => 'Users',
    'create_user' => 'Create User',
    'show_user' => 'Show User',
    'create_document' => 'Create Document',
    'edit_document' => 'Update Document',
    'show_document' => 'Show Document',
    'user_eligibilities' => 'User Eligibilities',
    'result' => 'Result',
    'create_user_eligibility' => 'Create User Eligibility',
    'quesiton' => 'Question',
    'incorrect' => 'Incorrect',
    'application_banks' => 'Application Banks',
    'gateway' => 'Gateway',
    'create_application_payment_bank' => 'Create a new Banking details',
    'edit_bank_payent_info' => 'Update Bank Payment',
    'show_bank_payent_info' => 'Show Bank Payment',
    'application_gateways' => 'Application Gateways',
    'create_application_payment_gateway' => 'Create Application Payment Gateway',
    'edit_application_gateway' => 'Update Application Gateway',
    'show_application_gateways' => 'Application Payment Gateways Details',
    'not_contacted' => 'Not Contacted',
    'contacted' => 'Contacted',
    'email_address' => 'Email Address',
    'contact_time' => 'Contact Time',
    'no_comment' => 'No comment',
    'create_contact_request' => 'Create Contact Request',
    'contact_request' => 'Contact Request',
    'create_user_orders' => 'Create Order',
    'edit_user_orders' => 'Update Order',
    'show_user_orders' => 'Show Order',
    'send_to' => 'Send To',
    'order_log' => 'Order Log',
    'group_offer' => 'Select Group Offer',
    'create_transactions' => 'Create transactions',
    'required_amount' => 'Required Amount',
    'required_currency' => 'Required Currency',
    'update_transaction' => 'Update Transaction',
    'show_transaction' => 'Show Transaction',
    'subscriptions' => 'Subscriptions',
    'registered_user' => 'Registered User',
    'create_subscriptions' => 'Create Subscriptions',
    'update_subscriptions' => 'Update subscriptions',
    'show_mt_group_specification' => 'Show Group Specs',
    'mt_group_specification' => 'MetaTrader Account Types',
    'group_type' => 'Group type',
    'group_name' => 'Group Name',
    'min_deposit' => 'Min Deposit',
    'spread_type' => 'Spread Type',
    'edit_mt_group_specification' => 'Update Group Specs',
    'create_MT_group_specification' => 'Create MetaTrader Group Specs',
    'min_lot_size' => 'Min Lot Size',
    'eas_allowed' => 'EA\'s Allowed',
    'mt_groups_list' => 'MetaTrader Groups',
    'mt_account_id' => 'MetaTrader Account ID',
    'number' => 'Number',
    'edit_mt_group' => 'Update MetaTrader Group',
    'group_number' => 'Group Number',
    'create_mt_group' => 'Create MetaTrader Group',
    'show_mt_group' => 'Show Group',
    'application_relations' => 'Application Relations',
    'master_account' => 'Master Account',
    'account' => 'Account',
    'ib_offers_list' => 'Ib Offers',
    'mt_login' => 'MetaTrader Login',
    'create_ib_offers' => 'Create Ib Offers',
    'offer_document' => 'Offer Document',
    'update_ib_offers' => 'Update Ib Offer',
    'company' => 'Company',
    'company_holder_list' => 'Company Holder',
    'create_company_holder' => 'Create Company holder',
    'edit_company_holder' => 'Update Company holder',
    'show_company_holder' => 'Company holder details',
    'citizenship_id' => 'Citizenship Id',
    'create_company' => 'Create company',
    'update_companies_form' => 'Update Company',
    'details_companies_form' => 'Show Company',
    'temporary_funds_list' => 'Deposit Attempts',
    'payments_gateway' => 'Payments Gateway',
    'req_amount' => 'Req. Amount',
    'rec_currency' => 'Rec. Currency',
    'show_temporary_fund' => 'Show Deposit Attempt',
    'max_deposit' => 'Max Deposit',
    'create_new_payment_gateways' => 'Create New Payment Gateways',
    'max_withdrawal' => 'Max. Withdrawal',
    'deposit_fees' => 'Deposit Fees',
    'withdrawal_fees' => 'Withdrawal Fees',
    'allow_withdraw' => 'Allow withdraw',
    'edit_payment_gateway' => 'Update Payment Gateway',
    'show_payment_gateway' => 'Show Payment Gateway',
    'user_activities_list' => 'User Activities',
    'object_id' => 'Object ID',
    'object_model' => 'Object Model',
    'user_log_details' => 'User Log Details',
    'url' => 'URL',
    'original' => 'Original',
    'changes' => 'Changes',
    'created_at_time' => 'Created at Time',
    'mobile' => 'Mobile',
    'affiliate_website_configurations_list' => 'Affiliate Website Configs',
    'website_name' => 'Website Name',
    'value' => 'Value',
    'last_update_time' => 'Last Update Time',
    'create_new_affiliate_website_configuration' => 'Create New Affiliate Website Configuration',
    'select_affiliate' => 'Select Affiliate',
    'affiliate_website_configuration_edit' => 'Update Affiliate Website Configuration',
    'notes' => 'Notes',
    'create_corporate_event' => 'Create Corporate Event',
    'close' => 'Close',
    'create_feedback' => 'Create Feedback',
    'show_feedback' => 'Show Feedback',
    'edit_feedback' => 'Update Feedback',
    'show_pages' => 'Show Page',
    'update_page' => 'Update Page',
    'create_pages' => 'Create Pages',
    'create_pages_form' => 'create Pages',
    'pages' => 'Pages',
    'model_name' => 'Model Name',
    'field_name' => 'Field Name',
    'object_key' => 'Object Key',
    'create_translation_elements' => 'Create Translation',
    'translation' => 'Translation',
    'price' => 'Price',
    'USD' => 'USD',
    'buy' => 'Buy',
    'sell' => 'Sell',
    'product' => 'Product',
    'main_banner' => 'Main banner',
    'margin_percentage' => 'Margin Level',
    'permission' => 'Permission',
    'all_platforms' => 'All Platforms',
    'platform_configurations_list' => 'Platform Configurations',
    'create_new_platform_config' => 'Create a new platform configuration',
    'show_platform_config' => 'Show Platform configuration',
    'edit_platform_config' => 'Update Platform configuration',
    'settings' => 'Settings',
    'jobs_list' => 'Jobs',
    'queue' => 'Queue',
    'payload' => 'Payload',
    'attempts' => 'Attempts',
    'report_sub_content_note' => 'This field should contain a report summary, it will be sent to the clients emails.',
    'please_note' => 'Please Note',
    'entity_redirect_entity_note' => 'Due to regulatory requirements, when you click "Continue" you will be redirected to INGOT Brokers\' website that matches your region and jurisdiction.',
    'entity_redirect_entity_validation_note' => 'INGOT Brokers does not serve clients from the chosen country on this site. <a href=":URL" class="dashed-border">Clicking here</a> redirects you to INGOT Brokers\' website fitting your country.',
    'continue' => 'Continue',
    'login_license_redirect_entity_note' => 'Due to regulatory requirements, when you click <a href=":url" class="dashed-border">Login</a> you will be redirected to INGOT Brokers\' website that matches your region and jurisdiction.',
    'deleted_successfully' => 'The record has been deleted successfully!',
    'delete_failed' => 'The record could not deleted due to the record has related items!',
    'added_successfully' => 'Added successfully!',
    'source' => 'Source',
    'message' => 'Message',
    'authorized_regions' => 'Authorized Regions',
    'wrong' => 'Wrong',
    'contact_requests_list' => 'Contact requests',
    'any_time' => 'Any time',
    'role_name' => 'Role Name',
    'body' => 'Body',
    'assign_to' => 'Assign to',
    'document' => 'Document',
    'report_sent_message' => 'The report has been scheduled to send to all subscribers',
    'banking_details_list' => 'Banking details',
    'link' => 'Link',
    'youtube_link' => 'Youtube Link',
    'right' => 'Right',
    'create_banking_details' => 'Create Banking Details',
    'show_banking_details' => 'Show Banking Details',
    'create_account_relation' => 'Assign Application',
    'show_account_relation' => 'Show application relation',
    'edit_account_relation' => 'Update application relation',
    'number_of_digits' => 'Number of digits',
    'edit_corporate_event' => 'Update Corporate Event',
    'create_contact' => 'Create Contact',
    'update_contact' => 'Update Contact',
    'show_contact' => 'Show Contact',
    'transaction_log' => 'Transaction log',
    'leave_empty_to_keep_the_same' => 'Leave empty to keep the same.',
    'for' => 'for',
    'id_front' => 'Front ID Card',
    'id_back' => 'Back ID Card',
    'ib_offers_details' => 'Show ib offer',
    'mt_account_details' => 'MetaTrader account details',
    'application_enabled_successfully' => 'The application enabled successfully',
    'edit_watch_list' => 'Update watch-list',
    'show_watch_list' => 'Show watch-list',
    'user_applications' => 'User Applications',
    'refund_mt_note' => 'If you reject this order, you can choose either refund the amount to the wallet or MetaTrader account.',
    'choose_file' => 'Choose file',
    'edit_translation_elements' => 'Update translation',
    'daily' => 'Daily',
    'weekly' => 'Weekly',
    'monthly' => 'Monthly',
    'yearly' => 'Yearly',
    'commissions_chart' => 'Cumulative Commissions',
    'transactions_chart' => 'Cumulative User Deposits & Withdrawals',
    'institutional' => 'Institutional',
    'request_a_call_note' => 'By submitting this form, you are agreeing that INGOT Brokers can contact you over the phone about your chosen subject or service.',
    'ingot_cookies_alert' => '<b>Cookies & Privacy:</b> Cookies assist us in securing your trading activities and enhancing the performance of our website. Cookies are small text files sent from the web server to your computer. Cookies used by us do not contain any personal client data or account and password information. Click <a href=":URL" target="_blank"><span class="border-dashed">here</span></a> to read more.',
    'accept' => 'Accept',
    'rate' => 'Rate',
    'change' => 'Change',
    'share_social' => 'You can share your referral link via',
    'country_of_residence' => 'Country of Residence',
    'company_website' => 'Company Website',
    'company_address' => 'Company Address',
    'institutional_contact_requests' => 'Institutional Contacts',
    'institutional_service' => 'Institutional Services',
    'send_notification_note' => 'By checking this box, you are confirming to send an email notification to the client.',
    'mt_account_limit' => 'MT accounts limit',
    'mt_accounts_limit_note' => 'Number of allowed accounts that could be opened for each client under this agent',
    'skrill_email' => 'Skrill Email',
    'refund' => 'Refund',
    'metatrader' => 'MetaTrader',
    'wallet' => 'E-Wallet',
    'open_rates_email_chart' => 'Cumulative Open Rate',
    'page' => 'Page',
    'history' => 'History',
    'swaps_history' => 'Swaps History',
    'edit_swap_data' => 'Update Swap Data',
    'filter_receipts' => 'Filter Receipts',
    'ingot_popup_send_report_modal_message' => 'Filter Email Receipts to whose:',
    'login_log_count' => 'Cumulative frequency of Portal Logins',
    'affiliate_link_chart' => 'Referral Links Chart',
    'register' => 'Register',
    'thank_you_contact' => 'Thank you! We will contact you shortly.',
    'roles' => 'Roles',
    'arabic' => 'Arabic',
    'websites_translatable_config' => 'Trans. Websites Config',
    'payment_gateways' => 'Payment Gateways',
    'languages' => 'Languages',
    'countries' => 'Countries',
    'request_card' => 'Request your INGOT Card',
    'request_card_msg' => 'You do not have a registered card request Your INGOT Card',
    'middle_name' => 'Middle Name',
    'third_name' => 'Third Name',
    'full_address' => 'Full Address',
    'customer_id' => 'Client Wallet Number/ internal reference ID',
    'name_on_card' => 'Name On Card',
    'profession' => 'Profession',
    'market_report_types' => 'Market Report Types',
    'swap_categories' => 'Swap Categories',
    'logs' => 'Logs',
    'user_activities' => 'User activities',
    'user_login_logs' => 'Portal Login History',
    'seos' => 'SEOs',
    'contracts' => 'Contracts',
    'market_reports' => 'Market Reports',
    'sliders' => 'Sliders',
    'users' => 'Users',
    'temporary_funds' => 'Deposit Attempts',
    'companies' => 'Companies',
    'ib_offers' => 'IB offers',
    'metatrader_groups' => 'MetaTrader Groups',
    'user_transactions' => 'User Transactions',
    'user_orders' => 'User Orders',
    'contact_requests' => 'Contact Requests',
    'feedbacks' => 'Feedbacks',
    'contacts' => 'Contacts',
    'and' => 'and',
    'contact_date' => 'Reminder Date',
    'remind' => 'Remind',
    'myself' => 'Myself',
    'save' => 'Save',
    'contact_reminder' => 'Contact Reminder',
    'reminder_saved' => 'This reminder Has been set successfully',
    'back_to_home' => 'Back to home page',
    'current_equities' => 'Current Equities',
    'reminder' => 'Reminder',
    'assign_contact' => 'Assign Contact',
    'main_specialty' => 'Main Specialty',
    'bio' => 'Bio',
    'channels' => 'Channels',
    'social_links' => 'Social Media Links',
    'followers_count' => 'Followers Count',
    'engagement' => 'Engagement',
    'website_url' => 'Website URL',
    'capital' => 'Capital',
    'license_type_legal_status' => 'License Type / Legal Status',
    'company_size' => 'Company Size',
    'gm_info' => 'GM (name, Phone, Email and LinkedIn Link)',
    'ceo_info' => 'CEO (name,Phone, Email and LinkedIn Link)',
    'sales_manager_info' => 'Name of the Sales Manager (name,Phone, Email and LinkedIn Link)',
    'sales_executive_info' => 'Name of the Sales Executive (name,Phone, Email and LinkedIn Link)',
    'rate_contact' => 'Rate Contact',
    'remove_selection' => 'Remove Selection',
    'created_by' => 'Created By',
    'contacts_samples_note' => 'You can use the following samples for CSV sheet data entry. <a class="dashed-border" target="_blank" href=":individual_url">Individual</a>, <a class="dashed-border" target="_blank" href=":broker_url">Broker</a>, <a class="dashed-border" target="_blank" href=":ib_url">IB</a>.',
    'import_campaigns_samples_note' => 'You can use the following samples for CSV sheet data entry. <a class="dashed-border" target="_blank" href=":url">Campaign</a>',
    'import_webinars_samples_note' => 'You can use the following samples for CSV sheet data entry. <a class="dashed-border" target="_blank" href=":url">Webinar</a>',
    'import_seminars_samples_note' => 'You can use the following samples for CSV sheet data entry. <a class="dashed-border" target="_blank" href=":url">Seminar</a>',
    'mailable' => 'Mailable',
    'no_subscribe_reports' => 'There are no reports to subscribe right now',
    'reminders_calendar' => 'Reminders calendar',
    'archive' => 'Archive',
    'archived' => 'Archived',
    'unarchive' => 'Unarchive',
    'application_unarchived_successfully' => 'The application has been successfully removed from the archived list',
    'contacts_rate' => 'Contacts Rate',
    'internal' => 'Internal',
    'my_accounts' => 'My Accounts',
    'reload' => 'Reload',
    'group_display_name_note' => 'This name will show to the client when they are trying to open an account.',
    'request_succ_message' => 'We have received your request. Thank you!',
    'app_chart_by_reg_date' => 'Registered User Applications',
    'mt_accounts' => 'MetaTrader Accounts',
    'mt_accounts_chart_by_group' => 'Registered MetaTrader Accounts By Group',
    'mt_accounts_chart_by_country' => 'Registered MetaTrader Accounts By Country',
    'mt_accounts_chart_by_reg_date' => 'Registered MetaTrader Accounts',
    'trade' => 'Trade',
    'group_mt_account_note' => 'Select the agent if this group belongs to the approved agent application account. Otherwise, leave it empty',
    'report_already_sent' => 'The report already sent to the clients!',
    'fund_wallet' => 'Fund Wallet/Account',
    'fund_wallet_note' => 'By checking this box, you are confirming to add the fund to the client wallet/account.',
    'ip_access_permission' => 'IP Access Permissions',
    'create_ip_access_permission' => 'Create IP Access Permission',
    'edit_ip_access_permission' => 'Update IP Access Permission',
    'show_ip_access_permission' => 'Show IP Access Permission',
    'user_sessions' => 'User Sessions',
    'last_activity' => 'Last Activity',
    'closed_by' => 'Deal Closed by',
    'ticket_closed_by' => 'Closed by',
    'followed_up_by' => 'Deal Followed up by',
    'visitor_types' => 'Visitor Types',
    'authorized_roles' => 'Authorized roles',
    'show_affiliate_website' => 'Show Affiliate Website',
    'edit_trading_platform' => 'Update Trading Platform',
    'show_error_log' => 'Show Error',
    'show_corporate_event' => 'Show Corporate Event',
    'previous' => 'Previous',
    'next' => 'Next',
    'update_application' => 'update application',
    'show_product_category' => 'Show Product Category',
    'show_faqs' => 'Show FAQs',
    'edit_banking_details' => 'Edit Financial Details',
    'show_product' => 'Show product',
    'update_institutional_service' => 'Update Institutional Service',
    'show_institutional_service' => 'Show Institutional Service',
    'cant_transfer_different_platforms_message' => 'Can\'t transfer funds to different platforms right now!',
    'something_went_wrong' => 'Something went wrong please try again later!',
    'send_report_analitics_message' => 'In order to display the statistics, please send the reports to the clients.',
    'group_details' => 'Group details',
    'delete_all' => 'Delete All',
    'add_comment' => 'Add Comment',
    'summery' => 'Summary',
    'timeline' => 'Timeline',
    'internal_transfer_conduct_balance_note' => 'Conduct balance operation without checking the free margin and the current balance on the account.',
    'logins_by_browser' => 'Logins by browser',
    'logins_by_platform' => 'Logins by Platform',
    'equity_report' => 'Equity & Credit Report',
    'liquidation_report' => 'Liquidation Report',
    'scalping_report' => 'Scalping Report',
    'watch_list' => 'Watch List',
    'sort' => 'Sort',
    'ascending' => 'Ascending',
    'descending' => 'Descending',
    'flag' => 'Flag',
    'failed_jobs' => 'Failed Jobs',
    'connection' => 'Connection',
    'language' => 'Language',
    'admin_name' => 'Admin Name',
    'number_of_lots' => 'No. of lots',
    'great_than' => 'Great than',
    'less_than' => 'Less than',
    'general_configuration' => 'General Settings',
    'create_general_configuration' => 'Create general configuration',
    'show_general_configuration' => 'Update general configuration',
    'edit_general_configuration' => 'Show general configuration details',
    'group_type_specs_note' => 'When the client wants to open an account and choose this group, the selected group type specifications will show to the client',
    'position_price' => 'Position price',
    'iso_code' => 'ISO Code',
    'clients_number' => '{0,1} :count client|[2,*] :count clients',
    'comments_number' => '{0,1} :count comment|[2,*] :count comments',
    'deals_number' => '{0,1} :count deal|[2,*] :count deals',
    'referral_accounts' => 'Referral accounts',
    'closed_deals' => 'Closed deals',
    'following_up_deals' => 'Following up deals',
    'there_are_no_activities' => 'There are no activities!',
    'their_application' => 'their application',
    'offers' => 'Offers',
    'entity' => 'Entity',
    'credit' => 'Credit',
    'thirty_days_total_deposits' => '30 Days Deposits',
    'seven_days_total_deposits' => '7 Days Deposits',
    'thirty_days_total_withdrawals' => '30 Days Withdrawals',
    'seven_days_total_withdrawals' => '7 Days Withdrawals',
    'thirty_days_total_profits' => '30 Days Profits',
    'total_profit' => 'Total Profit',
    'thirty_days_total_commissions' => '30 Days Commissions',
    'seven_days_total_commissions' => '7 Days Commissions',
    'seven_days_total_profits' => '7 Days Profits',
    'total_commissions' => 'Total Commissions',
    'highest_profitable_trade' => 'Highest Profitable Trade',
    'highest_losing_trade' => 'Highest Losing Trade',
    'last_trade_date' => 'Last Trade Date',
    'last_access_date' => 'Last Access',
    'thirty_days_total_trades' => '30 Days Trades Count',
    'week_total_trades' => '7 Days Trades Count',
    'total_trades_count' => 'Trades Count',
    'number_winning_trades' => 'Winning Trades per Transaction',
    'number_losing_trades' => 'Losing Trades per Transaction',
    'profitability_ratio' => 'Profitability Ratio',
    'thirty_days_profitability_ratio' => '30 Days Profitability Ratio',
    'seven_days_profitability_ratio' => '7 Days Profitability Ratio',
    'week_equity_growth' => '7 Days Equity Growth',
    'thirty_days_equity_growth' => '30 Days Equity Growth',
    'average_profit_per_profitable_trades' => 'Avg. Profit per Profitable Trades',
    'average_loss_per_losing_trades' => 'Avg. Loss Per Losing Trades',
    'top_traded_instruments' => 'Top Traded Instruments By Volume',
    'positions_by_sentiment' => 'Positions by Sentiment',
    'positions' => 'Positions',
    'manage_websites' => 'Managed websites',
    'net_deposit' => 'Net Deposit',
    'reason' => 'Reason',
    'day_ago' => '{0} Today |{1} :count day ago |[2,*] :count days ago',
    'total_expert_profit' => 'Total profit from EA',
    'total_expert_volume' => 'Total traded lots from EA',
    'traded_lots_by_volume' => 'Traded lots by Volume',
    'total_traded_lots' => 'Total Traded Lots',
    'deals' => 'Deals',
    'orders' => 'Orders',
    'commission' => 'Commission',
    'current_price' => 'Current price',
    'order_state' => 'State',
    'closed' => 'Closed',
    'sumsub_message_title' => 'Your documents are under review',
    'sumsub_message_content' => 'The status of your account will change automatically once the review is completed. If you experience any difficulties, please contact our customer support.',
    'summary' => 'Summary',
    'deal' => 'Deal',
    'ib_permissions' => 'Partner Permissions',
    'create_new_settings' => 'Create new Settings',
    'edit_settings' => 'Edit Settings',
    'client_restrictions' => 'Client Restrictions',
    'client_restrictions_note' => 'Select the features you want to disable it from the clients under the selected IB.',
    'ib_restrictions_note' => 'Select the features you want to disable it from selected IB.',
    'contact_with_agent_restricted_note' => 'You can\'t process right now!, Please contact the customer support',
    'exception' => 'Exception',
    'create_app_available_groups' => 'Create App Available Group',
    'edit_app_available_groups' => 'Edit App Available Group',
    'show_app_available_groups' => 'Show App Available Group',
    'app_available_groups' => 'App available groups',
    'trading' => 'Trading',
    'spreads' => 'Spreads',
    'leverage' => 'Leverage',
    'client' => 'Client',
    'direction' => 'Direction',
    'templates' => 'templates',
    'create_template' => 'Create template',
    'edit_template' => 'Edit Template',
    'version' => 'Version',
    'website' => 'Website',
    'thank_you' => 'Thank you!',
    'professional' => 'Professional',
    'will_contact_you' => 'We will contact you shortly.',
    'education' => 'Education',
    'market_alerts' => 'Market Alerts',
    'apply_now' => 'apply now',
    'growth' => 'Growth',
    'regulation' => 'Regulation',
    'legal_documents' => 'legal documents',
    'careers' => 'Careers',
    'learn_more' => 'Learn More',
    'avg' => 'AVG',
    'trading_platforms' => 'Trading Platforms',
    'input_type' => 'Input Type',
    '422_unprocessable_entity' => 'Validation failed.',
    'introducing_broker' => 'Introducing Broker',
    '405_method_not_allowed' => 'Method Not Allowed',
    'faq_categories' => 'Faq Categories',
    'create_faq_categories' => 'Create Faq Categories',
    'show_faq_categories' => 'Show Faq Categories',
    'update_faq_categories' => 'Update Faq Categories',
    'websites' => 'websites',
    'featured' => 'Featured',
    'nationality' => 'Nationality',
    'years_of_experience' => 'Years of Experience',
    'upload_cv' => 'Upload CV',
    'demo' => 'Demo',
    'agent' => 'Agent',
    'fund' => 'Fund',
    'download' => 'Download',
    'high' => 'High',
    'faq_help' => 'What do you need help with?',
    'we_want_to_hear_what_you_love_and_what_you_think' => 'Have more questions? Submit a request',
    'or_login_via' => 'or login via',
    'dont_have_account' => 'Don\'t have an account?',
    'here' => 'here',
    'available_on' => 'Available on',
    'desktop' => 'Desktop',
    'android' => 'Android',
    'ios' => 'iOS',
    'general' => 'General',
    'display_mode' => 'display mode',
    'two_auth_factor' => 'Two Auth Factor',
    'enabled' => 'Enabled',
    'multi_section' => 'Multi Section',
    'we_already_received_a_request_from_you' => 'We already received a message from you. You will be able to submit a new request in 24 hours.',
    'details' => 'Details',
    'assets' => 'Assets',
    'thumbnail' => 'Thumbnail',
    'theme' => 'Theme',
    'show_exceptions' => 'Exception Rules',
    'payment_gateway_exceptions' => 'Payment Gateway Exceptions',
    'create_payment_gateway_exception' => 'Create Payment Gateway Exceptions',
    'edit_payment_gateway_exception' => 'Edit Payment Gateway Exception',
    'show_payment_gateway_exception' => 'Show Payment Gateway Exception',
    'dash' => 'Dash',
    'voip' => 'VOIP',
    'calls_history' => 'Calls History',
    'caller_number' => 'Caller Number',
    'caller_name' => 'Caller Name',
    'destination_name' => 'Destination Name',
    'destination_number' => 'Destination Number',
    'caller_destination' => 'Caller Destination',
    'recording' => 'Recording',
    'duration' => 'Duration',
    'play_pause' => 'Play / Pause',
    'click_to_call' => 'Click to Call',
    'quick_actions' => 'Quick Actions',
    'international_number' => 'International number starts with 00',
    'extensions' => 'extensions',
    'extension' => 'Ext.',
    'show_extension' => 'Show Extension',
    'create_voip_extension' => 'Create VOIP Extension',
    'update_voip_extensions' => 'Update VOIP Extensions',
    'details_voip_extensions' => 'Show VOIP Extensions',
    'caller_id_name' => 'Caller ID Name',
    'caller_id_number' => 'Caller ID Number',
    'domain' => 'Domain',
    'add_mt_transaction' => 'New Transaction',
    'campaign_leads' => 'Campaign Leads',
    'campaign_name' => 'Campaign Name',
    'voip_extensions' => 'Voip Extensions',
    'src_cid_name' => 'CID Name',
    'src_cid_number' => 'CID Number',
    'has_trading_account' => 'Has a Trading Account',
    'referral_name' => 'Referral Name',
    'competition_tickets' => 'Competition Tickets',
    'companys_lei_no' => 'Company\'s LEI No',
    'companys_lei_no_ex' => 'Example: 589600DULQSSH4234H11',
    'companys_lei_no_tooltip' => 'The Legal Entity Identifier (LEI) is a 20-digit, alphanumeric code that enables clear and unique identification of companies participating in global financial markets. For more details, please contact customer support.',
    'payment_gateway_config' => 'Payment Gateway Config',
    'create_new_payment_gateway_configuration' => 'create new payment gateway configuration',
    'payment_gateway_configuration_edit' => 'edit payment gateway configuration',
    'payment_gateway_configuration_details' => 'payment gateway configuration details',
    'click_to_call_modal_message_1' => 'You are about to call this user. Are you sure that you wish to continue with this operation?',
    'click_to_call_modal_message_2' => 'Note: Please make sure that the call center software is working and you able to send a receive calls.',
    'show_config' => 'Show Config',
    'commissions_future' => 'Commissions Future',
    'account_currency' => 'Account Currency',
    'wallet_address' => 'Wallet Address',
    'you_will_get' => 'Receive In Your INGOT Wallet',
    'you_will_send' => 'Send From Your Crypto Wallet',
    'amount_in_usd' => 'Amount in USD',
    'amount_in_btc' => 'Amount in BTC',
    'amount_in_eth' => 'Amount in ETH',
    'crypto_placeorder_note_1' => 'You initially requested to deposit <b class="text-danger">:origin_amount</b>. However, for enhanced identification and expedited processing, we kindly ask you to fund your account with the unique amount of <b class="text-danger">:from_amount</b> instead',
    'crypto_placeorder_note_2' => 'After executing the transfer, please enter a proof of payment (TxHash)',
    'crypto_placeorder_note_4' => 'Your account will be funded with the :wallet_currency equivalent of :to_currency  at the transfer rate of the time you execute the transfer.',
    'crypto_placeorder_note_5' => 'You have 30 minutes to place the order before the session timeout.',
    'crypto_placeorder_note_6' => 'You have 15 minutes to place the order before the session timeout.',
    'copy' => 'Copy',
    'tx_hash' => 'TxHash',
    'cant_process_your_transaction' => 'We can\'t process your transaction right now. Kindly try again later!',
    'my_money' => 'My Money',
    'conversion_rate' => 'Conversion rate',
    'my_trading_tools' => 'My Trading Tools',
    'advanced_report' => 'Account Summary',
    'my_platforms' => 'My Platforms',
    'profile_completion' => 'Application completion is:',
    'become_a_partner' => 'Become a Partner',
    'label' => 'Label',
    'update_label' => 'Update Label',
    'account_label' => 'Account Label',
    'remaining' => 'Remaining',
    'verify_your_documents' => 'verify your documents',
    'verify_your_documents_description' => 'Before we activate your trading account, we are required to verify your documents.',
    'verify_your_document_venifits_description' => 'By verifying your account, you will',
    'verify_your_document_venifits_contetn_1' => 'Open a live account and start trading',
    'verify_your_document_venifits_contetn_2' => 'Have a dedicated account manager',
    'complete_verification' => 'Complete Verification',
    'internal_transfer_has_credit' => 'You cannot transfer right now. Please contact :support_email',
    'instant' => 'Instant',
    'execution_type' => 'Execution Type',
    'real_account_open_request' => 'Your request has been submitted for review. Please note that you must fund your wallet with the minimum deposit amount to activate the account.',
    'min_withdrawal' => 'Min. Withdrawal',
    'change_leverage' => 'Change leverage',
    'share_referral_link' => 'Share referral link',
    'user_becomes_client' => 'User becomes client',
    'client_trades' => 'Client Trades',
    'you_earn' => 'You earn!',
    'corporate_account' => 'Corporate Account',
    'my_profile' => 'My Profile',
    'finish' => 'Finish',
    'start_tour' => 'Start Tour',
    'take_tour_start' => 'This quick guide walks you through the features and functions of your personal account management space.',
    'take_tour_start_general' => 'The "My Profile" section allows you to manage your personal information and password, language settings, and email subscription preferences.',
    'take_tour_start_my_accounts' => 'In this section, you will be able to create new real or demo trading accounts.',
    'take_tour_start_my_money' => 'This is where you can make a deposit, transfer money to trading accounts or withdraw funds. Clicking the button at the bottom will allow you to review your transaction history.',
    'take_tour_start_my_trading_tools' => 'Your "Trading Tools" section has all the tools you need to analyze the markets and identify your next trading opportunity: Economic Calendar, Calculators and Currency Converter.',
    'take_tour_start_affliate_program' => 'Make your trading experience more rewarding with INGOT Brokers. In this section, you will be able to share your unique partner link with your network. The better you promote your link, the more you can earn!',
    'take_tour_start_my_platforms' => '"My Platforms" allows you to experience one of the world’s most popular trading platforms. This is where you can download the award-winning MT4 & MT5 on your desktop, tablet or mobile.',
    'take_tour_start_client_support' => 'Whenever you need support or have a question – click this button to get in touch with our support team.',
    'take_tour_start_my_dashboard' => 'Your dashboard is the hub to track all your activity on INGOT Brokers.',
    'take_tour_start_remaining' => 'In order to verify your trading account, you have to complete the missing information and save it.',
    'individual_account' => 'Individual account',
    'agent_account' => 'Agent account',
    'introducing_broker_key' => 'Key Advantages of our IB Program',
    'attractive_ib_commissions' => 'Attractive commission structure',
    'how_it_works' => 'How it works?',
    'partnership_leads' => 'Partnership Leads',
    'show_partnership_lead' => 'Show partnership lead',
    'edit_partnership_lead' => 'Edit partnership lead',
    'initial_deposit' => 'Initial Deposit',
    'referral_partnerships' => 'Referral',
    'open_account' => 'Open an Account',
    'create_account' => 'Create Account',
    'review_documents' => 'Review Documents',
    'review_answer' => 'Review Answer',
    'symbol_group' => 'Symbol Group',
    'grace_period' => 'Grace Period (Number of nights)',
    'storage_fees' => 'Storage Fees',
    'affiliate_websites_exceptions' => 'Affiliate Websites Exception Rules',
    'create_affiliate_websites_exception' => 'Create Affiliate Websites Exception',
    'show_affiliate_websites_exception' => 'Show Affiliate Websites Exception',
    'edit_affiliate_websites_exception' => 'Edit Affiliate Websites Exception',
    'redirect_to_website' => 'Redirect to Website',
    'has_balance' => 'Has Balance',
    'theme_mode' => 'Theme Mode',
    'reg_accounts' => 'Reg. Accounts',
    'num_accounts' => '[0,1] :count account|[2,*] :count accounts',
    'trans_count' => '[0,1] :count transaction|[2,*] :count transactions',
    'volume_sum' => '[0,1] :count lot|[2,*] :count lots',
    'referral_source' => 'Referral Source',
    'under_referral' => 'Under Referral/IB',
    'international' => 'International',
    'all' => 'All',
    'trading_hours' => 'Trading hours',
    'cost_per_acquisition_agreement' => 'Cost Per Acquisition (CPA) Agreement',
    'mt_specs_swaps' => 'MetaTrader Group Swaps',
    'create_mt_specs_swaps' => 'Create MetaTrader Group Swaps',
    'update_mt_specs_swaps' => 'Update MetaTrader Group Swaps',
    'show_mt_specs_swaps' => 'Show MetaTrader Group Swaps',
    'reff_links_with_id' => 'Referral links with your ID',
    'reff_links_with_keyword' => 'Referral links with your Keyword',
    'reff_links_with_keyword_c_1' => 'You can replace your referral ID by a unique word in your referral link to make referrals attraction more efficient.',
    'reff_links_with_keyword_c_2' => 'Note: Keyword change will result in invalidity of all the currently created links.',
    'referral_link' => 'Referral link',
    'keyword' => 'Keyword',
    'create_link' => 'Create your link',
    'open_account_checkbox_4' => 'I/We understand that it is my/our responsibility to align with my/our country’s tax laws and that my/our tax declarations are updated accordingly.',
    'you_will_get_crypto_note' => 'This is the amount that will be funded in your wallet and it is subject to conversion fees.',
    'you_will_send_crypto_note' => 'This is the amount that you will transfer from your crypto wallet to INGOT.',
    'copy_wallet_address' => 'Copy INGOT’s wallet address',
    'execute' => 'Execute',
    'execute_crypto_transaction' => 'Execute the transaction on the relevant crypto website or App',
    'proof' => 'Proof',
    'proof_crypto_transaction' => 'Provide a proof of payment (TxHash)',
    'confirm_crypto_transaction' => 'Click on the "Confirm" button below to transfer the :currency amount into your INGOT wallet',
    'important_notes' => 'Important Notes:',
    'or' => 'Or',
    'app_type' => 'Application Type',
    'notifications' => 'Notifications',
    'failed_deposit_1' => 'We\'re sorry to inform you that the deposit requested has failed.',
    'failed_deposit_2' => 'Please try again or try another payment method.',
    'in_progress' => 'In Progress',
    'mt_group_allocations' => 'Group Allocations',
    'mt_group_allocation' => 'Group Allocation',
    'allocation' => 'Allocation',
    'mt_group_allocation_note' => 'This field for back-office use, it used to allocate the groups for the equity report uses.',
    'create_mt_group_allocation' => 'Create Group Allocation',
    'edit_mt_group_allocation' => 'Edit Group Allocation',
    'show_mt_group_allocation' => 'Show Group Allocation',
    'full_calendar' => 'Full calendar',
    'daily_reminder_calender' => 'Daily Reminders Calendar',
    'start_trading' => 'Start Trading',
    'reg_groups' => 'Reg. Groups',
    'num_groups' => '[0,1] :count group|[2,*] :count groups',
    'equity_and_credit' => 'Equity & Credit on',
    'area' => 'Area',
    'c_e' => 'C/E',
    'period' => 'Period',
    'not_set' => 'Not set',
    'internal_transfer' => 'Internal Transfer',
    'internal_transfer_history' => 'Internal Transfers History',
    'displayed_data' => 'The following table presents data collected today. To view historical data, use the search box!',
    'valid_passport' => 'Valid Passport',
    'valid_national_identity_card' => 'Valid National Identity Card',
    'proof_of_identity' => 'Proof of Identity',
    'note_file_of_type' => 'jpeg, png, jpg, gif, pdf',
    'overview' => 'Overview',
    'month' => 'Month',
    'day' => 'Day',
    'year' => 'Year',
    'login_as' => 'Login as',
    'beneficiary_address' => 'Beneficiary Address',
    'number_of_accounts' => 'number of accounts',
    'separated_by_commas' => 'separated by commas',
    'logins' => 'Logins',
    'top_countries' => 'Top Countries',
    'num_applications' => '[0,1] :count application|[2,*] :count applications',
    'irr_support_note' => 'Please contact our support through: <a target="_blank" href="https://wa.me/message/W3FSMN3IZMGLC1">WhatsApp</a>, <a target="_blank" href="https://t.me/IngotBrokers_support">Telegram</a> or <a target="_blank" href="https://direct.lc.chat/8719361/32/">Live Chat</a>.',
    'proof_of_payment' => 'Proof of Payment',
    'id_card' => 'ID Card',
    'id_card_1' => 'Front ID',
    'id_card_2' => 'Back ID',
    'passport' => 'Passport',
    'utility_bill' => 'Proof of Address',
    'signature' => 'Proof of Signature',
    'drivers' => 'Driving License',
    'reg_confirm' => 'By opening this account, I confirm that I have had access to, reviewed, read, understood, and accept the full set of INGOT Brokers legal documents including the',
    'profile_info_updated_successfully' => 'The profile information has been submitted successfully!',
    'documents_updated_successfully' => 'The documents have been uploaded successfully!',
    'password_updated_successfully' => 'The password has been updated successfully!',
    'profile_settings_updated_successfully' => 'The profile have been updated successfully!',
    'eligibility_updated_successfully' => 'The client eligibility has been completed successfully!',
    'ticket_reply_created_successfully' => 'Your comment has been completed successfully!',
    'internal_transfer_successfully' => 'The amount has been transferred successfully!',
    'mt_password' => 'MetaTrader Password',
    'subscription_preferences' => 'Subscription Preferences',
    'subscription_preferences_updated_successfully' => 'Your subscription has been updated successfully!',
    'preferences_updated_successfully' => 'Your preferences has been updated successfully!',
    'neteller_account_number' => 'Neteller Account',
    'card_number_tip' => 'Your Credit/Debit Card number.',
    'skrill_email_tip' => 'Your registered Skrill email.',
    'neteller_account_tip' => 'Your registered Neteller account',
    'iban_tip' => 'The IBAN number consists of a two-letter country code, followed by two check digits, and up to thirty-five alphanumeric characters.',
    'btc_w_a_tip' => 'The Bitcoin address is alphanumeric and it is an identifier of 26-39 alphanumeric characters, beginning with the number 1, 3 or bc1.',
    'e_w_a_tip' => 'The Ethereum address is alphanumeric and it is an identifier of 42 alphanumeric characters, beginning with 0x.',
    'swift_code_tip' => 'A SWIFT code consists of 8 to 11 characters which identify the country, bank, and branch that an account is registered to).',
    'bank_name_tip' => 'Your bank name.',
    'beneficiary_name_tip' => 'Your name as it is registered with the bank.',
    'assign_date' => 'Assign date',
    'all_groups' => 'All Groups',
    'rewards' => 'Rewards',
    'terms_conditions' => 'Terms & Conditions',
    'eth_wallet_support_note' => 'I understand that INGOT Brokers receive ETH through the Ethereum network (ERC20) only. Payment with tokens, smart contracts,TRC20, or HRC20 is unsupported and will not be recovered. I also acknowledge that INGOT Brokers is not responsible to refund payments made through any such networks.',
    'has_credit' => 'Has Credit',
    'has_equity' => 'Has Equity',
    'country_auto_assign' => 'Auto Assign Rules',
    'registrable' => 'Registrable',
    'has_deposited' => 'Has Deposited',
    'include_comments' => 'Include comments',
    'never_logged_in_for_thirty_days' => 'Never Logged In For The Last 30 Days',
    'logged_into_their_account_in_thirty_days' => 'Logged in for the last 30 Days',
    'executed_by' => 'Executed By',
    'daily_balances' => 'Daily Balances',
    'transactions_summary' => 'Transactions Summary',
    'not_available' => '-',
    'opportunity_options' => 'Opportunity Options',
    'create_opportunity_options' => 'Create Opportunity Options',
    'edit_opportunity_options' => 'Edit Opportunity Options',
    'show_opportunity_options' => 'Show Opportunity Options',
    'opportunity_status' => 'Lead Status',
    'app_status' => 'App Status',
    'app_opportunity' => 'App. Status',
    'wallet_created_date' => 'Wallet Created Date',
    'app_created_date' => 'App Created Date',
    'open_agent_account' => 'Open an Agent Account',
    'open_corporate_account' => 'Open a Corporate Account',
    'regulation_entities' => 'Regulation Entities',
    'entities' => 'Regulation Entities',
    'regulation_entity' => 'Regulation Entity',
    'create_regulation_entity' => 'Create Regulation Entity',
    'show_regulation_entity' => 'Show Regulation Entity',
    'edit_regulation_entity' => 'Edit Regulation Entity',
    'verify_your_profile' => 'Verify Your Profile',
    'live_accounts' => 'Trading Accounts',
    'demo_accounts' => 'Demo Accounts',
    'promotion_sliders' => 'Dashboard Sliders',
    'fee' => 'Fee',
    'verification' => 'Verification',
    'under_review' => 'Under review',
    'banking_details_ingot' => 'banking details of ingot brokers',
    'identity_document' => 'Identity document',
    'id_doc_content_1' => 'The uploaded document should be:',
    'upload_front_doc' => 'upload the front of your document',
    'upload_back_doc' => 'upload the back of your document',
    'proof_doc_accept_content_1' => 'We accept the following documents:',
    'proof_doc_accept_content_2' => 'Bank statements.',
    'proof_doc_accept_content_3' => 'Utility bills.',
    'proof_doc_accept_content_6' => 'Rent Contract.',
    'proof_doc_accept_content_7' => 'Property registration document.',
    'upload_residence_doc' => 'Upload the proof of residence',
    'promotion_inner_title' => '$20,000 In Cash Prizes',
    'promotion_inner_desc_1' => 'Are you ready to join one of the biggest contests in the world?',
    'promotion_inner_desc_2' => 'Our Refer and Earn program is the simplest route to a profitable partnership. All you need to do is tell your network about our services by sending them a partner link or posting on popular social media channels. Tell your network: simply introduce INGOT Brokers to your network and earn commission for every referral that signs up and trades with us. Social networks: Promote INGOT to your contacts and followers on social media platforms or website and encourage them to start trading with us.',
    'are_you_ready_to_start' => 'are you ready to start?',
    'open_a_account_and_start_trading' => 'Open a account and start trading today',
    'withdrawals' => 'Withdrawals',
    'id_doc_content_2' => 'The image should be clear and readable.',
    'id_doc_content_3' => 'A government-issued document.',
    'id_doc_content_4' => 'The front and back of your document must be submitted (if applicable).',
    'id_doc_accepted_1' => 'Valid Passport',
    'id_doc_accepted_2' => 'Identity Card.',
    'id_doc_accepted_3' => 'Driver\'s License.',
    'proof_doc_not_accept_content_1' => 'As for the bank statement and the utility bill, the issuance date should not exceed three months.',
    'proof_doc_not_accept_content_2' => 'Proof of residence should include the name of the account holder. <span class="small"> (If the name of the account holder is not mentioned therein, please attach the “Family Register” to prove the type of relationship with the person whose name is mentioned on the resident proof).</span>',
    'change_leverage_confirm' => 'By submitting this Leverage Change request, I confirm that I have had access to, reviewed, read, understood, and accept the full set of INGOT Brokers legal documents including the <a class="dashed-border" target="__blank" href=":URL">Leverage Change Terms</a>.',
    'deposit_now' => 'Deposit Now',
    'deposit_execution_period' => 'Deposit Processing Time',
    'withdrawal_execution_period' => 'Withdrawal Processing Time',
    'processing_time' => 'Processing Time',
    'profile_review_tooltip' => 'Your application will be reviewed during our official working hours, from Monday to Friday',
    'transaction_id' => 'TX. ID',
    'reference_id' => 'Ref. ID',
    'deposit_withdraw_policy' => 'Based on our <a target="_blank" href=":URL">policy</a>, all compliance documentation, in addition to all other INGOT required documents, must have been received and approved by INGOT Brokers Compliance team in order to proceed with any withdrawal. Failure to comply with said submittal and approvals shall be wholly borne by the applicant/client without any ramifications whatsoever on INGOT Brokers.',
    'terms_and_conditions_request_card' => 'The <a target="_blank" href=":URL">Terms and Conditions</a>, you are about to sign, acknowledge and adhere to is with INGOT Broker',
    'in_progress_transaction' => 'Your transaction has been submitted successfully.',
    'in_progress_transaction_contact' => 'If you have any questions about your transaction, you can email us at :SUPPORT_EMAIL.',
    'trading_platform_numbering' => 'Entity Numbering Rules',
    'create_platform_numbering_rule' => 'Create Entity Numbering Rule',
    'edit_platform_numbering_rule' => 'Edit Entity Numbering Rule',
    'show_platform_numbering_rule' => 'Show Entity Numbering Rule',
    'minimum' => 'Minimum',
    'maximum' => 'Maximum',
    'allow_change_leverage' => 'Change Leverage',
    'transaction_number' => '{0,1} :count transaction|[2,*] :count transactions',
    'all_roles' => 'All Roles',
    'all_websites' => 'All Websites',
    'refer_and_earn_step_1' => 'Submit your request with one click',
    'refer_and_earn_step_2' => 'Use your unique IB link to register a client',
    'refer_and_earn_step_3' => 'Wait until your referred client trades',
    'refer_and_earn_step_4' => 'Earn your commission',
    'refer_and_earn_step_5' => 'Withdraw your money',
    'ingot_brokers_benefits' => 'ingot brokers benefits',
    'regular_commission_payouts' => 'Regular Commission Payouts',
    'dedicated_account_manager' => 'Dedicated Account Manager',
    'no_limit_on_the_commissions' => 'No limit on the commissions',
    'real_time_reporting' => 'Real-Time Reporting',
    'detailed_statistics' => 'Detailed Statistics',
    'ready_to_join_us' => 'Ready To Join Us?',
    'ready_to_join_us_description' => 'Simply click on the submit button and one of our Account Managers will contact you within 24 hours!',
    'accept_the_terms_and_conditions' => 'The <a target="_blank" href=":url">Terms and Conditions</a> you are about to sign, acknowledge, and adhere to are with :legal_entity',
    'become_a_partner_description_1' => 'Our Introducing Broker (IB) program is a great opportunity for individuals seeking ways to earn passive income. Under this scheme, IBs receive enticing commissions from INGOT Brokers on every client joining us when they introduce the brand to their network of connections.',
    'promotional_materials' => 'Free promotional materials upon request (banners, links, widgets, etc.)',
    'simple_client_onboarding' => 'Simple client onboarding',
    '24_6_round_support' => '24/5 support and dedicated account manager',
    'become_a_partner_detail_1' => 'To collect your commission, clients must open a trading account with INGOT Brokers through your unique INGOT Brokers IB link, which you can share on your social media accounts or any other channel you prefer. Once the clients referred by you fund their accounts and start trading, you will be receiving commissions as per your agreement with INGOT Brokers.',
    'become_a_partner_detail_2' => 'We designed our IB scheme to be a two-level partnership program to maximize your earnings. This means that if traders referred by you become partners with us and start introducing clients of their own, your income will increase!',
    'become_a_partner_detail_3' => 'Additionally, we will be providing different kinds of support to help you attract as many clients as possible. As such, we will assign you a dedicated account manager who will be available 24/5 for assistance. We will also provide you with varied promotional materials for your online advertising activities – upon your request.',
    'grow_your_profit' => 'Gain passive income with a few simple steps.',
    'join_ingot_brokers_partners' => 'Join our Family of Partners!',
    'show_edits_log' => 'Show User Edits Log',
    'how_it_works_affiliate_links' => 'It is Really Quite Simple!',
    'forward_groups' => 'Forward Groups',
    'old_group' => 'Old Group',
    'new_group' => 'New Group',
    'show_forward_group' => 'Show Forward Group',
    'create_forward_group' => 'Create Forward Groups',
    'edit_forward_group' => 'Edit Forward Groups',
    'show_uploaded_document' => 'Show Uploaded Document',
    'transactions_and_wallets_report' => 'E-Wallets Report',
    'open_position_report' => 'Open Positions Report',
    'count' => 'Count',
    'agent_number' => 'Agent Number',
    'educational_material_categories' => 'Educational Educational Videos Categories',
    'hide' => 'Hide',
    'unhide' => 'Unhide',
    'first_deposit_date' => 'First Deposit Date',
    'last_deposit_date' => 'Last Deposit Date',
    'last_withdrawal_date' => 'Last Withdrawal Date',
    'first_deposit_amount' => 'First Deposit Amount',
    'preview' => 'Preview',
    'fda' => 'FDA',
    'fdd' => 'FDD',
    'ldd' => 'LDD',
    'company_profile' => 'Company Profile',
    'similar_apps' => 'Similar Applications',
    'similar_apps_note' => 'Applications that shared a username with this user or logged-in to INGOT Portal from the same IP address.',
    'current_leverage' => 'Current leverage',
    'requested_leverage' => 'Requested leverage',
    'api_keys' => 'Api Keys',
    'api_key' => 'Api Key',
    'api_key_access_events' => 'Api Key Access Events',
    'your_account_number' => 'Your account number',
    'change_leverage_allowed' => 'change leverage allowed',
    'location' => 'location',
    'open_a_trading_account' => 'Open a Trading Account',
    'user_title' => 'Title',
    'abbreviation_name' => 'Abbreviation name',
    'full_name' => 'Full name',
    'id_number' => 'National ID',
    'back' => 'Back',
    'address_street' => 'Street & Number',
    'address_street_described' => 'Area, District, Street Name, Building Number, Floor, Flat Number, Nearby',
    'city_town' => 'Province / Territory',
    'state' => 'State',
    'source_of_income' => 'Source of income',
    'us_citizen' => 'U.S. Citizenship',
    'us_citizen_or_declarations' => 'I hereby certify that I am not a <span class="font-weight-bold"> U.S. citizen </span> nor qualified for U.S. citizenship',
    'upload_partnership_offer' => 'Upload the signed offer you received from the client',
    'first_reply_by' => 'First reply by username',
    'i_am_trading_on_behalf_of_someone' => 'No, I am trading on behalf of someone',
    'politically_person' => 'Politically exposed persons',
    'replies_count' => '{0,1} :count reply|[2,*] :count replies',
    'replies_number' => 'Replies',
    'last_reply_by' => 'Last reply by',
    'resolved_period' => 'Resolved period',
    'account_leverage' => 'Account Leverage',
    'default_leverage' => 'Default Leverage',
    'leverages' => 'Leverages',
    'leverages_list_note' => 'List of leverages as numbers (1,30,100, 200, 300), you can select multiple',
    'cashback' => 'Cashback',
    'cashback_group_note' => 'If you set this record to Yes, you are allowing to the clients that register under this group to get a cashback',
    'agent_bonuses' => 'Agent Bonuses',
    'agent_bonus' => 'Agent Bonus',
    'bonus_percentage' => 'Bonus Percentage',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'get_bonus_register' => 'And get :BONUS% Bonus on your Deposits',
    'get_started' => 'Get Started',
    'get_started_desc' => 'Complete these simple steps to get started',
    'fill_the_short_form' => 'Fill the short form',
    'status_active' => 'Active',
    'status_inactive' => 'Inactive',
    'outlook' => 'Outlook',
    'r1' => 'First level Resistance',
    'r2' => 'Second level Resistance',
    'r3' => 'Third level Resistance',
    's1' => 'First level Support',
    's2' => 'Second level Support',
    's3' => 'Third level Support',
    'technical_tools' => 'Technical tools',
    'timeframe' => 'Timeframe',
    'email_tags' => 'Email Tags',
    'email_key_tip' => 'By selecting a key, it will generate a default email comment for the chosen tag. If the key not found, you can choose another and type your custom comment.',
    'add_more' => 'Add more',
    'reg_confirm_1_jsc' => 'I acknowledge through my electronic approval of all the terms of <a href=":AGREEMENT_URL" class="dashed-border" target="_blank"> this agreement </a> and its attachments, and this should be considered as my actual signature on the Agreement and my acknowledgment of what is stated in it',
    'reg_confirm_2_jsc' => 'By ticking the box, I acknowledge that I have read and understood the <a href=":PRIVACY_URL" class="dashed-border" this agreement target="_blank">privacy policy</a> and will abide by its terms and conditions',
    'educational_articles' => 'Educational Articles',
    'experience_level' => 'Experience Level',
    'reding_time' => 'Reading time',
    'outcome' => 'outcome',
    'mins' => 'minutes',
    'trading_poa' => 'Managed account authorization',
    'web_trader' => 'Web Trader',
    'deposit_confirmation_policy' => 'INGOT does not process third-party payments. I confirm that my account name with INGOT matches the beneficiary’s name used with my payment method. INGOT will refund any third-party payments after deducting any refund fees that may apply.',
    'add' => 'Add New',
    'create_withdraw_request' => 'Do you want to create a new withdrawal request?',
    'bank_transfer_specs' => 'Bank Transfer Validation Rules',
    'routing' => 'Routing',
    'expire_time_info' => 'Please complete the below transaction before it expires',
    'expire_time_in' => 'Expire in',
    'min' => 'min',
    'sec' => 'sec',
    'bank_country' => 'Bank Country',
    'captured_deposit_another' => 'Do you want to add another deposit?',
    'please_add_financial_info' => 'Please add your financial information',
    'financial_information_added' => 'You have successfully added your financial information.',
    'financial_information_updates_rejected' => 'The financial information changes has been rejected.',
    'common' => 'common',
    'offer_section' => 'offer Section',
    'offer_sections' => 'offer Sections',
    'offer_product' => 'Offer Product',
    'offer_products' => 'Offer Products',
    'offer_terms' => 'Offer Terms',
    'custom' => 'Custom',
    'default' => 'Default',
    'sections' => 'Sections',
    'products' => 'Products',
    'offer_account_type' => 'Offer Account Type',
    'offer_product_section_default_details' => 'Offer Product Section Default Details',
    'offer_product_section_default_detail' => 'Offer Product Section Default Detail',
    'section_details' => 'Section details',
    'create_new_default_product_section' => 'Create New section details',
    'proceed' => 'Proceed',
    'reject' => 'Reject',
    'requests' => 'Requests',
    'new_instrument' => 'New instrument',
    'Request New Instrument' => 'Request New Instrument',
    'product_category' => 'Product Category',
    'offer_for' => 'Offer for',
    'bulk_assign' => 'Bulk App. Assign',
    'offer_details' => 'Offer details',
    'Request New Report' => 'Request New Report',
    'new_report' => 'New Report',
    'Request Open PAMM/MAM Account' => 'Request Open PAMM/MAM Account',
    'open_pamm_account' => 'New PAMM/MAM Account',
    'login_number' => 'Agent Login Number',
    'symbols' => 'Symbols',
    'managed_roles' => 'Managed roles',
    'performance_fees' => 'Performance Fees',
    'commission_fees' => 'Commission Fees',
    'frequency' => 'Frequency',
    'every_three_months' => 'Every 3 months',
    'every_six_months' => 'Every 6 months',
    'every_twelve_months' => 'Every 12 months',
    'certificate' => 'Certificate',
    'month_number' => '{0,1} :count month|[2,*] :count months',
    'request_change_leverage' => 'Request Change Leverage',
    'multi_terminal' => 'Multi Terminal',
    'mam_attach' => 'PAMM/MAM attach',
    'mam_detach' => 'PAMM/MAM detach',
    'Request MAM Attach' => 'Request PAMM/MAM Attach',
    'Request MAM Detach' => 'Request PAMM/MAM Detach',
    'mam_login' => 'PAMM/MAM Account',
    'genders' => 'Genders',
    'order_types' => 'Order Types',
    'order_type' => 'Order',
    'create_order_type' => 'Create order type',
    'cancelable' => 'Cancelable',
    'api_key_access_event' => 'Api Key Access Event',
    'user_titles' => 'User titles',
    'marital_statuses' => 'Marital Statuses',
    'marital_status' => 'Marital Status',
    'business_sectors' => 'Business Sectors',
    'professionalism' => 'Professionalism',
    'professionalisms' => 'Professionalisms',
    'risk_rate' => 'Risk Rate',
    'required' => 'Required',
    'question_categories' => 'Question Categories',
    'question_category' => 'Question Category',
    'male' => 'Male',
    'female' => 'Female',
    'personal_information_new' => 'Personal Information',
    'personal_phone_number' => 'Phone Number',
    'place_of_residence' => 'Place Of Residence',
    'place_of_birth' => 'Place of birth',
    'current_address' => 'Current Residence Address',
    'national_id_number' => 'National ID number',
    'passport_number' => 'Passport number',
    'residency_number' => 'Residency number',
    'place_of_issue' => 'Place of issue',
    'date_of_issue' => 'Date of issue',
    'expiry_date' => 'Expiry date',
    'employment_information' => 'Employment Information',
    'politically_exposed_people' => 'Politically exposed people',
    'educational_major' => 'Educational Major',
    'questions' => 'Questions',
    'content' => 'Content',
    'answers' => 'Answers',
    'has_not_deposited' => 'Has Not Deposited',
    'has_withdrawal' => 'Has Withdrawn',
    'has_not_withdrawal' => 'Has Not Withdrawn',
    'has_demo' => 'Has a Demo Account',
    'has_not_demo' => 'Does Not Have a Demo Account',
    'has_not_real' => 'Does Not Have a Trading Account',
    'has_mam_pamm' => 'Has PAMM/MAM Account',
    'has_traded' => 'Has Traded',
    'has_not_traded' => 'Has Never Traded',
    'restricted_ip_addresses' => 'Restricted IPs',
    'restricted_ip_address' => 'Restricted IP',
    'other_nationalities' => 'Do you have other nationalities?',
    'real_trader' => 'Real Beneficiary',
    'real_trader_agreement_1' => 'I declare that I am the real beneficiary of the account and that I have read and understood all the terms of the Financial Brokerage Agreement for the Account of Others in Foreign Stock Exchanges and its annexes.',
    'real_trader_agreement_2' => 'I also hereby certify that all the information mentioned here is true and accurate, and I hereby relieve INGOT Financial Brokerage Ltd. (hereinafter referred to as “the Company”) of any responsibility and/or liability whatsoever that might occur or result from the inaccuracy and/or incorrectness of such information.',
    'real_trader_agreement_3' => 'I hereby accept any decision by the Company in the event of it finding out that any of the information mentioned in this disclaimer is incorrect, untrue and/or inaccurate, including closing my account at INGOT Financial Brokerage Ltd. without any liability whatsoever towards the Company.',
    'purpose_of_investment' => 'Purpose of investment',
    'current_professional' => 'Current professional status',
    'academic_qualification' => 'Academic Qualification',
    'occupation' => 'Occupation',
    'business_sector' => 'Business Sector',
    'job_title' => 'Job Title',
    'business_nature' => 'Business nature',
    'business_address' => 'Business address',
    'institution_name' => 'Organization name',
    'text' => 'Text',
    'is_required' => 'Is Required',
    'job_positions' => 'Job Positions',
    'job_position' => 'Job Position',
    'client_segment' => 'client Segment',
    'sort_by' => 'Sort By',
    'sort_direction' => 'Sort Direction',
    'continue_with_microsoft' => 'Continue With Microsoft',
    'company_types' => 'Company Types',
    'add_m_t_account' => 'Add MT Account',
    'mark_as_solved' => 'Mark as solved',
    'start_investigation' => 'Start Investigation',
    'set_reminder' => 'Contact Reminder',
    'enter_your_txhash_here' => 'Enter your TxHash here',
    'how_was_the_help' => 'How was the help you received?',
    'tell_us_about_your_exp' => 'Tell us about your experience',
    'good' => 'Good',
    'neutral' => 'Neutral',
    'poor' => 'Poor',
    'rated_successfully' => 'Thanks for your feedback.',
    'notify_me' => 'Send me the MetaTrader account credentials',
    'opportunities' => 'Opportunities',
    'max_active_tickets' => 'You have reached your limit of :TICKET_COUNT opened tickets under review. Our team is currently investigating and will get back to you as soon as possible.',
    'reminders' => 'Reminders',
    'reminders_number' => '{0,1} :count reminder|[2,*] :count reminders',
    'agent_accounts' => 'Agent Accounts',
    'individual_accounts' => 'Individual Accounts',
    'corporate_accounts' => 'Corporate Accounts',
    'mam_request_terms_content_1' => 'By clicking on the request bottom below, I hereby agree to the following terms and conditions unconditionally and without liability on INGOT Broker LLC. The said account and the clients under this account remain my full and sole responsibility without liability on the company.',
    'mam_request_terms_content_2' => 'The terms and conditions are as follows:',
    'mam_request_terms_content_3' => 'I must have a minimum of 3 active clients under my IB before I submit this request.',
    'mam_request_terms_content_4' => 'I agree and hereby acknowledge that if the number of active clients under my said account falls below 2, I will receive a notification from INGOT Broker LLC and if within two weeks I am still unable to increase the number of active clients to 3. INGOT Broker LLC will disable my said account and close out any open positions.',
    'mam_request_terms_content_5' => 'MT4 is the only available terminal for MAM and PAMM.',
    'mam_request_terms_content_6' => 'The only IB offer that can be used to open a PAMM/MAM account is the "IB Pro Offer".',
    'mam_request_terms_content_7' => 'I hereby acknowledge and agree that trading Micro E-mini contracts, big differences of client’s equities (more than 10 times) and hedging on the PAMM/MAM account may result in different distribution ratios between accounts, and it may cause misdistribution of the contracts among some accounts. I also hereby acknowledge and agree that any negative consequences due to these activities are my sole responsibility and INGOT Broker LLC shall not be liable for any such consequences.',
    'mam_request_terms_content_8' => 'Any client may at their sole discretion send request to the Admin to attach and/or detach from the PAMM/MAM account. When initiated by the client, detachment is preceded by a grace period of 24 hours for amicable discussions with the client.  If no agreement was reached, the detach order will go to the Admin, who will execute it.',
    'mam_request_terms_content_9' => 'I hereby acknowledge that I am not able to detach client from the PAMM/MAM account unless after closing the positions of that client. The detach request will be definitive.',
    'client_request_attach_mam_terms' => 'By clicking this box, I hereby acknowledge that I have reviewed, read, understood, and accept the terms and conditions of the Managed Account Authorization and Profit transfer.',
    'client_request_detach_mam_terms' => 'I understand that the MM has a grace period of 24 hours to contact me to discuss my detach request. If we reach an agreement within this grace period, I will be able to cancel my detach request through the portal; doing so is my sole responsibility. I acknowledge that after the grace period, I will not be able to cancel my detach request, it will be final, and all my open positions will be automatically closed out and the detach will be definitive.',
    'closed_position_report' => 'Closed Positions Report',
    'company_documents' => 'Company Documents',
    'id_card_1_doc_note' => 'The front side of your identity document',
    'id_card_2_doc_note' => 'The back side of your identity document',
    'utility_bill_doc' => 'Proof Of Address',
    'utility_bill_doc_note' => 'E.g., bank statement, utility bill, lease agreement, etc. Must not be older than 6 months',
    'ticket_rate' => 'Ticket Rate',
    'not_rated' => 'Not rated',
    'excellent' => 'Excellent',
    'shareholders_and_directors_details' => 'Shareholders and Directors Details',
    'shareholders_and_directors' => 'Shareholders and Directors',
    'are_you_user_as_holder' => 'Are you one of the shareholders?',
    'user_as_holder' => 'Yes, i\'m  one of the shareholders',
    'the_user_as_holder' => 'The user is one of the shareholders',
    'main_contact_documents' => 'Main Contact Documents',
    '8_char_min' => 'minimum 8 characters',
    'shareholder_documents' => 'Holder Documents',
    'certificate_of_incorporation' => 'Certificate of Incorporation/Registration',
    'certificate_of_incorporation_doc_note' => 'A Certificate of Incorporation is a legal document/license relating to the formation of a company or corporation. In addition to the basic details of the company, please ensure the document you upload includes information about the directors and any shareholders holding 25% or more of the company\'s shares.',
    'company_operational_document' => 'Company Operational Document',
    'company_operational_document_doc_note' => 'A document that specifies the regulations for a company\'s operations and defines the company\'s purpose. We accept any of the following documents: Certificate of Incumbency, Articles of Association, Company Extract Memorandum',
    'company_proof_address' => 'Company’s Proof of Address or Certificate of Good Standing',
    'company_proof_address_doc_note' => 'A document that proofs the status of the company. It must be current and not outdated by more than 6 months',
    'company_authorization' => 'Authorization',
    'company_authorization_doc_note' => 'Corporates with more than one shareholder or director are required to provide a letter signed by all Shareholders/Directors that states their approval to the opening of the account and to authorize one of the directors or a representative in dealing with INGOT Brokers.',
    'ticket_report' => 'User Tickets Report',
    'total_tickets' => 'Total Tickets',
    'open_tickets' => 'Open Ticket',
    'closed_tickets' => 'Closed Ticket',
    'ticket_subject' => 'Ticket Subject',
    'portal_login_report' => 'Portal Logins Report',
    'total_logins' => 'Total logins',
    'last_login' => 'Last Login Date',
    'most_login_first' => 'Most login first',
    'least_login_first' => 'Least login first',
    'deposit_and_withdrawal_report' => 'Deposit & Withdrawals Report',
    'support_agent' => 'Support Agent',
    'bank_account_number' => 'Your bank account number',
    'bank_account_name' => 'Name on your bank account',
    'payment_method_report' => 'Payment Method Report',
    'payment_method' => 'Payment Method',
    'ingot_popup_modal_currencies_message' => 'Please be aware that exchange rates will be applied',
    'change_currency_msg' => 'You\'re changing your wallet currency to <span class="curency_code font-weight-bold"></span>; this will affect all your historical transactions',
    'change_wallet_currency' => 'Change Wallet Currency',
    'never_signin' => 'Never Sign-in',
    'active_on' => 'Active On',
    'have_most_tickets' => 'Most Tickets First',
    'have_least_tickets' => 'Least Tickets First',
    'not_closed' => 'Not Closed',
    'business_facility_name' => 'Organization name',
    'previous_former_labor_sector' => 'Previous Business Sector',
    'previous_job_title' => 'Previous Job Title',
    'previous_business_nature' => 'Previous Business nature',
    'previous_business_address' => 'Previous Business address',
    'previous_business_province_id' => 'Previous Business Province',
    'previous_business_city' => 'Previous Business City',
    'previous_business_facility_name' => 'Previous Organization name',
    'previous_business_country' => 'Previous Business Country',
    'business_province_placeholder' => 'Business Province',
    'business_city_placeholder' => 'Business City',
    'specialization' => 'Specialization',
    'withdrawal_request' => 'Withdrawal Request',
    'open_sub_account' => 'Open Sub Account',
    'call' => 'Call',
    'services' => 'Services',
    'dropdown' => 'DropDown',
    'radio' => 'Multiple choice',
    'checkbox' => 'Checkbox',
    'eligibility' => 'Eligibility',
    'kyc' => 'Kyc',
    'contract_size' => 'Contract Size',
    'pending_requests' => 'Pending Requests',
    'data_management' => 'Data Management',
    'errors_log' => 'Errors & Exceptions',
    'auditing' => 'Auditing & Logging',
    'transaction_internal_system' => 'Internal Transaction',
    'users_history' => 'Users History',
    'commission_offers' => 'Commission Offers',
    'user_management' => 'Users Management',
    'contacts_and_leads' => 'Contacts & Leads',
    'graphs_diagrams' => 'Graphs & Diagrams',
    'analytics_reports' => 'Analytics & Reports',
    'administration' => 'Administration',
    'queue_logs' => 'queue jobs',
    'campaign_templates' => 'Campaign templates',
    'instruments_and_contracts' => 'Instruments & Contracts',
    'educational_materials' => 'Educational Videos',
    'websites_banking_details' => 'Site Financial Details',
    'contact_request_categories' => 'Contact Request Categories',
    'institutional_services' => 'Institutional Services',
    'source_of_funds_questions' => 'Source Of Funds Questions',
    'eligibility_questions' => 'Eligibility Questions',
    'eligibility_answers' => 'Eligibility Answers',
    'pending_ib_offers' => 'Pending IB offers',
    'my_contacts' => 'My Contacts',
    'index_dividends_reports' => 'Index Dividends Reports',
    'expired_account_report' => 'Expired Account Report',
    'has_managed_roles' => 'Has Managed Roles',
    'cron_job_logs' => 'CronJobs',
    'whitelabels' => 'Whitelabels',
    'ib_offer_scenario' => 'IB Offers',
    'mt_pending_orders' => 'MetaTrader Pending Orders',
    'has_assigned_childrens' => 'Has assigned applications',
    'has_authorized_regions' => 'Has authorized regions',
    'popular_instruments' => 'Popular Instruments',
    'no_results_available' => 'No results available',
    'accounts_limit' => 'Accounts Limit',
    'master_password' => 'Master Password',
    'investor_password' => 'Investor (Read-Only)',
    'arabic_name' => 'Full Name (Arabic)',
    'english_name' => 'Full Name (English)',
    'on_hold' => 'On hold',
    'married' => 'Married',
    'divorced' => 'Divorced',
    'single' => 'Single',
    'widowed' => 'Widowed',
    'lessons' => 'Lessons',
    'beginner' => 'Beginner',
    'intermediate' => 'Intermediate',
    'expert' => 'Expert',
    'islam' => 'Islam',
    'christian' => 'Christian',
    'pre-approval' => 'Pre Approval',
    'incomplete' => 'Incomplete',
    'waiting_client_feedback' => 'Waiting client feedback',
    'ticket_count' => '{0,1} :count ticket|[2,*] :count tickets',
    'last_ticket_date' => 'Last Ticket Date',
    'websites_config' => 'Aff. Websites Config',
    'create_trading_account' => 'Create Trading Account',
    'create_demo_account' => 'Create Demo Account',
    'campaigns' => 'Campaigns',
    'holder_at_least_1' => 'You should add at least one shareholder',
    'ticket_tags' => 'Ticket Tags',
    'ticket_tag' => 'Ticket Tag',
    'referral_report' => 'Referral report',
    'total_referrals' => 'Total Referrals',
    'most_referral_first' => 'Most referral first',
    'least_referral_first' => 'Least referral first',
    'personal_image' => 'Personal Image',
    'remove' => 'Remove',
    're_open_ticket' => 'Re-Open The Ticket',
    're_opended' => 'Re-Opened',
    'in_active' => 'Inactive',
    're_opend_count' => '{0,1} :count Time|[2,*] :count Times',
    'profile_documents' => 'Profile Documents',
    'webinars' => 'Webinars',
    'webinar' => 'Webinar',
    'influencer' => 'Speaker',
    'employee_name' => 'Organizer Name',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'start_from' => 'Start From',
    'start_to' => 'Start To',
    'speaker_brief' => 'Speaker Brief',
    'organizers' => 'Organizers',
    'agenda' => 'Agenda',
    'new_actual_attendees' => 'Register New User',
    'actual_attendees' => 'Webinar Registrations',
    'limit' => 'Limit Attendees Number',
    'registered_users' => 'Registered Users',
    'webinar_registered_failed' => 'The webinar has reached the max number of attendees',
    'webinar_registered_successfully' => 'You\'r now a member of the webinar, check your email for more info',
    'webinar_feedback' => 'Write your feedback',
    'register_date' => 'Registration Date',
    'ask_for_webinar_feedback' => 'How was your experience in the webinar?',
    'most_registered_users' => 'Most registered users',
    'least_registered_users' => 'Least registered users',
    'finished' => 'Finished',
    'unique_views' => 'Unique Views',
    'total_views' => 'Total Views',
    'file_name' => 'File Name',
    'template' => 'Template',
    'campaign' => 'Campaign',
    'marketing' => 'Marketing',
    'start_at' => 'Start at',
    'end_at' => 'End at',
    'referral_hash' => 'Referral Hash',
    'agent_hash' => 'Agent Hash',
    'with_cryptocurrencies' => 'With Cryptocurrencies?',
    'retention' => 'Retention',
    'acquisition' => 'Acquisition',
    'save_sort' => 'Save Sort',
    'tickets_chart_by_reg_date' => 'Cumulative User Tickets',
    'tickets_chart_by_category' => 'User Tickets By Category',
    'tickets_chart_by_rates' => 'User Tickets Rates',
    'email_notifications' => 'Email Notifications',
    'user_margin_call' => 'Notify accounts margin call',
    'risk_10k_deposit' => 'Risk with above 10K deposits',
    'campaign_report' => 'Campaign Report',
    'document_uploaded' => 'The document has been uploaded',
    'users_count' => '{0,1} :count user|[2,*] :count users',
    'webinar_is_end' => 'This webinar has expired',
    'join' => 'Join',
    'booked_spots' => 'Booked Spots',
    'of' => 'of',
    'received_amount_exchanged_note' => 'This field is the approved amount you will transfer to the client after the exchange',
    'received_amount_exchange' => 'Received Amount (exchanged)',
    'client_wallet' => 'Client\'s E-Wallet',
    'my_agent_wallet' => 'My Agent\'s E-Wallet',
    'tutorial_videos' => 'Tutorial Videos',
    'platform_tutorials' => 'platform tutorial',
    'need_help_navigating_our' => 'Need help navigating?',
    'click_on_the_play_button' => 'Click on the play button',
    'for_our_tutorials' => 'For Our Tutorials!',
    'business_country' => 'Business Country',
    'business_city' => 'Business City',
    'duplicated' => 'Duplicated',
    'with_verified' => 'With resolved',
    'application_verified_successfully' => 'The application marked as verified.',
    'application_duplicated_successfully' => 'The application marked as duplicated.',
    'duplicate' => 'Duplicated',
    'suspended' => 'Suspended',
    'suspend_account' => 'Suspend Account',
    'account_suspended' => 'This account is suspended.',
    'account_orders' => 'Account Orders',
    'are_you_politically' => 'Are you a politically exposed person?',
    'what_contact_method' => 'What is the preferred real-time contact method?',
    'phone_call' => 'Phone Call',
    'text_messages' => 'Text messages',
    'contact_when_have_question' => 'I will contact you when I have a question',
    'contact_agreement' => 'Please note that by checking the first  and/or the second checkbox(es) above,  you give us your consent to contact you in a real-time method. This consent will be renewed via email. You can unsubscribe from the email at any time.',
    'cumulative_user_requests' => 'Cumulative User Requests',
    'user_request_by_language' => 'User Requests by Language',
    'user_request_by_instrument' => 'User Requests by Instrument',
    'contact_method' => 'Contact Method',
    'real_account_created_successfully_without_deposit' => '<p>You’re only one step away from trading!</p> <p>Now, you can browse your MetaTrader platform, and you will be able to deposit money into your account once we review and approve your application.</p>',
    'real_beneficiary_name' => 'Real Beneficiary Name',
    'generate' => 'Generate',
    'rebates_and_commission' => 'Rebates & Commission',
    'withdrawal_icon' => 'Withdrawal Icon',
    'deposit_icon' => 'Deposit Icon',
    'suspend' => 'Suspend',
    'activate' => 'Activate',
    'application_suspended_successfully' => 'The application has been suspended successfully',
    'application_activated_successfully' => 'The application has been activated successfully',
    'viewed_at' => 'Viewed At',
    'viewers' => 'Viewers',
    'show_in_ib' => 'Show in IB Offer',
    'political_position' => 'Relationship, Facility name and political position',
    'additional_info' => 'Additional info',
    'rebate_rate' => 'Rebate Rate',
    'rebate' => 'Rebate',
    'classification' => 'Classification',
    'site_tutorial_videos' => 'Need help registering? Watch our tutorial!',
    'execution_time' => 'Execution Time',
    'other_documents' => 'Other Documents',
    'open_in_portal' => 'Open in portal',
    'seconds' => 'Seconds',
    'we_will_open_account_in_40_1' => 'We guarantee to have your account set and ready within 40 minutes or less. Otherwise, we will compensate you with $300!',
    'we_will_open_account_in_40_2' => 'Terms and conditions apply.',
    'message_in_case_time_out' => 'We keep our promises! Please note that the $300 compensation has been accredited to your MetaTrader account.',
    'min_for_guarantee_banner_1' => 'We will Open Your <br> Account in 40 Minutes',
    'min_for_guarantee_banner_2' => 'If we are late, we will give you $300!',
    'agent_name' => 'Agent Name',
    'agent_comm_profile' => 'Agent Commission Profile',
    'usdt_deposit_note' => 'I understand that INGOT Brokers only accepts Tether (USDT) through the ERC20 Main Network and does not accept USDT over any other sub or private networks, such as but not limited to, BEB20, BEB2, and BSC. I also understand that any funds sent through such unsupported networks will not be accepted and INGOT Brokers will not be held responsible for extracting or refunding these funds.',
    'has_wallet_balance' => 'Has E-Wallet Balance',
    'has_not_wallet_balance' => 'Has No E-Wallet Balance',
    'a_great_deal' => 'A great deal, isn\'t it?',
    'general_terms_and_conditions' => 'General Terms and Conditions',
    'terms_and_conditions_1' => 'This is to acknowledge receipt of customer’s application to open an account with INGOT for the services specified on the website (the “Application”).',
    'terms_and_conditions_2' => 'These Terms and Conditions (“Terms and Conditions”) constitute a part of the Application.  INGOT shall review and process the Application and revert to the client with the decision to accept or require additional information within 40 minutes from submitting and completing the Application (“Processing Time”) during the working server hours, Monday to Friday (the “Window”). INGOT may also reject the Application during that time; if the Application is rejected, the client shall not be entitled to the USD 300. Notification of decision will be sent via email from INGOT.',
    'terms_and_conditions_3' => 'If INGOT requires additional information, the Application will be reviewed again within 40 minutes from submitting all required information and accepting the Terms and Conditions.  If the Application is accepted at any time after the Processing Time during the Window, INGOT shall compensate the client USD 300.',
    'terms_and_conditions_4' => 'The compensation of USD 300 will not be reimbursed in cash and will be reserved in the client’s account to be allocated in the Client’s Trading Account and/or Portal E-Wallet.  The compensation above is the sole and only amount payable by INGOT if the customer does not receive INGOT’s decision on the Application within the Processing Time during the Window. Any delay in responding to the customer outside the Window stipulated above will not entitle the customer to any compensation.',
    'terms_and_conditions_5' => 'At all times, INGOT  will have the right to make changes to these Terms and Conditions, including changes to the specifications, Window, Processing Time, compensation, or any provision as INGOT deems necessary without notifying the client.',
    'terms_and_conditions_6' => 'The client shall indemnify and hold INGOT and its affiliates harmless and shall defend each of them from and against any or all third party claims, demands, litigation, or proceedings of whatever kind, whether based upon negligence, breach of express or implied warranty, strict liability, infringement of intellectual property rights, or any other theory, and from and against all direct, indirect, special, exemplary, incidental or consequential damages of every kind whatsoever, arising out of, by reason of, or in any way connected with the services by INGOT.',
    'terms_and_conditions_7' => 'It is agreed that INGOT’s aggregate liability arising from or resulting to these Terms and Conditions is limited to the amount of USD 300. To the maximum extent allowable under the applicable law, INGOT shall not be liable for any special, incidental, consequential, indirect damages. The client may not assign the Application or any interest in it or any payment due or to become due under it without the written consent of INGOT.',
    'terms_and_conditions_8' => 'All notices, consents, waivers and other communications required or permitted to be given pursuant to these Terms and Conditions shall be in writing and shall be deemed to have been delivered on the delivery date by email and/or notifications on the website and/or portal. If any provision of these Terms and Conditions shall be held or deemed to be or shall, in fact, be illegal, inoperative or unenforceable, this provision shall not affect any other provision or provisions contained in this thereunder.  These Terms and Conditions, and all related transactions, will be interpreted under and governed by the laws of the country of INGOT’s registration. Disputes arising under these Terms and Conditions will be resolved by the parties through good faith negotiations in the ordinary course of business. Any dispute not so resolved will be submitted for litigation before the civil court of the capital of the country of registration.',
    'additional_terms' => 'Additional Terms:',
    'additional_terms_1' => 'The client shall be at least 18 years old and in full mental capacity.',
    'additional_terms_2' => 'Even if the client is eligible to receive the USD 300, but their account would still be rejected for reasons attributable to AML checks, the USD 300 will not be credited into their account. Only the amount the client deposited will be returned to them (as per the policy of returning the amounts).',
    'additional_terms_3' => 'Employees will not be penalized for late service.',
    'additional_terms_4' => 'INGOT reserves the right to withdraw the service guarantee without prior notification.',
    'additional_terms_5' => 'The service guarantee shall not be applicable on weekends (Saturday and Sunday) and official holidays. The service guarantee may be withdrawn temporarily in view of difficult operating conditions for providing the service, such as servers/website/platform being unavailable. Stipulations of INGOT Legal Documents apply.',
    'additional_terms_6' => 'To be eligible, all documents must meet the requirements of the authorities. If any document is found defective, the USD 300 will be forfeited.',
    'additional_terms_7' => 'This case is only available on the platform of your country of registration. It cannot be transferred to any other user.',
    'additional_terms_8' => 'If you do not open any trading positions using this amount within one month, you forfeit it.',
    'additional_terms_9' => 'INGOT reserves the right to refuse to apply and/or allow you to redeem the USD 300 if you attempt to manipulate or abuse our service guarantee, for example, by registering multiple accounts or where the delay was due to your own actions (change in uploaded documents).',
    'additional_terms_10' => 'INGOT is not responsible for any failure due to incorrect email details, email restrictions or any wrong information pertaining to your account.',
    'additional_terms_11' => 'Website usage terms apply.',
    'additional_terms_12' => 'INGOT has the right to refuse the service if multiple accounts are received from the same IP address. Individuals shall not be allowed to open accounts in the names of their family members.',
    'additional_terms_13' => 'The English version of these Terms and Conditions shall prevail in case of discrepancy.',
    '40_min_for_guarantee' => '40 Min Guarantee',
    'id_card_number' => 'ID Card Number',
    'id_passport_number' => 'ID Passport Number',
    'agent_profile_name' => 'Agent Profile Name',
    'market_analysis' => 'Market Analysis',
    'deleted_at' => 'Deleted Date',
    'complete_the_app_info_note' => 'In order to view this data, kindly complete the user application information!',
    'missing_info' => 'missing information',
    'is_missing' => ':key missing',
    'application_status_tags' => 'Application Status Tags',
    'create_application_status_tag' => 'Create Application Status Tag',
    'show_application_status_tag' => 'Show Application Status Tag',
    'edit_application_status_tag' => 'Edit Application Status Tag',
    'archive_reason' => 'Archive Reason',
    'archive_retainable' => 'Archive Retability',
    'archived_tags' => 'Non Retainable Tag',
    'archive_application' => 'Archive Application',
    'archived_successfully' => 'Application Archived Successfully',
    'disable_reason' => 'Disable Reason',
    'disable_application' => 'Disable Application',
    'disabled_successfully' => 'Application Disabled Successfully',
    'expired_accounts' => 'Expired Accounts',
    'expired_at' => 'Expired At',
    'i_am_politically_exposed_person' => 'No, I am a politically exposed person',
    'copy_trading' => 'Copy Trading',
    'gainers_and_losers_report' => 'Gainers & Losers Report',
    'number_of_tickets' => '{0,1} :count ticket|[2,*] :count tickets',
    'number_of_calls' => '{0,1} :count call|[2,*] :count calls',
    'username' => 'Username',
    'profit_lot' => 'P\L',
    'profit_loser_desc' => 'P/L Descending',
    'profit_loser_asc' => 'P/L Ascending',
    'other' => 'Other',
    'last_view' => 'Last view by',
    'not_viewed_yet' => 'Not viewed yet!',
    'provinces' => 'Provinces',
    'app_details' => 'App. Summary',
    'never_logged_in' => 'Never Logged in',
    'time_has_expired' => 'time has expired',
    'not_politically_person' => 'Not politically exposed persons',
    'has_wallet' => 'Has wallet',
    'cities' => 'Cities',
    'app_id' => 'App. ID',
    'business_province_id' => 'Business Province',
    'registered_apps' => 'Registered Applications',
    'registered_mt_accounts' => 'Registered Accounts',
    'deposits_count' => 'Number Of Deposits',
    'withdrawals_count' => 'Number Of Withdrawals',
    'num_deposits' => '[0,1] :count deposit|[2,*] :count deposits',
    'num_withdrawals' => '[0,1] :count withdrawal|[2,*] :count withdrawals',
    'allow_leverage_on_reg' => 'Allow select Leverage on Reg.',
    'paypal_email' => 'PayPal Email',
    'mt_account_info' => 'MetaTrader account',
    'position_volume' => 'Position volume',
    'last_opened_position_date' => 'Last open position date',
    'deduct_amount_wallet' => 'Deduct the amount from the E-Wallet/MetaTrader account',
    'source_of_the_funds' => 'source of the funds',
    'client_summary' => 'Clients summary',
    'current_time' => 'Current Time',
    'how_does_our_ib_program_work' => 'How Does our IB Program Work?',
    'submit_your_request_with_one_click' => 'Submit your request with one click',
    'use_your_unique_ib_link_to_register_a_client' => 'Use your unique IB link to register a client',
    'wait_until_your_referred_client_trades' => 'Wait until your referred client trades',
    'earn_your_commission' => 'Earn your commission',
    'withdraw_your_money' => 'Withdraw your money',
    'become_a_professional_trader' => 'Become a professional trader with INGOT Brokers',
    'become_a_professional_trader_disc' => 'Change your account type into Wholesale or Sophisticated to enjoy advanced trading conditions! Check your eligibility',
    'wholesale_1' => 'Wholesale',
    'wholesale_desc' => 'To change your account type into Wholesale, you are required to download, fill, and upload the documents below:',
    'additionally_you_must_assets' => 'Additionally, you must have net assets of at least AUD 2.5 million or a gross income amounting to AUD 250,000 for each of the last two financial years.',
    'sophisticated_1' => 'Sophisticated',
    'to_change_your_account_type_sophisticated' => 'To change your account type into Sophisticated, you are required to download, fill, and upload the documents below:',
    'additionally_you_must_meet_following_1' => 'Additionally, you must meet the following two conditions:',
    'having_the_required_knowledge' => '<span class="font-weight-bold">1.</span> Having the required knowledge and expertise, as well as an annual income of at least AUD 100,000 or assets worth AUD 500,000.',
    'have_previously_traded_leveraged' => '<span class="font-weight-bold">2.</span> Have previously traded leveraged Forex or CFDs 20 times per quarter for any four quarters in the past two years, with a notional value of at least AUD 1,000,000.',
    'if_you_do_not_possess_the_above_two_requirements' => 'If you do not possess the above two requirements, then you must confirm that you assume or have assumed a professional position in the financial sector for at least one year, with proven knowledge of leveraged trading, Forex, and CFDs.',
    'according_to_asic_qualified_accountant' => 'Also known as Certificate issued for Sections 761G(7)(c) of Chapter 7 of the Corporations Act 2001: According to ASIC, the qualified accountant must be a member of one of the eligible professional bodies listed on ASIC’s website; and must be a resident in the same country as the Wholesale client applicant.',
    'retention_kpi' => 'Retention KPI',
    'growth_rate' => 'Growth Rate',
    'inactive_to_active' => 'Inactive To Active',
    'retention_rate' => 'Retention Rate',
    'client_type' => 'Client Type',
    'open_real_jo_confirm' => 'I hereby confirm and accept the mentioned Specifications of this Account Type.',
    'change_leverage_jo_confirm' => 'By ticking this box, I hereby confirm that I have had access to, reviewed, read, understood, and accept the full set of INGOT Financial brokerage Ltd. legal documents including the <a class="dashed-border" target="__blank" href=":URL">Leverage Terms</a>.',
    'change_leverage_jo_confirm2' => 'By ticking this box, I hereby accept the aforementioned Leverage and this <a class="dashed-border" target="__blank" href=":URL">Acknowledgement</a>. I also hereby acknowledge that I am fully aware with the high risks that are associated with the financial Leverage.',
    'total_reg_apps' => 'Total Reg. Apps',
    'channel' => 'Channel',
    'application_exists' => 'Application with this email is already exists',
    'scalping_user_email' => 'An email has already been sent!',
    'scalping_details' => 'Scalping Details',
    'inactive_note' => 'An inactive client is a client who does to make a deal transaction after a period of time',
    'company_trade_name' => 'Company Trade Name',
    'company_sector' => 'Company Sector',
    'company_national_number' => 'Company National Number',
    'is_company_parent' => 'Is the company a foreign subsidiary or is it the parent company?',
    'is_parent' => 'Parent Company',
    'is_company' => 'Is Company',
    'is_subsidiary' => 'Foreign Subsidiary',
    'registration_number' => 'Registration Number',
    'country_of_origin' => 'Country Of Origin',
    'registration_authority' => 'Registration Authority',
    'parent_registration_number' => 'Parent Registration Number',
    'parent_registration_date' => 'Parent Registration Date',
    'operating_subsidiary' => 'Operating Subsidiary',
    'non_operating_subsidiary' => 'Non Operating Subsidiary',
    'no_american_nationalities' => 'Unfortunately, you cannot proceed as we do not offer trading accounts for US citizens.',
    'main_operation_address' => 'Main Operation Address',
    'parent_address' => 'Parent Company Address',
    'mail_address' => 'Mail Address',
    'mail_box' => 'Mail Box',
    'mail_city' => 'Mail City',
    'postal_code' => 'Postal Code',
    'profession_license_number' => 'Profession License Number',
    'profession_license_issue_date' => 'Profession License Issue Date',
    'profession_license_expiry_date' => 'Profession License Expiry Date',
    'company_objectives' => 'Company Objectives',
    'list_in_stock_market' => 'Is the company enlisted in any stock markets inside the Kingdom or abroad?',
    'stock_market_name' => 'Stock Market Name',
    'work_in_public_sector' => 'Do you currently work, or have you previously worked in the public sector?',
    'issue_date' => 'Issue Date',
    'identification' => 'Identification Number',
    'subsidiary_name' => 'Name of Foreign Subsidiary',
    'managers_and_directors' => 'Managers And Directors',
    'authorized_signatories' => 'Authorized Signatories on Behalf of the Company',
    'authorized_individuals' => 'Individuals Authorized to Trade on Behalf of the Company',
    'manager_documents' => 'Manager Documents',
    'signatory_documents' => 'Signatory Documents',
    'individual_documents' => 'Individual Documents',
    'signatory_authorization' => 'Company signatory authorization',
    'individual_authorization' => 'Company authorization to deal on behalf of the company',
    'jo_parent_register_certificate' => 'Registration Certificate issued by the relevant authority',
    'jo_parent_concern_certificate' => 'To Whom It May Concern Certificate” showcasing partners, delegates, and company directors',
    'jo_parent_incorporation_certificate' => 'Corporate Charter or Certificate of Incorporation',
    'jo_parent_profession_license' => 'Valid Profession License',
    'jo_parent_authorized_dealers' => 'Document showcasing authorization to deal on behalf of the company (Example: a letter from the Ministry of Industry and Trade)',
    'not_jo_parent_register_certificate' => 'Registration Certificate duly authenticated by the relevant authority',
    'not_jo_parent_concern_certificate' => 'To Whom It May Concern Certificate showing partners, delegates, and company directors',
    'good_standing_certificate' => 'Certificate of Good Standing',
    'not_jo_parent_incorporation_certificate' => 'Corporate Charter or Certificate of Incorporation',
    'subsidiary_register_certificate' => 'Subsidiary Registration Certificate',
    'parent_register_certificate' => 'Parent Registration Certificate issued by the relevant authority in the foreign country',
    'subsidiary_profession_license' => 'Valid Profession License',
    'subsidiary_authorized_dealers' => 'Document showcasing authorization to deal on behalf of the company (Example: a letter from the Ministry of Industry and Trade)',
    'receive_calls' => 'I have no objection to receiving a phone call from the account manager to complete the account opening procedures',
    'real_beneficiary' => 'We acknowledge that we are the real beneficiary of the account and that we have read and understood all the terms of the third-party financial intermediation agreement in foreign stock exchanges and its attachments. We also acknowledge that all information herein is true and accurate. By this acknowledgment, we release INGOT Financial Brokerage Ltd. hereinafter referred to as the "Company" from any liability arising from or due to lack of and/or incorrectness of such information. By this acknowledgment, we accept any decision that will be taken by the company in the case of any of the aforementioned information is incorrect, inaccurate, and/or unreliable, including closing our account with INGOT Financial Brokerage Ltd. without any liability, whatsoever, on the Company.',
    'manager_at_least_1' => 'You should add at least one manager',
    'signatory_at_least_1' => 'You should add at least one Authorized signatory',
    'individual_at_least_1' => 'You should add at least one Authorized Individual',
    'mt_tags' => 'MetaTrader Tags',
    'mt_keys_tip' => 'By selecting a key, it will generate a default comment for the chosen tag.',
    'acquisition_manager' => 'Acquisition Manager',
    'retention_manager' => 'Retention Manager',
    'in' => 'in',
    'out' => 'out',
    'origin_amount' => 'Prev. Amount',
    'origin_currency' => 'Prev. Currency',
    'classification_options' => 'Classification Options',
    'classification_option' => 'App Client Type',
    'application_classification' => 'App. Classification',
    'acquisition_kpi' => 'Acquisition KPI',
    'client_name' => 'Client Name',
    'total_calls_by_agent' => 'Total calls by agent',
    'current_month_calls' => 'Total calls Current Month',
    'current_month_successful_calls' => 'Total successful calls Current Month',
    'residence_address' => 'Area, Street, Building Number',
    'classification_rejected_order' => 'Cant update application classification status, please create classification option first!',
    'edit_career' => 'Edit Career',
    'create_new_career' => 'Create New Career',
    'email_templates' => 'Email Templates',
    'pre_defined_templates' => 'Pre Defined Templates',
    'email_title' => 'Email Title',
    'email_text' => 'Email Text',
    'send' => 'Send',
    'mail_template' => 'Mail Template',
    'sales_mail_template' => 'Sales Email Templates',
    'create_new_mail_template' => 'Create New Mail Template',
    'edit_mail_template' => 'Edit Mail Template',
    'request_a_call' => 'Request a call',
    'call_requests' => 'Call Requests',
    'desired_date' => 'Desired date',
    'already_have_open_real_account_request' => 'A request for this account type has already been submitted. You may submit a new order once your original request is processed',
    'wallet_at' => 'Wallet History At',
    'currency_at_date' => 'Currency At Selected Date',
    'balance_at_date' => 'Balance At Selected Date',
    'markup' => 'Ingot Markup/Commission',
    'lp' => 'LP Commission',
    'security' => 'Security',
    'symbol_groups' => 'Symbol Groups',
    'related_symbol_groups' => 'Related Symbol Groups',
    'securities' => 'Securities',
    'offer_scenarios' => 'Offer Scenarios',
    'offer_scenario' => 'Offer Scenario',
    'max_commission' => 'Max IB/Referral commission',
    'profit_margin' => 'Profit Margin',
    'values_type' => 'Values Type',
    'create_new_user' => 'Create new application ?',
    'commission_transaction' => 'Commission profits',
    'sales_commissions' => 'Sales Commission',
    'trade_time' => 'Trade Time',
    'paid' => 'Paid',
    'trade_type' => 'Trade',
    'by' => 'By',
    'trans_template' => 'The customer has been contacted through',
    'client_timeline' => 'Client Timeline',
    'export_app_data' => 'Export App. Data',
    'creator' => 'Creator',
    'banner' => 'Banner',
    'speaker_avatar' => 'Speaker Avatar',
    'reserve_your_seat_now' => 'Reserve your seat now',
    'email_registered_in_webinar' => 'This Email is already register in this Webinar',
    'registered_successfully' => 'You Have Successfully Signed Up For The Webinar!',
    'registered' => 'Registered Account',
    'guest' => 'Guest',
    'user_type' => 'User Type',
    'group_tags' => 'MetaTrader Group Tags',
    'exists' => 'Exists',
    'political_questions' => 'Political Questions',
    'political_question' => 'Political Question',
    'email_has_been_sent' => 'The email has been sent successfully!',
    'managers_details' => 'Managers Details',
    'signatories_details' => 'Signatories Details',
    'individuals_details' => 'Individuals Details',
    'daily_report' => 'Daily Reports',
    'click_here_without_dote' => 'Click here',
    'back_to_top' => 'Back To Top',
    'recorded' => 'Recorded',
    'recorded_by' => 'Recorded By',
    'recorded_at' => 'Recorded At',
    'posted' => 'Posted',
    'posted_by' => 'Posted By',
    'posted_at' => 'Posted At',
    'at' => 'At',
    'reference_number' => 'Reference Number',
    'total_calls' => 'Total Calls',
    'not_recorded' => 'Not Recorded',
    'not_posted' => 'Not Posted',
    'eligibility_test_is_required' => 'You have to pass the eligibility test then upload the documents',
    'client_eligibility_test' => 'Client Eligibility Test',
    'is_numeric' => 'Numeric Question',
    'numeric' => 'Is Numeric',
    'try_again_later' => 'You have reached the maximum number of attempts for the eligibility test. Please try again in 24 hours',
    'try_again' => 'Unfortunately, you didnt pass the test successful, You can try it again',
    'pass_eligibility_test' => 'Congrats You have passed the test',
    'already_passed_eligibility_test' => 'You have passed the test',
    'eligibility_test_answers' => 'Eligibility Eligibility Test Answers',
    'test_not_ready' => 'Eligibility Test Is Not Ready Yet!',
    'passport_1' => 'Passport Page 1',
    'passport_2' => 'Passport Page 2',
    'id_doc_content_5' => 'You can upload either passport or ID card or both',
    'apply' => 'Apply',
    'tokens' => 'Tokens',
    'token' => 'Token',
    'funds_account' => 'Funds Account',
    'edited' => 'Edited',
    'national_number' => 'National ID',
    'payment_method_webhook_logs' => 'payment method webhook Logs',
    'include_mt_transactions' => 'Display internal MetaTrader Transactions',
    'seminars' => 'Seminars',
    'seminar' => 'Seminar',
    'attend' => 'Attend',
    'doesnt_attend' => 'Doesnt Attend',
    'reg_count' => 'Count of Reg.',
    'allow_withdraw_mt' => 'Allow Withdraw to Wallet',
    'notify' => 'Notify',
    'newsroom' => 'Newsroom',
    'referral_links_config' => 'Referral link Type',
    'wallet_balance_after_transaction' => 'Balance after txs',
    'first_deposit' => 'First Deposit',
    'email_is_invalid_msg' => 'The email sender or receivers is invalid',
    'logged_in_as_user_disclaimer' => 'Notice: You are currently logged in with restricted permissions. This account is limited to view-only access, and no actions should be performed. If this access level is unexpected or if you require additional permissions to complete specific tasks, please contact the system administrator for assistance.',
    'users_different_platforms' => 'Users are from different platforms you have to do the transfer manually',
    'done' => 'Done',
    'no_enough_balance' => 'Your balance is less than the requested amount',
    'transaction_canceled_successfully' => 'Your transaction is canceled successfully',
    'transaction_types' => 'Transaction Types',
    'debug' => 'Debug',
    'emails_bulk' => 'Email Notifier',
    'seych_id_doc_accepted_2' => 'Passport copy',
    'seych_id_doc_accepted_1' => 'Government issued Identity Card',
    'seych_id_doc_accepted_3' => 'Government issued ID proofs',
    'seych_proof_doc_accept_content_2' => 'Bank statement',
    'seych_proof_doc_accept_content_3' => 'Latest Utility Bill',
    'seych_proof_doc_accept_content_6' => 'Credit card statement',
    'seych_proof_doc_accept_content_7' => 'Affidavit',
    'seych_proof_doc_accept_content_8' => 'Bank Reference Letter',
    'seych_proof_doc_not_accept_content_2' => 'Proof of residence should include the name of the account holder.',
    'seych_real_trader_agreement_1' => 'I declare that I am the real beneficiary of the account and that I have read and understood all the terms of the Client Service Agreement',
    'seych_real_trader_agreement_2' => 'I also hereby certify that all the information mentioned here is true and accurate, and I hereby relieve INGOT Global Ltd. (hereinafter referred to as “the Company”) of any responsibility and/or liability whatsoever that might occur or result from the inaccuracy and/or incorrectness of such information.',
    'seych_real_trader_agreement_3' => 'I hereby accept any decision by the Company in the event of it finding out that any of the information mentioned in this disclaimer is incorrect, untrue and/or inaccurate, including closing my account at INGOT Global Ltd. without any liability whatsoever towards the Company',
    'confirm_new_license_message_part_1' => 'INGOT is dedicated to provide you with the best trading experience under optimal legal and trading conditions. Therefore, your account, positions and equity will be moved to INGOT GLOBAL LTD (which is a company registered in the Republic of Seychelles under the number 8431005-1 and licensed by the Financial Services Authority “FSA”, License Number: SD117) as of the date of this notification.',
    'confirm_new_license_message_part_2' => 'Your first login to your account and/or your first trading is considered acknowledgment and approval to the legal documents of INGOT GLOBAL LTD, which could be accessed',
    'confirm_new_license_message_part_3' => 'If you have any inquiry, please contact Customer Service at',
    'reg_confirm_global_1' => 'By ticking the box, I acknowledge through my electronic approval of all the terms of :company_name’s',
    'reg_confirm_global_2' => 'and agreements (including but not limited to the',
    'client_services_agreement' => 'Client Service Agreement',
    'compliant_handling_policy' => 'the Compliant Handling Policy',
    'best_execution_policy' => 'Best Execution Policy',
    'and_the' => 'and the',
    'reg_confirm_global_3' => 'and this should be considered as my actual signature on the said documents and my acknowledgment of what is stated therein.',
    'branch' => 'branch',
    'branches' => 'branches',
    'sub_branch' => 'sub branch',
    'sub_branches' => 'Sub branches',
    'main_branch' => 'main branch',
    'main_branches' => 'main branches',
    'map' => 'map',
    'amount_in_usdt' => 'Amount in USDT',
    'source_of_fund_error' => 'The answer is required',
    'deduct_scalping' => 'Deduct Scalping',
    'mt_password_changed_successfully' => 'Your password has been reset!',
    'mt_password_cant_change' => 'You cannot update your password right now!',
    'export_send_by_mail' => 'The export process is currently underway in the background. Once completed, the sheet will be dispatched to your email address.',
    'request_failed' => 'We can\'t process your request right now.',
    'whitelisted_ip' => 'Whitelisted IP',
    'whitelisted_ips' => 'Whitelisted IPs',
    'ip_addresses' => 'IP Addresses',
    'updated_by' => 'Updated By',
    'is_active' => 'Is Active',
    'social_information' => 'Social Information',
    'interests' => 'Interests',
    'religion' => 'Religion',
    'risk_kpi' => 'Risk Kpi\'s',
    'max' => 'Max',
    'sum_of_tickets' => '# tickets',
    'risk_daily_kpi_note' => 'The result at specified date',
    'risk_weekly_kpi_note' => 'The result from specified date sub week',
    'risk_monthly_kpi_note' => 'The result from specified date sub month',
    'mt_balance' => 'MetaTrader Balance',
    'total_balance' => 'Total Balance',
    'confirmed_global_license' => 'Confirm Global License',
    'not_confirmed' => 'Not Confirmed',
    'confirmed' => 'Confirmed',
    'confirm_lvg_desc' => 'By ticking this box, I hereby accept the aforementioned Leverage and this <a target="_blank" href=":ACKNOW_URL">Acknowledgement</a>. I also hereby acknowledge that I am fully aware with the high risks that are associated with the financial Leverage.',
    'confirm_cond_desc' => 'By ticking this box, I hereby confirm that I have had access to, reviewed, read, understood, and accept the full set of INGOT Financial brokerage Ltd. legal documents including the <a target="_blank"  href=":LVG_TERMS">Leverage Terms</a>.',
    'bank_code' => 'Bank Code',
    'document_type' => 'Document Type',
    'document_types' => 'Document Types',
    'document_type_key' => 'Document Key',
    'ingot_bank' => 'INGOT Bank',
    'need_check' => 'Needs Check',
    'successfully_execute_whitelabel_transaction' => 'Successfully execute your transaction',
    'successfully_create_whitelabel_transaction' => 'Successfully create your transaction , wait until your Sr check it .',
    'whitelabel' => 'Whitelabel',
    'allowed_trading_platforms' => 'Allowed Trading Platforms',
    'need_for_report' => 'Specify the need for the report',
    'popular_instrument' => 'Popular Instrument',
    'alternative_payment_gateway' => 'Alternative Payment Gateway',
    'alternative_payment_gateway_note' => 'You can set an alternative payment method to the client who deposited from the current payment gateway, so they can use it for withdrawal.',
    'internal_account_name' => 'Internal Account Name',
    'application_is_completed' => 'The Application is completed',
    'failed_job_details' => 'Failed Job Details',
    'last_trade_at' => 'Last trade date',
    'last_traded_symbol' => 'latest traded instrument',
    'last_mt_deposit' => 'last deposit at',
    'agreement_entity_note' => 'The agreement you are about to sign, acknowledge and adhere to is with :company_name',
    'introducing_broker_agreement_svgfsa' => 'Introducing Broker (IB) Agreement',
    'addendum_ib_agreement_svgfsa' => 'Addendum to the IB Agreement',
    'agreements_key_2' => 'and agreements (including but not limited to the IB Agreement,',
    'agreements_key_3' => 'and agreements (including but not limited to the CPA Affiliate Agreement',
    'referral_agreement_svgfsa' => 'Referral Agreement',
    'agreements_key_4' => 'and agreements (including but not limited to the Referral Agreement,',
    'iso' => 'ISO',
    'quarter' => 'Quarter',
    'quarter_1' => 'Q1',
    'quarter_2' => 'Q2',
    'quarter_3' => 'Q3',
    'quarter_4' => 'Q4',
    'company_email' => 'Company Email',
    'company_mobile' => 'Company Mobile',
    'company_telephone' => 'Company Telephone',
    'company_fax' => 'Company Fax',
    'mt_pending_orders_report' => 'MetaTrader Pending Orders Report',
    'excel_format_worng' => ':count contacts are not added; please check the sheet format!',
    'mt_wallets_report' => 'MetaTrader Wallets Report',
    'sheet_empty' => 'Nothing added; The sheet is empty!',
    'comment_first' => 'Before proceeding, please make sure to review and verify all relevant details carefully then add an internal comment',
    'contact_cs_to_edit' => 'Kindly note that you can not edit the approved applications. Please get in touch with the Customer Service department for any further edits.',
    'employee_setting' => 'Employee Setting',
    'total_calls_config' => 'Total calls config',
    'email_body' => 'Email Content',
    'email_options' => 'Email Options',
    'mt_group_type' => 'MT Group Type',
    'shared_group' => 'Shared Group',
    'additional_document' => 'Additional Document',
    'receive_in' => 'Bank / Exchange',
    'and_comma' => '',
    'user_number' => '{0,1} :count user|[2,*] :count users',
    'open_an_affiliate_account' => 'Open an Affiliate Account',
    'viewed_by_compliance' => 'viewed by compliance',
    'rec_account' => 'Received Account',
    'credit_request' => 'Credit Request',
    'email_ta_intro' => 'Prepared by leading industry experts specifically for INGOT Brokers clients, this daily market report includes a comprehensive analysis of the most popular instruments worldwide, consequently presenting traders with a general outlook over their potential next trade idea.',
    'disclaimer_all' => 'DISCLAIMER: The information and data provided herein are of a general nature, strictly for general informational purposes, and proprietary to TDR VIEWS LTD.  Such information does not consider your personal circumstances, financial situation, or needs. Before deciding to trade, please ensure you understand the risks involved and take into account your level of experience. Trading leveraged products incur a high level of risk and can result in great losses. Therefore, it may not be suitable for all investors.',
    'open_referral_account' => 'Open a Referral Account',
    'check_account_platform' => 'Please check the account platform',
    'mt_account_credit' => 'MetaTrader Account Credit',
    'mt_shared_groups' => 'Shared Groups',
    'assign_shared_group' => 'Assign Shared Groups',
    'update_assign_shared_group' => 'Update Assign Shared Groups',
    'swap_type' => 'Swap Type',
    'clone' => 'Clone',
    'already_have_account' => 'Already have account?',
    'open_an_individual_accounts' => 'Open an Individual Account',
    'inconsistent_branch' => 'inconsistent sub-branch with the main branch',
    'create_account_type' => 'Create Account Type',
    'show_account_type' => 'Show Account Type',
    'edit_account_type' => 'Edit Account Type',
    'select_account_type_to_duplicate' => 'Select account type to duplicate it\'s contracts',
    'portal_url' => 'Portal URL',
    'site_url' => 'Site URL',
    'cant_process_your_request' => 'We can\'t process your request right now .',
    'attach_of_add_data_note' => 'Please attach a .csv file or fill out the below form!',
    'idwise_portal' => 'IDWise Portal',
    'idwise_no_portal' => 'Document review is :STATUS',
    'confirmed_agent_on_sv' => 'Agents who confirmed the SV License',
    'failed_to_upload' => 'Failed to upload file',
    'allow_deduct_scalping' => 'Allow Deduct Scalping',
    'prevent_deduct_scalping' => 'Prevent Deduct Scalping',
    'scenario_details' => 'Scenario Details :',
    'place_order' => 'Place Order',
    'archived_chats' => 'Archived Chats',
    'archived_chat' => 'Archived Chat',
    'add_valid_embed_url' => 'Please add valid video embed URL',
    'cant_transfer_fund' => 'In order to be able to transfer fund please verify your account',
    'executed_at' => 'Executed Date',
    'executed_from_date' => 'Executed From',
    'executed_to_date' => 'Executed To',
    'created_from' => 'Created From',
    'created_to' => 'Created To',
    'become_a_follower' => 'Become a Follower',
    'become_a_provider' => 'Become a Provider',
    'no_available_groups' => 'No Available Groups',
    'please_update_your_bank_info' => 'Please update your bank informations',
    'cant_delete_approved_offer' => 'You can\'t delete approved offer .',
    'edit_details' => 'Edit details',
    'confirm_transfer' => 'You are about to execute an irretrievable fund transfer to another person. Once you select “Transfer”, the amount you entered will be transferred to the selected client’s account.',
    'exchangeable' => 'Exchangeable',
    'configurations' => 'Configurations',
    'commission_receiver' => 'Receiver',
    'total_paid' => 'Paid Commissions',
    'net_commission' => 'Net Commission',
    'total_unpaid' => 'Unpaid Commissions',
    'deny_offer_update' => 'Approved offer can\'t be updated!',
    'percent_rebate' => 'Percent Rebate',
    'offer_sent_successfully' => 'Offer successfully sent!',
    'offer_sent_failed' => 'Offer failed to sent!',
    'send_offer' => 'Send Offer',
    'approve' => 'Approve',
    'offer_approved_successfully' => 'Offer successfully approved!',
    'offer_approve_failed' => 'Offer failed to approve!',
    'offer_rejected_successfully' => 'Offer rejected successfully!',
    'offer_reject_failed' => 'Offer failed to reject!',
    'offer_rejection_reason' => 'Please provide us with your rejection reason',
    'offer_clone_desc' => 'Please chose the application you want to clone the offer for',
    'app_offer_log' => 'App Offer Log',
    'unable_to_edit' => 'You cannot modify, the offer has been sent to the client',
    'unable_to_edit_scenario_offers' => 'You cannot edit the scenarios that connected with an approved offer!',
    'pending_offers_warning' => 'Please note that you have pending offers , <a href=":LINK">check your offers</a>',
    'complete_profile_ib_offers' => 'Please note that you will not be able to approve/reject pending offers before verifying your profile, <a href=":LINK">Verify Your Profile</a>',
    'nullable_offer_commission' => 'Please note that to be able to send an offer you have to set commissions',
    'export_for_client' => 'Export for client',
    'full_offer_export' => 'Export full report',
    'doc_not_available' => 'Doc not available',
    'upload_documents' => 'Upload Documents',
    'proceed_by' => 'Proceed by',
    'proceed_by_note' => 'Name of the person who transferred the transaction from the Payment Jr. to the Payment Sr. role',
    'executed_by_note' => 'Name of the person who executed the transaction (reject or approve)',
    'allocation_agent_commission_report' => 'Agent Commission Report',
    'agent_commission' => 'Agent Commission',
    'display_mt_details' => 'Display MetaTrader Accounts Details',
    'request_vacation' => 'Request a vacation',
    'requested_at' => 'Requested at',
    'vacation_requests' => 'Vacation Requests',
    'delegated_to' => 'Delegated To',
    'internal_requests' => 'Internal Requests',
    'internal_request' => 'Internal Request',
    'request_type' => 'Request Type',
    'update_internal_request' => 'Update Internal Request',
    'show_internal_request' => 'Show Internal Request',
    'already_in_vacation' => 'The requester is already in his vacation and his leads assigned to someone in his team',
    'my_requests' => 'My Requests',
    'request_canceled_successfully' => 'Your request is canceled successfully',
    'in_vacation_mood' => 'Your vacation is in progress, and your leads are temporarily assigned to <b>:delegated_name</b>',
    'command' => 'Command',
    'kra' => 'KRA',
    'affidavit_of_residence' => 'Affidavit Of Residence',
    'kenya_address_confirm_1' => 'I hereby certify that the information I am providing herein is true and complete and to the best of my knowledge up to the date of this declaration. I will update this information as and when any of its items change. This declaration will be considered as part of the on-boarding process and will be subject to all the applicable laws, rules and regulations.',
    'kenya_address_confirm_2' => 'I am aware that this self-declaration statement is subject to review and verification and if such information has been falsified my contract with INGOT KE Ltd. may be terminated upon INGOT\'s sole discretion.',
    'names_as_per_id' => 'Names as per ID',
    'apartment' => 'Apartment\building',
    'street' => 'Street',
    'town' => 'Town/constituency',
    'signature/initials' => 'Signature/initials',
    'proof_doc_kenya_note_1' => 'You can upload KRA or fill Affidavit of residence information',
    'all_entities' => 'All Entities',
    'parameter' => 'Parameter',
    'controlling_officer_documents' => 'Controlling Officer Documents',
    'no_team_member_available' => 'No team member available',
    'lead_source' => 'Lead Source',
    'end_vacation' => 'End Vacation',
    'canceled_at' => 'Canceled At',
    'me' => 'Me',
    'no_available_users' => 'No available users',
    'agent_transaction_report' => 'Agents Transaction Report',
    'receiver_account' => 'Receiver Account',
    'manaf_application_id' => 'Manaf App. ID',
    'commission_model' => 'Commission Model',
    'fixed_percent' => 'Fixed Percent',
    'shared_commission' => 'Shared Commission',
    'has_not_wallet' => 'Has No Wallet',
    'withdraw_option' => 'Withdraw Option',
    'bank_account' => 'Bank Account',
    'mobile_payment_provider' => 'Mobile Payment Provider',
    'politically_exposed_check' => 'I hereby certify that I am not a Politically Exposed Person (“PEP”) as defined in the applicable Anti Money Laundering and Counter-Terrorist Financing laws and regulations, as I do not occupy any high position within or outside :entity, and I am not among political, governmental, judicial or military personnel, nor am I an employee of executive positions in any government-owned company, chief of any political party or have any prominent, high-level position in any international organization, such as manager, vice-manager, board member or any similar role. Additionally, I am not a relative of any of the aforementioned persons or their families, nor a close person to any of them.',
    'error_404_description' => 'Sorry, but the page you are looking for does not exist and has been removed. The page’s address has either been changed or is temporarily unavailable.',
    'error_500' => 'An unexpected error has occurred',
    'error_500_description_1' => 'We\'ve encountered an issue processing your request. Our team is already aware and is diligently working to resolve it promptly.',
    'error_more_details' => 'For more details, please <a class="dashed-border hover:text-ired" href=":URL">contact us.</a>',
    'error_403_description_1' => 'Sorry, but you do not have permission to access this page.',
    'error_401_description_1' => 'Sorry, but you do not have permission to access this page.',
    'error_413_description_1' => 'The requested entity is too large.',
    'error_419_description_1' => 'Sorry, your session has expired. Please refresh and try again.',
    'error_422_description_1' => 'Sorry, the request was rejected.',
    'error_503_description_1' => 'Our Portal is currently undergoing maintenance. We Should be back shortly. Thank you for your patience.',
    'live_chat' => 'Live Chat Support',
    'whatsapp' => 'WhatsApp',
    'pending approval' => 'Pending Approval',
    'create_time_zone' => 'Create Time Zone',
    'edit_time_zone' => 'Edit Time Zone',
    'time_zones' => 'Timezones',
    'time_zone' => 'Timezone',
    'activity_log' => 'User Activities',
    'log_name' => 'Log Name',
    'causer_type' => 'Causer Model Name',
    'properties' => 'Properties',
    'subject_id' => 'Subject ID',
    'subject_type' => 'Subject Type',
    'causer_id' => 'Causer ID',
    'timezone_note' => 'Preferred timezone for displayed reports',
    'language_note' => 'Preferred language for email notifications',
    'excepted_applications' => 'Excepted Applications',
    'excepted_groups' => 'Excepted Groups',
    'company_proof_address_africa' => 'Company Proof Address',
    'ownership_structure' => 'Ownership Structure',
    'kra_pin' => 'KRA pin',
    'bank_statement' => 'Bank Statement',
    'not_allocated' => 'Not Allocated',
    'active_logs' => 'Active Logs',
    'data' => 'Data',
    'updated_data' => 'Updated Data',
    'hi' => 'Hi',
    'user_logs' => 'User Logs',
    'receiver' => 'Receiver',
    'mobile_number' => 'Mobile Number',
    'nothing_selected' => 'Nothing selected',
    'canceled' => 'Canceled',
    'serial_number' => 'Serial Number',
    'calculation_models' => 'Calculation Model',
    'symbol_id' => 'Symbol',
    'security_id' => 'Security',
    'item' => 'Item',
    'clone_offer' => 'Clone Offer',
    'commission_system_config' => 'Commission System Config',
    'material' => 'material',
    'material_category' => 'Material Category',
    'material_categories' => 'Material Categories',
    'create_material_categories' => 'Create Material Categories',
    'show_material_categories' => 'Show Material Categories',
    'material_dimension' => 'Material Dimension',
    'material_dimensions' => 'Material Dimensions',
    'create_material_dimensions' => 'Create Material Dimensions',
    'show_material_dimensions' => 'Show Material Dimensions',
    'height' => 'Height',
    'width' => 'Width',
    'dimension' => 'Dimension',
    'marketing_materials' => 'Marketing Materials',
    'marketing_material' => 'Marketing Material',
    'all_dimensions' => 'All Dimensions',
    'all_categories' => 'All Categories',
    'create_marketing_materials' => 'Create Marketing Materials',
    'show_marketing_materials' => 'Show Marketing Material',
    'ignore_rejected_txs' => 'Ignore rejected transactions',
    'confirm_tranfer_funds' => 'Are you sure you want to transfer <b>:amount :currency</b> to the <b>:account</b>?',
    'aml_result' => 'AML Result',
    'dataset' => 'Dataset',
    'score' => 'Score',
    'matches' => 'Matches',
    'activities' => 'Activities',
    'has' => 'has',
    'accountant_certificate_whole_doc' => 'Certificate from a Qualified Accountant',
    'wholesale_information_whole_doc' => 'Wholesale Client Information Disclosure Form',
    'wholesale_acknowledgment_whole_doc' => 'Sophisticated Investor / Wholesale Client Acknowledgment',
    'client_eligibility_soph_doc' => 'Sophisticated Investor/Wholesale Client Eligibility Test',
    'wholesale_information_soph_doc' => 'Wholesale Client Information Disclosure Form',
    'wholesale_acknowledgment_soph_doc' => 'Sophisticated Investor / Wholesale Client Acknowledgment',
    'allow_add_financeable' => 'Allow user to add their financial information',
    'extra_data' => 'Extra Data',
    'card_type' => 'Card Type',
    'confirmation' => 'Confirmation',
    'metatrader_password' => 'Metatrader Password',
    'reserved_at' => 'Reserved Date',
    'available_at' => 'Execution Date',
    'master_password' => 'Master Password',
    'inv_password' => 'Investor Password',
    'user_information' => 'User Information',
    'open_demo_account' => 'We recommend you log onto a demo account and gain the necessary knowledge before proceeding onto a live account <a href=":URL" target="_blank"><b>Click Here</b></a>',
    'stop_application' => 'Cannot onboard US citizens  so the application should not go through',
    'direct_clients_commission' => 'Direct Clients Commissions',
    'indirect_clients_commission' => 'In-Direct Clients Commissions',
    'total_volumes' => 'Total Volumes',
    'per_agent' => 'Per Agent',
    'per_group' => 'Per Group',
    'per_symbol' => 'Per Symbol',
    'per_security' => 'Per Security',
    'earned_commission' => 'Earned Commission',
    'commissions_report' => 'Commissions Report',
    'receiver_type' => 'Receiver Type',
    'age' => 'Age',
    'aml_report' => 'AML Report',
    'crypto_processing_time_note' => 'The processing time of crypto transactions is subject to the network\'s conditions. As per our internal policy, for a crypto transaction to be completed, a minimum of six transaction validations is required.',
    'crm_url' => 'CRM URL',
    'affiliate' => 'Affiliate',
    'affiliate_company' => 'Affiliate company',
    'affiliate_additional_info' => 'Affiliate additional info',
    'pending sign offer' => 'Pending sign offer',
    'group_count' => '{0,1} :count group|[2,*] :count groups',
    'connected_groups' => 'Connected groups',
    'direct_manager' => 'Direct Manager',
    'target_rate' => 'Target Rate',
    'flag_types' => 'Flag Types',
    'has_flagged' => 'Has Flagged',
    'team_kpis' => 'Team KPIs',
    'kpis' => 'KPIs',
    'qr_scan' => 'Scan QR',
    'assign_keep_empty_note' => 'Keep the this fields empty, if you are looking for the not assigned applicants!',
    'active_only' => 'Active Apps. Only',
    'include_null_countries' => 'Include clients with empty Country Of Residence',
    'include_null_citizen' => 'Include clients with empty Nationality',
    'results' => 'Results',
    'never_logged_in_for_ninety_days' => 'Never logged in for the last 90 Days',
    'please_configure_before_enable' => 'Please make sure to add the payment gateway configuration before enabling it',
    'self_employed' => 'Self-employed',
    'risk_rating' => 'Risk Rating',
    'low_risk' => 'Low Risk',
    'meduim_risk' => 'Meduim Risk',
    'high_risk' => 'High Risk',
    'enhanced_high_risk' => 'Enhanced High Risk',
    'country_entity_risk_levels' => 'Country Entity Risk Levels',
    'create_country_entity_risk_levels' => 'New Country Entity Risk Level',
    'show_country_entity_risk_level' => 'Country Entity Risk Level Details',
    'edit_country_entity_risk_levels' => 'Edit Country Entity Risk Level',
    'risk_level' => 'Risk level',
    'already_exists' => 'Already Exists!',
    'no_risk' => 'No Risk',
    'referral_account' => 'Referral Account',
    'affiliate_account' => 'Affiliate Account',
    'referred_by' => 'Referred By',
    'assign_role' => 'Assign Role',
    'steps_count' => '{0,1} :count step|[2,*] :count steps',
    'not_traded_for_month' => 'Not traded for a month',
    'not_approved' => 'Not Approved',
    'freeze' => 'Freeze',
    'processing' => 'Processing',
    'app_relation_report' => 'Relationship Report',
    'relation_history' => 'Relations History',
    'not_detected' => 'Not Detected',
    'public_sector_position' => 'Public Sector Position',
    'color' => 'Color',
    'red' => 'Red',
    'yellow' => 'Yellow',
    'green' => 'Green',
    'process_started' => 'Your request has been initiated, and the process is now underway. Please be patient, as this may take some time to complete in the background.',
    'agent_groups' => 'Agent Groups',
    'agent_mt_accounts' => 'Agent MT Accounts',
    'tmd' => 'Target Market Determination',
    'low' => 'low',
    'bid_high' => 'bid high',
    'bid_low' => 'bid low',
    'ask_high' => 'Ask high',
    'ask_low' => 'Ask low',
    'symbol_prices' => 'Symbols Pricing',
    'check_iban' => 'Check Iban',
    'not_assigned_to_payment' => 'Not assigned to payment department',
    'not_assigned_to_risk' => 'Not assigned to risk department',
    'electrical_meter_number' => 'Electrical Meter Number',
    'water_meter_number' => 'Water Meter Number',
    'reg_av_groups' => 'Reg. Av. Groups',
    'wrong_otp' => 'Wrong entry for OTP',
    'alias' => 'Alias',
    'has_assigned_scenario' => 'Has Assigned Scenarios',
    'needs_update' => 'Needs Update',
    'offer_scenario_group' => 'Scenario Group',
    'offer_scenario_groups' => 'Scenario Groups',
    'agent_categories' => 'Agent Categories',
    'agent_category' => 'Agent Category',
    'min_accounts' => 'Min Accounts',
    'max_accounts' => 'Max Accounts',
    'accounts_range' => 'Accounts Range',
    'assign_reason' => 'Assign Reason',
    'app_relation_history' => 'Relation History',
    'number_of_logs' => 'Number of Logs',
    'record_number' => '{0,1} :count record|[2,*] :count records',
    'ip_connect' => 'You are connecting to server local IP:',
    'categorization_type' => 'Categorization Type',
    'automatic' => 'Automatic',
    'manual' => 'Manual',
    'tx_process_started' => 'Your request has been initiated, and the process is now underway. Please be patient, as this may take some time to complete in the background.',
    'relationship_changed' => 'show only the relationships that have changed',
    'configuration_keys' => 'Configuration Keys',
    'public_only' => 'Public Only',
    'affiliate_website_config_keys' => 'Affiliate Website Config Keys',
    'is_public' => 'Is Public',
    'create_affiliate_website_config_key' => 'Create Affiliate Website Config Key',
    'show_affiliate_website_config_key' => 'Show Affiliate Website Config Key',
    'brief' => 'Brief',
    'media_library' => 'Media Library',
    'show_media' => 'Show Media',
    'create_media' => 'Create Media',
    'not_have_available_groups' => 'Has No Available Groups',
    'previously_rejected' => 'Previously Rejected for Verification',
    'apply_to_no_av_groups' => 'Apply this to those who under this agent, but do not have an app. available group',
    'has_mt_wallet' => 'Has MT Wallet',
    'has_e_wallet' => 'Has E-Wallet',
    'citizenship_confirmed_at' => 'Confirm not a US Citizenship At',
    'is_real_beneficiary' => 'Real Beneficiary',
    'daily_newsletter' => 'Daily Newsletter',
    'others' => 'Others',
    'closed-by' => 'Closed By',
    'app_id_comma' => 'App IDs seperated by comma',
    'assign_campaign' => 'Assign Campaign Leads',
    'seminar_leads' => 'Seminar Leads',
    'assign_seminar' => 'Assign Seminar Leads',
    'active_accounts' => 'Active Accounts',
    'archived_accounts' => 'Deleted/Expired Accounts',
    'payment_gateway_equity' => 'Payment Gateway Equity & Credit Report',
    'sales_deposit_withdrawal_report' => 'Sales Deposit & Withdrawal Report',
    'number_of_withdrawals' => 'Number of withdrawals',
    'number_of_deposits' => 'Number of deposits',
    'commission_model_note' => '<b>Shared Commission :</b> Each level of IBs will have his own selected commission. </br><b>Fixed Percent :</b> All IBs levels will get fixed percent of commission (:commission_percent%).',
    'categorization_type_note' => '<b>Automatic : </b> Agent categotization will be changed automaticlly and you should set commission value for each category </br><b> Manual : </b> Changing commission category will be manually </br> ',
    'scenario_group_note' => ' * In order to be able to use thees Scenario Groups when creating offers please make sure of these points : Having scenarios for all entities within the Scenario group in additional to having linked MT group for each scenario.',
    'apply_to_all_btn' => 'Click to apply values to other scenarios',
    'offer_successfully_cloned' => 'Offer successfully cloned!',
    'none' => 'None',
    'wallet_b_should_be' => 'Expected Wallet Balance ',
    'assign_webinar' => 'Assign Webinars',
    'email_already_used' => 'The email is already used by another user!',
    'events' => 'Events',
    'promotions' => 'Promotion',
    'our_news' => 'Updates On Our News',
    'receive_email_and_sms' => 'Subscribe to receive email and SMS updates on our latest news, promotions, events, and more.',
    'sms_body' => 'SMS Body',
    'bulk_sms' => 'SMS Notifier',
    'clients' => 'Clients',
    'mobile_numbers' => 'Mobile Numbers',
    'mobile_numbers_note' => 'Mobile numbers seperated by comma',
    'invalid_mobile_numbers' => 'Invalid Mobile Numbers',
    'invalid_message_length' => 'Invalid Message Length',
    'message_length_sms_note' => 'The max length of message is 70 characters in arabic and 140 characters in english',
    'ingot_card_activate_steps_1' => 'Once you’ve received your card, you can activate it by following the steps below:',
    'ingot_card_activate_steps_2' => 'Open the INGOT Card application on your phone and click on “Register New User”.',
    'ingot_card_activate_steps_3' => 'Insert your phone number and INGOT Card number.',
    'ingot_card_activate_steps_4' => 'Select your nationality and ID type. Click Next to upload an image of your ID.',
    'ingot_card_activate_steps_5' => 'Take a selfie to prove your identity.',
    'ingot_card_activate_steps_6' => 'Update your profile information. Some of the fields will be filled automatically.',
    'ingot_card_activate_steps_7' => 'Add your username and password.',
    'ingot_card_activate_steps_8' => 'You will receive a One-Time Password (OTP) on your phone number. Insert it in the respective field.',
    'ingot_card_activate_steps_9' => 'Once your registration process is completed, a pop-up message will appear indicating successful registration and you will be able to access your account.',
    'already_have_card_request' => 'Your request is being processed',
    'assign_webinar_leads' => 'Assign Webinars Leads',
    'webinar_leads' => 'Webinar Leads',
    'congratulations' => 'Congratulations!',
    'to_submit_withdrawal_request_contact_customer_service' => 'To submit a withdrawal request, please contact customer service or contact your account manager.',
    'disable_offer_creation' => 'You can not manage offers and shred groups for unapproved and incomplete accounts!',
    'hedging' => 'Hedging',
    'product_partnership' => 'Product Partnership',
    'add_new_instrument' => 'Add New Instrument',
    'import' => 'Import',
    'margin_call_type' => 'Margin Call Type',
    'percentage' => 'Percentage (Margin Level)',
    'fixed' => 'Fixed (Equity)',
    'margin_level_type' => 'Margin Call Type',
    'covering_login' => 'Covering Login',
    'covering_login_note' => 'MT login for the compliance report',
    'compliance_report_included' => 'Included In Compliance Report',
    'balances' => 'Balances',
    'balances_list_note' => 'List of balances as numbers (5000, 10000, 15000), you can select multiple',
    'ingoteers' => 'INGOTeers',
    'causer' => 'Causer',
    'thirda_party_note' => 'The third-party payments are prohibited due to regulatory requirements. All deposits and withdrawals must be made using payment methods that belong to you, and the beneficiaries name must match the name on the trading account. Additionally, funds can only be returned to the same payment method and currency that was used to deposit them.
     By initiating a transaction with us, you confirm that the payment details are under your ownership and authority. You will be solely responsible for any legal liability that may arise from providing a payment method that does not belong to you.',
    'client_retention_report' => '90 days In-active Clients',
    'times' => 'Times',
    'portal_logins' => 'Login to portal',
    'match' => 'Match',
    'any' => 'Any',
    'referred_by_comm_system' => 'Referred by commission system',
    'where_type' => 'Type of search',
    'offer_deactivated' => 'Offer deactivated successfully!',
    'where_type_note' => 'Select the type of search you want to perform, any means where follower or closer or parent, match means where follower and closer and parent',
    'offer_deactivate_reason' => 'Please provide the reason for deactivating the offer ',
    'delete_active_terms' => 'Active Terms & Conditions could not be deleted , please activate another Terms & Conditions to be able to delete the activated one  ',
    'default_terms' => 'Use Default Terms & Conditions',
    'custom_terms' => 'Custom Terms & Conditions',
    'wallett_daily_balancies_note' => 'The "Balance at Selected Date" represents the closing balance for the specified day. When filtering data by a particular date, the balance shown reflects the amount as of 23:59:59 on that day.',
    'commission_direction' => 'Commission Direction',
    'direct' => 'Direct',
    'indirect' => 'In-Direct',
    'portal_type' => 'Portal Type',
    'cellxpert_portal' => 'Cellxpert Portal',
    'ingot_portal' => 'INGOT Portal',
    'please_contact_your_account_manager' => 'We cannot proceed with your inquiry right now, Please contact your account manager',
    'cellxpert_default_rules' => 'Cellxpert Offer Rules',
    'create_cellxpert_rule' => 'Create Cellxpert Rule',
    'edit_cellxpert_rule' => 'Edit Cellxpert Rule',
    'cellxpert_affiliate_id' => 'Cellxpert Affiliate ID',
    'cellxpert_sales_rules' => 'Cellxpert Sales Rules',
    'create_cellxpert_sales_rule' => 'Create Cellxpert Sales Rule',
    'edit_cellxpert_sales_rule' => 'Edit Cellxpert Sales Rule',
    'manager' => 'Manager',
    'department_name' => 'Department Name',
    'departments' => 'Departments',
    'create_new_department' => 'Create New Department',
    'bitcoin_wallet_repo' => 'Bitcoin Wallets Repository',
    'application_verification_offer_note' => 'You can attach the partner offer to be sent by email, or ask the account manager to this manually',
    'technical_indicators' => 'Technical Indicators',
    'hour' => 'Hour',
    'hours' => 'Hours',
    'market_day' => 'day',
    'market_week' => 'Week',
    'market_month' => 'Month',
    'app_offer_tree' => 'App Offer Tree',
    'no_permission_to_wallet_transfer' => 'You don\'t have permission to transfer to client\'s wallet',
    'last_assign_date' => 'Last Assign Date',
    'agent-corporate' => 'Agent Corporate',
    'request_details' => 'Request Details',
    'allow_swap_free_request' => 'Swap Free Request',
    'swap_confirmation' => 'I confirm that I have read and understood the <a href=":URL" target="_blank">Swap Free Policy</a> and I would like to request a swap-free.',
    'swap_free_request' => 'Request Swap Free',
    'you_have_request_before' => 'You already have a pending order',
    'no_download_link' => 'No link is available to download!',
    'cant_get_the_exchange_rate' => 'Can\'t get the exchange rate for the currency',
    'documents_history' => 'Documents History',
    'demo_group_addons' => 'Demo group addons',
    'extras' => 'Extras',
    'swap_free' => 'Swap Free',
    'update_account_leverage' => 'Change Leverage',
    'withdrawal' => 'Withdrawal Request',
    'open_positions' => 'Open Positions',
    'employee' => 'Employee',
    'potential_leads' => 'Potential Leads',
    'cronjob_configurations' => 'Cronjob Configurations',
    'drivers_1' => 'Front Driver\'s License',
    'drivers_2' => 'Back Driver\'s License',
    'has_assigned_view_ams' => 'Has assigned other sales members',
    'account_managers' => 'Account Managers',
    'has_relations' => 'Has Assigned Applications',
    'total_accounts' => 'Number of accounts',
    'number_of_new_ftd' => 'Number of FTDs',
    'number_of_active_ibs' => 'Active Partners',
    'number_of_active_retails_accounts' => 'Active retails',
    'deposits' => 'Deposits',
    'traded_lots' => 'Traded Lots',
    'lot' => 'Lot',
    'leads_widget_report' => 'Leads Report',
    'lead_has_application' => 'No referral link for this lead because he already has an application',
    'show_summery' => 'Show Summery',
    'number_of_active_retails_note' => 'Number of active retail individual accounts who deposited or made a trade for the past 90 days',
    'number_of_active_partner_note' => 'Number of active referred accounts who deposited or made a trade for the past 90 days',
    'usd_amount' => 'USD Amount',
    'partners_portal' => 'Partners Portal',
    'my_documents' => 'My Documents',
    'my_offers' => 'My Offers',
    'group_id' => 'Group ID',
    'bad_request' => 'Bad Request',
    'aliases' => 'Aliases',
    'comma_separated' => 'Comma Separated',
    'country_number' => 'Country Number',
    'app_offers' => 'App. Offers',
    'device_type' => 'Device Type',
    'device_family' => 'Device Family',
    'isp' => 'ISP',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'invalid_group_number' => 'Invalid group number notation. The MT4 group number must be numeric, while the MT5 group number should commence with the word "real"',
    'request_upload_document_required' => 'Request Document Required',
    'request_upload_document' => 'The account type you selected requires a document thats provided by your account manager',
    'view_only' => 'View Only',
    'export_app_offer_tree' => 'Export App Offer Tree',
    'canot_login_as_user' => 'You can not login as this user',
    'trading_servers' => 'Trading Servers',
    'trading_server_limit' => 'Limit',
    'create_trading_server' => 'Create Trading Server',
    'add_row' => 'Add Row',
    'prefix' => 'Prefix',
    'trading_server' => 'Trading Server',
    'mt_group_has_accounts_error' => 'This MetaTrader group has mt accounts, you can not change his trading server .',
    'cant_delete_trading_server' => 'You can not delete this trading server, it has mt groups',
    'connection_name' => 'Connection Name',
    'database_name' => 'Database Name',
    'database_username' => 'Database Username',
    'database_password' => 'Database Password',
    'database_port' => 'Database Port',
    'configurations_for' => 'Configurations for (:value))',
    'configs' => 'Configs',
    'server_name' => 'Server Name',
    'trading_server_configs' => 'Trading Server Configs',
    'trading_server_config' => 'Trading Server Config',
    'account_is_hidden' => 'The MetaTrader Account is Hidden!',
    'zain_cash_iraq' => 'Zain Cash Iraq',
    'wallet_after_trans' => 'Wallet Balance After Transaction',
    'otp_has_sent' => 'OTP has been sent to your mobile number',
    'iqd_amount' => 'Amount in IQD',
    'iqr_exchange_rate' => '1 USD = :exchange_rate IQD',
    'exchangers' => 'Exchangers',
    'oauth_clients' => 'Third Parties',
    'secret' => 'Secret',
    'third_party' => 'Third Party',
    'personal_access_client' => 'Personal Access Client',
    'password_client' => 'Password Client',
    'show_oauth_client' => 'Show OAuth Client',
    'edit_oauth_client' => 'Edit OAuth Client',
    'oauth_client_config_keys' => 'OAuth Client Config Keys',
    'oauth_client' => 'Third Party',
    'show_oauth_client_config_key' => 'Show OAuth Client Config Key',
    'oauth_client_config_key' => 'OAuth Client Config Key',
    'meta_trader_dealer' => 'Meta Trader Dealer',
    'private_key' => 'Private Key',
    'public_key' => 'Public Key',
    'has_payment_flagged' => 'Has Flagged',
    'commission_system' => 'Commission System',
    'referred' => 'Referred',
    'not_referred' => 'Not Referred',
    'has_ingot_card' => 'Has INGOT Card',
    'application_status' => 'Application Status',
    'active' => 'Active',
    'application_flags' => 'App. Flags',
    'please_add_your_gt_card_info' => 'Please Request / Activate your INGOT Card',
    'market_analysis_disclaimer' => 'DISCLAIMER: The information and data provided herein are general in nature, intended strictly for informational purposes, and are proprietary to Autochartist Limited. This information does not take into account your personal circumstances, financial situation, or needs. Before deciding to trade, please ensure you understand the risks involved and consider your level of experience. Trading leveraged products involves a high level of risk and can result in significant losses. Therefore, it may not be suitable for all investors.',
    'unable_to_request_a_card' => 'To request your INGOT card, please <a class="dashed-border" href=":url">verify your profile</a>.',
    'jordan' => 'Jordan',
    'kenya' => 'Kenya',
    'australia' => 'Australia',
    'seychells' => 'Seychells',
    'withdraw_with_no_deposit' => 'Withdraw with no deposit',
    'rec_req_amount' => 'Amount (Req/Rec)',
    'rec_req_amount_note' => 'This column indicates the requested amount when a client makes a deposit and the amount transferred when a client withdraws.',
    'events_count' => 'Events Count',
    'last_use_date' => 'Last Use Date',
    'event_number' => '{0,1} :count event|[2,*] :count events',
    'rate_ticket' => 'Rate The Ticket',
    'trading-signals' => 'Trading Signals',
    'company-page' => 'Company Page',
    'earnings-cal' => 'Earnings Calendar',
    'heatmap-ingot-stocks' => 'Stock Heatmap',
    'screener' => 'Stock Screene',
    'payment_execution_time' => 'Payment Execution Time',
    'send_qr_in_email' => 'Send QR in email',
    'cellxpert_id' => 'Cellxpert ID',
    'display_only_no_provinces' => 'Display Countries does not have provinces',
    'bid' => 'Bid',
    'not_synced' => 'Not Synced',
    'migration' => 'Migration',
    'migration_success' => 'Migration Success',
    'migrate' => 'Migrate',
    'data_migration' => 'Data Migration',
    'migrate_app_groups_desc' => 'Migrate the app available groups to the new group.',
    'agent-corporate_account' => 'Corporate Agent Account',
    'retail_team' => 'Retail',
    'members' => 'Members',
    'flag_text' => 'Flag Text',
    'class' => 'Class',
    'with_hidden' => 'With Hidden/Disabled',
    'check_client' => 'Include All Clients Under Agent',
    'all_client' => 'All Clients',
    'add_flag_per' => 'Add Flag Per Country, Payment, Entity',
    'flag_per' => 'Other Flag',
    'all_flag_per' => 'Flag Per Country, Payment, Entity',
    'add_flag_transaction' => 'Add Flag Transaction',
    'flag_transaction' => 'Flag Transaction',
    'transaction_flags' => 'Transaction Flags',
    'app_available_groups_migration_note' => 'You can select multiple app-available groups for migration, delete existing groups, or assign new groups to a specific agent. When adding a new group to an agent, the groups will be allocated to clients in accordance with the group regulation entity.',
    'groups_migration_note' => 'You can select multiple Groups to be migrated their accounts to the new group.',
    'delete_connected_app_groups' => 'Delete the app available groups',
    'nothing' => 'Nothing',
    'options' => 'Options',
    'more' => 'More',
    'add_held_amount' => 'Add Held Amount',
    'disable_withdrawals' => 'Disable Withdrawals',
    'disable_automation' => 'Disable Automation',
    'enable_withdrawals' => 'Enable Withdrawals',
    'enable_automation' => 'Enable Automation',
    'disabled_application' => 'Application is Disabled',
    'held_transactions' => 'Held Transactions',
    'disabled_features_list' => 'Disable Account Log',
    'cellxpert' => 'Cellxpert',
    'cellxpert_migration_note_1' => 'When you click the "Migrate" button, the following actions will take place:',
    'cellxpert_migration_note_2' => 'The referral link type will switch to "Single.',
    'cellxpert_migration_note_3' => 'The connection between the agent account and MetaTrader groups will be disconnected.',
    'cellxpert_migration_note_4' => 'The chosen groups will be included as shared groups for the agent.',
    'cellxpert_migration_note_5' => 'All referred users will have access to the app with the selected groups.',
    'cellxpert_migration_note_6' => 'The agent will be informed of their Cellxpert system username and password.',
    'cellxpert_application' => 'Cellxpert Application',
    'held_amount' => 'Held Balance',
    'add_held_trans' => 'Add Held Transaction',
    'bank_currency_other_note' => 'If your bank account\'s currency is not included in this list, then the amount will be transferred in USD with a currency conversion applied. It\'s essential to ensure that your bank accepts transfers in USD',
    'agent_type' => 'Agent Type',
    'universities' => 'Universities',
    'university' => 'University',
    'university_leads' => 'University Leads',
    'id_type' => 'ID Type',
    'university_id_number' => 'University ID Number',
    'id_image' => 'ID Image',
    'university_id_image' => 'University ID Image',
    'promo_code' => 'Promo Code',
    'causer_name' => 'Causer Name',
    'university_name' => 'University Name',
    'has_no_accounts' => 'Has No Accounts',
    'has_no_av_groups' => 'Has No App. Available Groups',
    'get_the_promotion' => 'Get the promotion',
    'enrolled_promotion' => 'Promotion Already Enrolled',
    'successfully_enrolled_promotion' => 'The promotion has been enrolled successfully.',
    'promotion_terms_conditions' => 'by ticking the box i acknowledge through my electronic approval of all <a target="_blank" href=":TERMS_CONDITIONS" class="dashed-border">terms & conditions</a>',
    'apply_contracts_chnages_to_same_type' => 'Apply the contract specifications changes to the same account type and category of contracts',
    'contract_specs' => 'Contract Specifications',
    'junior_execution_time' => 'Junior Execution Time',
    'senior_execution_time' => 'Senior Execution Time',
    'hedging_execution_time' => 'Hedging Execution Time',
    'agent_activity_report' => 'Agent Activity Report',
    'activity_agent_report' => 'Activity Agent Report',
    'number_of_reg_acco' => 'Reg. accounts',
    'number_of_reg_app' => 'Reg. Apps',
    'number_of_traded' => 'Traded Lots',
    'owned_group' => 'Owned Group',
    'creation_date' => 'Creation Date',
    'include_name_parts' => 'Include Full Name in 4 Parts',
    'm-pesa-deposit-note' => 'Please note that the M-Pesa deposit service is only available for clients who have an updated SIM Card that has a small sim size and thus needs an update. If you are unable to use this service, please contact your account manager for assistance.',
    'crypto_internal_transactions' => 'Crypto Internal Transactions',
    'verify_wallet_address' => 'For enhanced security, you will be redirected to deposit an automatically generated specified amount, which will be slightly less than your intended deposit :attribute USD.',
    'or_add_new_financial_info_wallet' => 'Or Add new :payment_type Wallet Address',
    'spot_or_future' => 'Spot or Future',
    'unheld' => 'Release Hold Amount',
    'first_name_en' => 'First Name (English)',
    'second_name_en' => 'Second Name (English)',
    'third_name_en' => 'Third Name (English)',
    'last_name_en' => 'Last Name (English)',
    'first_name_ar' => 'First Name (Arabic)',
    'second_name_ar' => 'Second Name (Arabic)',
    'third_name_ar' => 'Third Name (Arabic)',
    'last_name_ar' => 'Last Name (Arabic)',
    'response_status' => 'Response Status',
    'show_third_party_log' => 'Show Third Party Log',
    'request_id' => 'Request ID',
    'request_body' => 'Request Body',
    'request_headers' => 'Request Headers',
    'third_party_logs' => 'Third Party Logs',
    'term' => 'Term',
    'definition' => 'Definition',
    'show_glossary_term' => 'show glossary term',
    'edit_glossary_term' => 'edit glossary term',
    'create_glossary_term' => 'create glossary term',
    'glossary_terms' => 'Glossary Terms',
    'active_clients' => '# Active Clients',
    'inactive_clients' => '# In-Active Clients',
    'retention_kpis' => 'Retention KPIs ',
    'acquisition_kpis' => 'Acquisition KPIs ',
    'registered_app' => 'Registered Apps',
    'first_time_deposits' => 'FTDs ',
    'first_time_deposits_amount' => 'FTDs Amount ',
    'cash_accounts' => 'Cash Accounts',
    'not_enough_amoun_in_free_margin' => 'Not enough amount in free margin',
    'mt_id' => 'Meta. ID',
    'has_transactions' => 'Has Transactions',
    'check_item' => 'Check Item',
    'under_cx_user' => 'Under Cellxpert Affiliate',
    'verify_financial_information' => 'Verify Financial Information',
    'require_verification' => 'Require Verification',
    'financial_information_verification' => 'To deposit, you must verify your <a class="dashed-border" href=":url">financial information</a>',
    'login_time' => 'Login Time',
    'total_sessions' => 'Total Sessions',
    'mt_account_country' => 'MT Account Country',
    'exchange' => 'Exchange',
    'exchanger' => 'Exchanger',
    'edit_exchanger' => 'Edit Exchanger',
    'show_exchanger' => 'Show Exchanger',
    'receiver_email' => 'Receiver Email',
    'check_email' => 'Check Email',
    'amount_usd' => 'Amount (USD)',
    'insufficient_balance' => 'Insufficient Balance!',
    'wrong_email' => 'The Email is invalid!',
    'exchange_success_message' => 'The amount has transferred successfully.',
    'exchange_success_message_details' => 'The amount has transferred successfully, For more details please contact your Exchanger :CONTACT_DETAILS',
    'exchanger_deposit_funds' => 'Exchanger Deposit Funds',
    'contact_details' => 'Contact Details',
    'your_changes_has_been_submitted_successfully' => 'Your changes have been submitted successfully , please wait for the compliance approval .',
    'signed_legal_docs' => 'Signed Legal Documents',
    'import_corporate_events_note' => 'You can use the following sample for CSV sheet data entry. <a class="dashed-border" target="_blank" href=":url">Corporate Events</a>',
    'job_retried_successfully' => 'Job retried successfully',
    'error_retrying_job' => 'Error retrying job!',
    'retry_job' => 'Retry Job',
    'eligibile_for_manaf_but_not_synced' => 'Eligible for MANAF but not synced',
    'from_account_number' => 'From Account Number',
    'to_account_number' => 'To Account Number',
    'own_wallet' => 'Own Wallet',
    'exchanger_wallet' => 'Exchanger Wallet',
    'acknowledge_payments' => 'I acknowledge and agree to transfer the adjusted amount of <b class="text-danger">:from_amount</b> instead of the originally specified <b class="text-danger">:origin_amount</b> to facilitate quick identification by INGOT Brokers.',
    'article_date' => 'Article Date',
    'successfully_reactivated' => 'Successfully Reactivated',
    'invalid_account' => 'Invalid Account',
    'expired_token_or_reactivated_account' => 'Token has expired or account has been reactivated',
    'hide_from_clients' => 'Hide From Clients',
    'show_to_clients' => 'Show To Clients',
    'you_cannot_perform_this_action_while_impersonating' => 'You cannot perform this action while impersonating another user.',
    'recorded_video' => 'Recorded video',
    'security_preferences' => 'Security Preferences',
    'sms' => 'SMS',
    'authenticator' => 'Authenticator',
    'app' => '2FA App',
    'security_methods' => 'Security Methods',
    'failed_to_send_otp' => 'Failed to send OTP. Please try again later or reach out to customer support for assistance.',
    'failed_to_resend_otp' => 'Failed to resend OTP, Please try again after :seconds seconds.',
    'app_verification' => '2FA Verification',
    'auth_vis' => 'use other methods',
    'resend_otp_info' => 'Click here to resend OTP!',
    'authenticate_using' => 'Authenticate using',
    'set_as_primary' => 'Set as Default',
    'enter_otp' => 'Enter OTP',
    'send_sms_phone' => 'Send SMS code to phone number',
    'send_otp_to_email' => 'Send OTP Code to your email',
    'generate_code' => 'Send OTP Code',
    'enable_2fa_title' => 'Enhance Your Account Security!',
    'enable_2fa_message_1' => 'Hi there! We noticed you haven’t enabled Two-Factor Authentication (2FA) for your account yet. Activating 2FA adds an extra layer of security, making it much harder for unauthorized users to access your information. Setting it up only takes a minute, but it can make a big difference in keeping your account secure.',
    'enable_2fa_message_2' => 'Setting up only takes a minute, but it can make a big difference in keeping your account secure. to enable it, please <a href=":URL">click here</a>.',
    'order_updated_successfully' => 'Order updated successfully',
    'generate_bill_number' => 'Generate eFAWATEERcom Bill',
    'triels_number' => '{0,1} :count trial|[2,*] :count trials',
    'proof_of_identification' => 'Proof of Identification',
    'accepted_identification' => 'Accepted identification includes passport, ID card, drivers license, or residence permit.',
    'add_missing_transaction' => 'Add Missing Transaction',
    'efawateercom_success' => ':AMOUNT :CURRENCY now is invoiced for bill number :BILL_NUMBER.',
    'show_on_website' => 'Show on Website',
    'exchange_number' => 'Exchange Number',
    'exchanger_name' => 'Exchanger Name',
    'authenticate_using_app_description' => 'Use an Authenticator App for enhanced security and faster access. This method requires you to download a free authenticator app on your mobile device. Once installed, you will scan a QR code or enter a setup key provided by our CRM. The app will then generate a unique, temporary code every 30 seconds that you will use to log in, alongside your password. This method does not depend on cell service or internet access once set up, making it reliable even in areas with poor connectivity.',
    'authenticate_using_email_description' => 'Choose Email verification for a simple and accessible option. Whenever you log in, a unique verification code will be sent to your registered email address. Check your inbox (and the spam folder, just in case) for the code, and enter it on the login page along with your password. This method requires access to your email account, so it\'s essential to ensure your email service is reachable whenever you need to log in.',
    'authenticate_using_sms_description' => 'OTP for SMS-based verification for straightforward, on-the-go security. With this method, you will receive a text message containing a unique code on your registered mobile phone number each time you attempt to log in. Enter this code along with your password to access your account. This method requires a mobile network signal to receive text messages.',
    'calculator' => 'Calculator',
    'assign_application' => 'Assign Application',
    'bulk_assign_sales' => 'Bulk Assign - Sales',
    'bulk_assign_app_ids' => 'Bulk Assign - App. IDs',
    'app_ids' => 'Application IDs',
    'display_active_only' => 'Display active only',
    'academic_year' => 'Year of Study',
    'university_college' => 'University College',
    'college_name' => 'School',
    'colleges' => 'Colleges',
    'sync_accounts' => 'Sync accounts to the Portal',
    'sync' => 'Sync',
    'phone' => 'Phone',
    'target' => 'Target',
    'sent' => 'Sent',
    'not_accepted' => 'Not Accepted',
    'bsb_code_tip' => 'The BSB (Bank State Branch) code is a six-digit number, it is formatted as XXX-XXX, where the first three digits represent the bank and the last three digits identify the specific branch',
    'complaint_history' => 'Complaint History',
    'complaint_number' => 'Complaint No.',
    'complaint_description' => 'Complaint Description',
    'date_issue' => 'Date Issue',
    'complaint_rate' => 'Complaint Rate',
    'edit_complaint' => 'Edit Complaint',
    'show_complaint' => 'Show Complaint',
    'is_the_complaint_resolved' => 'Is the complaint resolved?',
    'support_tickets' => 'Tickets',
    'support_complaint' => 'Support Complaint',
    'complaint' => 'Complaint',
    'create_complaint' => 'Create Complaint',
    'max_active_complaint' => 'You have reached your limit of :COMPLAINT_COUNT opened complaints under review. Our team is currently investigating and will get back to you as soon as possible.',
    'complaint_created_successfully' => 'Complaint created successfully',
    'complaint_reply_created_successfully' => 'Complaint reply created successfully',
    'rate_complaint' => 'Rate Complaint',
    're_open_complaint' => 'Re-Open Complaint',
    'complaint_details' => 'Complaint Details',
    'pending_complaints' => 'Pending Complaints',
    'latest_complaints' => 'Account Support Complaints',
    'is_default' => 'Is Default',
    'covered_mt_accounts' => 'Covered MT Accounts',
    'covered_mt_accounts_desc' => 'MT Accounts to be Covered',
    'emails_history' => 'Emails History',
    'opened' => 'Opened',
    'opened_at' => 'Opened At',
    'email_failed' => 'Failed',
    'delivered' => 'Delivered',
    'delivered_at' => 'Delivered At',
    'email_history' => 'Email History',
    'view_email' => 'View Email',
    'dynamic_email_templates' => 'Auto-Email Templates',
    'dynamic_email_contents' => 'Auto-Email Contents',
    'dynamic_email_tags' => 'Auto-Email Tags',
    'mail_key' => 'Mail Key',
    'tags' => 'Tags',
    'tag' => 'Tag',
    'send_test_email' => 'Send me a test email',
    'set' => 'set',
    'number_of_open_times' => 'Number of Open Times',
    'qr_code' => 'Qr Code',
    'mail_key_info' => 'The mail key is used to identify the email template in the system. It should be unique and not used by any other template. You can\'t edit it .',
    'freeze_wthdrawals' => 'After submitting a deposit, you will be eligible to request a withdrawal after 24 hours.',
    'create_a_new_version' => 'Create a new version',
    'create_a_new_version_description' => 'When checked, a new pending version will be created , once you release it, the current version will be replaced with the new one.',
    'changes_description' => 'Changes Description',
    'whats_changed' => 'What\'s Changed ?',
    'versions' => 'Versions',
    'old_file' => 'Old File',
    'new_file' => 'New File',
    'release' => 'Release',
    'released_at' => 'Released At',
    'updated_failed' => 'Updated Failed',
    'affected_users' => 'Affected Users',
    'affected_users_description' => 'The users who will be affected by this change , may be the number is changed after the release.',
    'update_to_our' => 'Update to our',
    'we_have_revised_our' => 'We have revised our',
    'please_ensure_you_informed' => 'Please ensure that you are informed about these changes and confirm your acknowledgment and acceptance of the updates to our legal document.',
    'if_you_click_accept' => 'If you click <b> “Accept” </b>, then you confirm that you have read, understood, and accepted the update on INGOT Brokers’ legal documents.',
    'accepting_this_update_is_required' => 'Accepting this update is required if you wish to continue with INGOT.  If you do not accept this update, you may contact our support.',
    'iqr_amount' => 'Amount in IQD',
    'action_required_new_form_version' => 'Action Required: Update to our :form_name',
    'phone_numbers' => 'Phone Numbers',
    'my_complaints' => 'Complaints',
    'swap_long' => 'Swap Long',
    'swap_short' => 'Swap Short',
    'min_spread' => 'Min Spread',
    'markup_spread' => 'Markup Spread',
    'commission_wallet' => 'Commission Wallet',
    'instrument_already_exists_note' => 'This instrument already exists on our system; please reject the order and add a comment to the user.',
    'fraud_dashboard' => 'Fraud Dashboard',
    'reference_table_name' => 'Reference Table Name',
    'hide_slider_based_on_order' => 'Make it hidden if has order',
    'hint_to_hide_slider_based_on_order' => 'The slider will be hidden if the user has an order of the chosen type with a \'pending\' or \'approved\' status',
    'whitelabel_trading_platform' => 'Whitelabel Trading Platforms',
    'unverified' => 'Unverified',
    'sum' => 'Sum',
    'existing_financial_information' => 'The Financial Information is already in use',
    'second_name' => 'Second Name',
    'ib_restrictions' => 'Partner Restrictions',
    'ib_settings' => 'Partner Restrictions',
    'show_restriction' => 'Show The Restrictions',
    'client_permissions' => 'Client Permissions',
    'client_permissions_note' => 'Select the features you want to enable it for the clients under the selected IB.',
    'ib_permissions_note' => 'Select the features you want to enable it for selected IB.',
    'verify_wallet_amount' => 'Please deposit the following amount to activate the wallet for deposits & withdrawals',
    'wallet_deposit_to' => 'Deposit to the following wallet',
    'crypto_placeorder_note' => 'To fund your account with the equivalent of <b class="text-danger">:from_amount :from_currency</b><br/>You must send the exact amount of <b class="text-danger">:to_amount :to_currency</b> to <b>INGOT’s wallet address</b> below',
    'name_as_per_id' => 'Name as per Id',
    'withdraw_type' => 'Withdraw Type',
    'deposit_type' => 'Deposit Type',
    'switch_to_aff_website' => 'In order to log in, please login with the user registered domain.',
    'tron_usdt_wallet_support_note' => 'I understand that INGOT Brokers receive USDT through the Tron network (TRC20) only. Payment with tokens, smart contracts, ERC20, or HRC20 is unsupported and will not be recovered. I also acknowledge that INGOT Brokers is not responsible to refund payments made through any such networks.',
    'tether_usdt_wallet_support_note' => 'I understand that INGOT Brokers receive USDT through the Ethereum Blockchain network (ERC20) . Payment with tokens, smart contracts, TRC20, or HRC20 is unsupported for this payment method and will not be recovered. I also acknowledge that INGOT Brokers is not responsible to refund payments made through any such networks.',
    'include_content_in_export' => 'Show Ticket Content When Exporting',
    'last_deposit' => 'Last Deposit',
    'last_withdraw' => 'Last Withdraw',
    'base_currency' => 'Base Currency',
    'digits' => 'Digits',
    'profit_currency' => 'Profit Currency',
    'copy_trading_verified_restriction' => 'To initiate copy trading, kindly ensure that you have verified your account and created a real trading account.',
    'copy_trading_real_mt_account_restriction' => 'To initiate copy trading, kindly ensure that you have created a real trading account.',
    'ingot_payid' => 'INGOT PayID',
    'payid' => 'PayID',
    'deactivate_offer' => 'Deactivate',
    'black_listed_risk' => 'Black List',
    'black_listed_risk_message' => 'We are unable to onboard you at the moment due to our KYC policies. Should this change in the future, we will notify you and assist you through the process.',
    'app_available_offer' => 'App Available Offer',
    'change_app_available_offer' => 'Change App Available Offer',
    'app_offer' => 'App Offer',
    'offer_display_name' => 'Offer Display Name',
    'redeposits' => 'Re-deposits',
    'application_created_date' => 'Application Created At',
    'most_traded_symbols' => 'Most traded symbols',
    'total_lots' => 'Total Lots',
    'client_retention' => 'Client Retention',
    'application_creation_period' => 'Application Creation Period',
    'application_id' => 'Application ID',
    'years_old' => 'Years old',
    'cant_change_leverage' => 'In order to access and utilize this feature, it is necessary to fund your account',
    'within_kpi' => 'Within KPI',
    'open_hybrid_account' => 'Open a Hybrid Account',
    'hybrid' => 'Hybrid',
    'last_traded_on_before' => 'Last Traded On/Before',
    'hybrid_account' => 'Hybrid account',
    'no-feedback' => 'No Feedback',
    'mark_as_done' => 'Mark as Done',
    'delaied_tx_note' => 'This transaction is stuck for more than 10 minutes, if its executed manually, please <a class="btn btn-sm text-white btn-secondary confirmation_dialog ml-05" data-url=":url">click here</a>',
    'tx_is_not_found' => 'The transaction either does not exist or has been processed',
    'scenario_group' => 'Scenario Group',
    'default_balance' => 'Default Balance',
    'default_balance_note' => 'The default balance will be used in registration form for demo accounts',
    'default_on_register' => 'Default when Open a new account',
    'default_leverage_note' => 'The default leverage will be used in registration form for demo accounts',
    'order_documents' => 'Order Documents',
    'fix_balance' => 'Fix Balance',
    'journal' => 'Journal',
    'cellxpert_system' => 'Cellxpert System',
    'subscribed' => 'Subscribed',
    'not_subscribed' => 'Not Subscribed',
    'exchange_rates' => 'Exchange Rates',
    'fixed_exchange_rates' => 'Fixed Exchange Rates',
    'show_exchange_rate' => 'Show Exchange Rate',
    'create_exchange_rate' => 'Create Exchange Rate',
    'client_under_partner_rest_note' => 'The application has been registered under a restricted partner, which may impose certain restrictions on key functionalities, such as opening a trading account, deposits, withdrawals, fund transfers and transfers to the agent wallet.',
    'virtual' => 'Virtual',
    'virtual_ingot_card' => 'In order to activate the virtual card, you should <a target="_blank" href=":URL">download the PDF</a>, fill it out, sign it, and then upload it.',
    'virtual_card_doc' => 'Virtual card document',
    'signed_virtual_card_doc' => 'Signed Virtual card document',
    'do_u_want_to_get_v_card' => 'Would you like to activate the virtual card?',
    'province_number' => '{0,1} :count province|[2,*] :count provinces',
    'other_flag' => 'Other Flag',
    'deposit_withdrawal_volumes' => 'Deposit & Withdrawal Volumes',
    'deposit_transactions_frequency' => 'Deposit transactions frequency',
    'locations_relating_to_client' => 'Locations relating to the client',
    'credit_cards_used' => 'Credit Cards Used',
    'deposit_attempts_per_day' => 'Deposit attempts per day',
    'rejected_transactions' => 'Rejected Transactions',
    'ref_id' => 'REF.ID',
    'method' => 'Method',
    'rej_reason' => 'Rejection Reason',
    'fraud_risk_rating' => 'Fraud Risk Rating:',
    'fraud_risk_score' => 'Fraud Risk Score',
    'account_not_verified' => 'Not verified',
    'export_pdf' => 'Export PDF',
    'flag_account' => 'Flag Account',
    'freeze_account' => 'Freeze Account',
    'tx_rejections' => 'Tx Rejections',
    'trading_activity' => 'Trading Activity',
    'client_locations' => 'Client Locations',
    'last_withdrawal' => 'Last Withdrawal',
    'reg_ip_address' => 'Reg. IP Address',
    'registered_country' => 'Registered Country',
    'country_fraud_rating' => 'Country Fraud Rating',
    'unfreeze' => 'Unfreeze Account',
    'account_frozen_successfully' => 'Account frozen successfully',
    'account_unfrozen_successfully' => 'Account unfrozen successfully',
    'flagged_account' => 'Flagged Account',
    'remove_held_trans' => 'Remove Held Transaction',
    'promotion_agents_label' => 'Apply promotion to clients under selected agents (if no agents are selected, the promotion will be available to all clients)',
    'targeted_countries' => 'Targeted Countries',
    'targeted_entities' => 'Targeted Regulation Entities',
    'targeted_citizenship' => 'Targeted Citizenship',
    'excluded_citizenship' => 'Excluded Citizenship',
    'promotion_name' => 'Promotion Name',
    'promotion_image' => 'Promotion Image',
    'terms_and_conditions_pdf' => 'Terms & Conditions (PDF)',
    'show_promotion' => 'Show Promotion',
    'edit_promotion' => 'Edit Promotion',
    'check_list' => 'Check List',
    'check_list_name' => 'Check List Name',
    'create_check_list' => 'Create Check List',
    'mandatory' => 'Mandatory',
    'requirement' => 'Requirement',
    'checklist_item_not_completed' => 'Checklist item not completed',
    'mail_notifiers' => 'Mail Notifiers',
    'create_mail_notifier' => 'Create Mail Notifier',
    'update_mail_notifier' => 'Update Mail Notifier',
    'metatrader_id' => 'Agent Number',
    'residuals' => 'Residuals',
    'deposits_per_payment' => 'Deposits per payment method',
    'withdrawals_per_payment' => 'Withdrawals per payment method',
    'are_you_depositing_from' => 'Are you depositing from: ',
    'wallet_source' => 'Wallet Source',
    'no_data' => 'No Data',
    'aml_flag' => 'AML Flag',
    'age_group' => 'Age Group',
    'all_age_group' => 'All Age Group',
    'active_from_date' => 'Users Active Date From',
    'active_to_date' => 'Users Active Date To',
    'display_account_manager_widget' => 'Display Account Manager Widget',
    'account_manager_card' => 'Account Manager Widget',
    'preferences' => 'Preferences',
    'manaf_system' => 'MANAF System',
    'synced' => 'Synced',
    'cx_affiliate_id' => 'Cellxpert Affiliate ID',
    'manaf_user_id' => 'MANAF User ID',
    'manaf_bank_lookups' => 'Manaf Bank Lookups',
    'bank_name_en' => 'Bank Name (English)',
    'bank_name_ar' => 'Bank Name (Arabic)',
    'create_manaf_lookup' => 'Create Manaf Lookup',
    'show_manaf_lookup' => 'Show Manaf Lookup',
    'manaf_bank_account_lookups' => 'Manaf Bank Account Lookups',
    'create_manaf_bank_account_lookups' => 'Create Manaf Bank Account Lookup',
    'show_manaf_bank_account_lookups' => 'Show Manaf Bank Account Lookup',
    'contact_verified' => 'Contact Verified',
    'view_on_map' => 'View on Map',
    'active_only_note' => 'You will need to select the (Active Date From) and (Active Date To) to be able to filter the data!',
    'high_risk_countries' => 'High Risk Countries',
    'with_no_aml_users' => 'With No AML Users',
    'logins_with_same_ip' => 'Logins with same IP',
    'never_logged_in_for_seven_days' => 'Not Logged In For The Last 7 Days',
    'user_doesnt_have_wallet' => 'User doesn\'t have wallet',
    'user_restrictions' => 'User Restrictions',
    'create_new_user_restriction' => 'Create New User Restriction',
    'user-restrictions' => 'User Restrictions',
    'blocked_actions' => 'Blocked Actions',
    'sales_summary' => 'Sales Summary',
    'assigned_applications' => 'Assigned Applications',
    'active_applications' => 'Active Apps.',
    'inactive_applications' => 'Inactive Apps.',
    'used_payments' => 'Used Payments',
    'avg_withdrawals' => 'Avg Withdrawals',
    'avg_withdrawals_desc' => 'Total Withdrawals / Number of transactions',
    'avg_deposits' => 'Avg Deposits',
    'avg_deposits_desc' => 'Total Deposits / Number of transactions',
    'used_payments_desc' => 'The payment methods used by clients assigned to each sales person',
    'total_deposits_transactions' => 'Deposit Transactions',
    'active_apps_for_90_day' => 'Total applications who logged in to the portal within the past 90 days.',
    'inactive_apps_for_90_day' => 'Total applications who never logged in to the portal within the past 90 days.',
    'total_deposits_in_used' => 'Total deposits in USD',
    'total_withdrawals_in_used' => 'Total withdrawals in USD',
    'trading_accounts_desc' => 'Number of registered real accounts by applications assigned to each sales person',
    'assigned_applications_desc' => 'Number of assigned applications per sales person',
    'total_apps' => 'Total Apps',
    'individuals' => 'Individuals',
    'ftd' => 'FTDs',
    'ftd_desc' => 'Total and Sum of First Time Deposits',
    'demos' => 'Demos',
    'agents' => 'Agents',
    'apps_number' => '{0,1} :count App|[2,*] :count Apps',
    'transactions_number' => '{0,1} :count Transaction|[2,*] :count Transactions',
    'total_transaction' => 'Total Transactions',
    'sum_transaction' => 'Sum Transactions',
    'accounts_number' => '{0,1} :count Account|[2,*] :count Accounts',
    'has_covering_login' => 'Has Covering Login',
    'confirm_disclaimer' => 'Confirm Disclaimer',
    'notify_user_tip' => 'Notify User By Email',
    'e_wallet_report_description' => 'The E-wallet report provides a transactions summary for all active clients who has E-Wallet. (Ex: Global, AU, Africa clients)',
    'mt_wallet_report_description' => 'The MetaTrader wallet report provides a transactions summary for all active clients who has MetaTrader Wallet. (Ex: JSC clients)',
    'metatrader_manager' => 'Metatrader Manager',
    'accepted_users' => 'Accepted Users',
    'accepted_users_description' => 'The users who accepted the new version.',
    'your_acceptance_of_legal_document_update_subject' => 'Your Acceptance of the Update to our :form_name',
    'cellxpert_commissions' => 'Cellxpert Commissions',
    'num_logins' => '[0,1] :count login|[2,*] :count logins',
    'fee_percentage' => 'Fee Percentage (%)',
    'fee_amount' => 'Fee Amount',
    'failed_at' => 'Failed Date',
    'uuid' => 'UUID',
    'sync' => 'Sync',
    'phone' => 'Phone',
    'target' => 'Target',
    'sent' => 'Sent',
    'is_primary' => 'Is Primary',
    'withdraw_cellxpert_commissions' => 'Cellxpert Commissions Withdrawal',
    'has_2fa' => 'Has 2FA',
    'has_2fa_enabled' => 'Has 2FA Enabled',
    'hide_disabled' => 'Hide disabled apps',
    'notified' => 'Notified',
    'accepted' => 'Accepted',
    'accepted_at' => 'Accepted At',
    'not_notified' => 'Not Notified',
    'not_accepted' => 'Not Accepted',
    'bsb_code_tip' => 'The BSB (Bank State Branch) code is a six-digit number, it is formatted as XXX-XXX, where the first three digits represent the bank and the last three digits identify the specific branch',
    'complaint_history' => 'Complaint History',
    'complaint_number' => 'Complaint No.',
    'complaint_description' => 'Complaint Description',
    'date_issue' => 'Date Issue',
    'complaint_rate' => 'Complaint Rate',
    'edit_complaint' => 'Edit Complaint',
    'show_complaint' => 'Show Complaint',
    'is_the_complaint_resolved' => 'Is the complaint resolved?',
    'support_tickets' => 'Support Tickets',
    'support_complaint' => 'Support Complaint',
    'complaint' => 'Complaint',
    'create_complaint' => 'Create Complaint',
    'max_active_complaint' => 'You have reached your limit of :COMPLAINT_COUNT opened complaints under review. Our team is currently investigating and will get back to you as soon as possible.',
    'complaint_created_successfully' => 'Complaint created successfully',
    'complaint_reply_created_successfully' => 'Complaint reply created successfully',
    'rate_complaint' => 'Rate Complaint',
    're_open_complaint' => 'Re-Open Complaint',
    'complaint_details' => 'Complaint Details',
    'pending_complaints' => 'Pending Complaints',
    'latest_complaints' => 'Account Support Complaints',
    'phone_numbers' => 'Phone Numbers',
    'is_default' => 'Is Default',
    'covered_mt_accounts' => 'Covered MT Accounts',
    'covered_mt_accounts_desc' => 'MT Accounts to be Covered',
    'update_financial_information_reject' => 'Please provide the reason for rejecting the update ',
    'reject_update' => 'Update Rejection ',
    'app_disabled_date' => 'App Disabled Date',
    'resources' => 'Resources',
    'create-account' => 'Create Account',
    'advanced-report' => 'Advanced Report',
    'platforms' => 'Platforms',
    'metatrader-for-windows' => 'MetaTrader for Windows',
    'metatrader-for-mac' => 'MetaTrader for Mac',
    'metatrader-for-web' => 'MetaTrader for Web',
    'metatrader-for-ios' => 'MetaTrader for iOS',
    'metatrader-for-android' => 'MetaTrader for Android',
    'download-for-windows-and-mac' => 'Download for your platform',
    'trade-from-web' => 'Trade From Web',
    'support_ticket' => 'Support Ticket',
    'request_instruments' => 'Request Instruments',
    'open_support_tickets' => 'Open Support tickets',
    'Add_new_request' => 'Add New Request',
    'my_tickets_list' => 'My Tickets',
    'check_our_payment_banking_details' => 'Check Our Payment Banking Details',
    'please_select_a_payment_method' => 'Please select a payment method',
    'partnership_program' => 'Partnership Program',
    'benefits_to_become_agent' => 'Benefits of becoming an INGOT agent',
    'join_to_become_agent' => 'Join us as an agent and earn commissions',
    'submit_request' => 'Submit Request',
    'details_statistics' => 'Details Statistics',
    'no_limits_on_the_commissions' => 'Unlimited Commissions',
    'cash_account' => 'Cash Account',
    'no_cash_account' => 'There is no cash account for this whitelabel on this :tradingPlatformName. Please contact support.',
    'scan_qr_code' => 'Scan QR code',
    'login_title' => 'Welcome to INGOT Portal',
    'login_subtitle' => 'Your personal account management system is simply one step away.',
    'indvidual_title' => 'A few clicks away from creating your account',
    'indvidual_subtitle' => 'Register now, it’s quick!',
    'help_center' => 'Help Center',
    'kpi_dashboard' => 'KPI Dashboard',
    'performance_current_month' => 'Performance this month',
    'avg_number_of_new_ftd' => 'FTD AVG',
    'kpi' => 'KPI',
    'ftd' => 'FTD',
    'fifty_covered_symbol_ids' => 'Symbols Covering 50%',
    'excluded_symbol_ids' => 'Exclude from Coverage',
    'passport_number_non_jod' => 'Passport Number for Non-Jordanian',
    'po_box' => 'P.O.Box',
    'recipient' => 'Are you the real beneficiary of the card?',
    'national_number_gate' => 'National Number',
    'number_id' => 'ID Number',
    'district' => 'District',
    'first_degree' => 'Is the client or one of the client’s first-degree relatives politically exposed?',
    'first_degree_text' => 'Relationship',
    'otp_verification_title' => 'OTP Verification',
    'otp_verification_sub_title' => 'Please enter the 6-digit code from your authenticator app.',
    'my_account' => 'My Account',
    'latest' => 'Latest',
    'personal_settings' => 'Personal Settings',
    'one_more_step_register' => 'You\'re almost there, Just one more step!',
    'joint' => 'Joint',
    'nationality_id' => 'Nationality ID',
    'show_summary' => 'Show Summary',
    'invalid_input_data' => 'invalid data found when processing input',
    'group_entity' => 'Group Entity',
    'bulk_disable_apps' => 'Bulk Disable Apps',
    'bulk_hide_mt_accounts' => 'Bulk Hide MT Accounts',
    'mt_ids' => 'MT IDs',
    'bulk_delete_app_ids' => 'Bulk Delete App. IDs',
    'bulk_delete_app_ids_note' => 'Please note that applications with MetaTrader  accounts will not be deleted.',
    'sms_sender_name' => 'Support SMS Alpha',
    'previous_date' => 'Previous Date',
    'previous_date' => 'Previous Date',
    'covered_mt_accounts_description' => 'Chose clients logins that (:login) should covers it',
    'fifty_covered_symbol_ids_description' => 'Chose symbols that (:login) should cover 50% for',
    'excluded_symbol_ids_description' => 'Chose symbols that (:login) should exclude it from coverage',
    'not_exists' => 'Not Exists',
    'current_data' => 'Current Data',
    'data_at' => 'Data for :date',
    'swap_free_ne_note' => 'This option will remove the row from the account types page.',
    'closing_balance_ne_note' => 'This data presents the closing balance for the selected date.',
    'change_wallet_currency' => 'Change Wallet Currency',
    'current_data_only_note' => 'This option work with the current data only!',
    'egp_w_currency_disclaimer' => 'Disclaimer: Exchange rates are based on the Central Bank of Egypt and may fluctuate. If you choose to have an EGP currency (Egyptian Pound) portal e-wallet , all deposits and withdrawals will be processed using the official Central Bank of Egypt exchange rate and fees may apply. Fees may vary without notification or prior notice. We are not liable for any losses or delays related to exchange rate changes or payment processing.',
    'wallet_currencies' => 'Wallet Currencies',
    'wallet_currencies_payment_notes' => 'The wallet currencies you wish to display the payment methods when a user has it.',
    'location_op_or' => 'Location Operation To (OR)',
    'profile_status' => 'Profile Status',
    'metatrader_status' => 'Metatrader Account Status',
    'has_open_positions' => 'Has Open Positions',
    'reg_users_country' => 'Reg. Users Country',
    'reg_users_citizen' => 'Reg. Users Citizenship',
    'dependency_answer' => 'Dependency Answer',
    'dependency_question' => 'Question dependency',
    'advanced_logic' => 'Advanced Logic',
    'ASIC_answers_advanced_logics_list' => 'ASIC Answers Advanced Logics List',
    'add_advanced_logic' => 'Add Advanced Logic',
    'create_ASIC_answers_advanced_logics' => 'Create ASIC Answers Advanced Logics',
    'edit_ASIC_answers_advanced_logics' => 'Edit ASIC Answers Advanced Logics',
    'base_question' => 'Base Question',
    'new_question' => 'New Question',
    'is_text' => 'Is Text',
    'enter_your_answer' => 'Enter Your Answer',
    'financial_assessment' => 'Financial Assessment',
    'based_on_your_selection' => 'Based on your selection we cannot accept your application',
    'how_satisfied_service' => 'On a scale of 1 to 5, how satisfied are you with our service?',
    'feedback_complaint_rate' => 'We value your feedback. We\'re sorry to hear the resolution wasn\'t ideal. How can we improve? Your suggestions are important to us',
    'incident_date' => 'Incident Date',
    'street_address' => 'Street Address',
    'active_after_deposit' => 'Active After Deposit',
    'min_amount_deposit' => 'Minimum Amount Deposit',
    'first_response_time' => 'First Response Time',
    'download_platform' => 'Download Platform',
    'sub_type' => 'Sub Type',
    'enabled_by_default' => 'Enabled by default',
    'promotion_enabled_by_default_description' => 'Enable this option to make the promotion available to all selected clients. If disabled, the promotion will only be available to specific clients',
    'enabled_promotions' => 'Enabled Promotions',
    'enable_promotions' => 'Enable Promotions',
    'enabled_at' => 'Enabled At',
    'next_steps' => 'Next Steps :',
    'no_effect' => 'No Effect',
    'brief_description' => 'Brief Description',
    'email_banner' => 'Email Banner',
    'email_content_info' => 'This info will appear in the email content',
    'optional_email_banner_info' => 'This image is optional, if you did not upload an image, the default image will be used.',
    'not_traded' => 'Not traded',
    'deposits_and_withdrawals_summary' => 'Deposits & Withdrawals Summary',
    'loading' => 'Loading',
    'net_deposits_dashboard' => 'Net Deposits',
    'deposits_withdrawals_monthly' => 'Deposits And Withdrawals Monthly',
    'deposits_and_withdrawals' => 'Deposits & Withdrawals',
    'net_deposits_per_entity_monthly' => 'Net Deposits Per Entity Monthly',
    'net_deposits_per_entity' => 'Net Deposits Per entity',
    'net_deposits_per_month' => 'Net Deposits Per Month',
    'deposits_vs_withdrawals_per_client' => 'Deposits Vs Withdrawals Per Client',
    'deposits_vs_withdrawals_per_account_manager' => 'Deposits Vs Withdrawals Per Account manager',
    'risk_assessment' => 'Risk Assessment',
    'new_eligibility_assessment' => 'New Eligibility Assessment',
    'has_versions' => 'Has Versions',
    'long_admin_fee' => 'Long admin fee (USD)',
    'short_admin_fee' => 'Short admin fee (USD)',
    'grace_period_days' => 'Grace period (number of days)',
    'not_restricted' => 'Not Restricted',
    'your_password_has_expired' => 'Your current password has expired. To continue using your account, please update your password.',
    'password_must_be_different_from_last_5_passwords' => 'Password must be different from the last 5 passwords you used.',
    'your_password_has_been_changed_successfully' => 'Your password has been changed successfully.',
    'skip_for_now' => 'Skip for now',
    'your_new_password_must_be_different_from_current_password' => 'Your new password must be different from your current password.',
    'add_otp_phone_note' => 'Please enter the <b>:digits</b> digit code we sent to your phone number ending with <b>:phone_number</b>.',
    'your_phone_number_is_invalid' => 'We are unable to send the OTP because your current phone number is incorrect.. please update it by <a target="_blank" href=":url">clicking here</a>.',
    'did_not_receive_otp' => 'Didn\'t receive the code?',
    'resend_otp' => 'Resend OTP',
    'pending_to_be_approved_client' => 'Pending to be approved from the client!',
    'request_sent_for_client approval' => 'A request has been sent to the client for approval.',
    'target_market_determination' => 'Target Market Determination',
    'target_market_determination_question' => 'Target Market Determination Question',
    'questionnaire_for_client_risk' => 'Client information',
    'eligibility_assessment_test' => 'Eligibility Assessment Test',
    'withdraw_require_otp' => 'Require OTP for Withdraw',
    'show_all_data' => 'Show all data',
    'clients_deposit_withdrawal_report' => 'Clients Deposit & Withdrawal Report',
    'first_free_trader' => 'Zero Risk Free',
    'first_free_trader_group_note' => 'If this option is enabled, the first loss on a trade will be refunded up to $100.',
    'client_deposit' => 'Client Deposit',
    'client_withdrawal' => 'Client Withdrawal',
    'client_transfer' => 'Client Transfer',
    'client_acc_creation' => 'Client Acc. Creation',
    'client_transfer_to_ib_wallet' => 'Client Transfer to IB Wallet',
    'agent_internal_transfer' => 'Agent Internal Transfer',
    'agent_transfer_to_client_mt' => 'Agent Transfer to Client MT',
    'agent_transfer_to_client_wallet' => 'Agent Transfer to Client Wallet',
    'limit_notes' => 'Limit Notes',
    'export_mt_data' => 'Include MetaTrader data with export',
    'made_tx_on' => 'Made a Transaction using',
    'current_currency' => 'Current Currency',
    'current_balance' => 'Current Balance',
    'internal_comments' => 'Internal Comments',
    'utm_data' => 'Marketing Data',
    'utm_term' => 'UTM Term',
    'utm_medium' => 'UTM Medium',
    'utm_source' => 'UTM Source',
    'utm_content' => 'UTM Content',
    'utm_campaign' => 'UTM Campaign',
    'include_utm_data' => 'Include Marketing Data',
    'source_name' => 'Source Name',
    'sent_at' => 'Sent At',
    'processed' => 'Processed',
    'soft_bounced' => 'Soft Bounced',
    'hard_bounced' => 'Hard Bounced',
    'spam' => 'Spam',
    'clicked' => 'Clicked',
    'view_content' => 'View Content',
    'attachments' => 'Attachments',
    'no_email_content' => 'Email content is unavailable or exceeds the allowable length.',
    'size' => 'Size',
    'no_attachments' => 'No Attachments',
    'no_groups_available' => 'Agent Does Not Have Groups',
    'has_offers_only' => 'Has offers only',
    'no_groups_available' => 'Agent Does Not Have Groups',
    'has_offers_only' => 'Has offers only',
    'report_to_traction' => 'Report to TRAction',
    'has_traction_group' => 'Has Traction Group',
    'has_recording' => 'Has Recording only',
    'uwallet_need_approved_msg' => 'To proceed with your deposit, please click the Confirm button below. After confirming, open your UWallet mobile app to approve the pending transaction',
    'uwallet_processing_msg_1' => 'Kindly open your UWallet mobile app and approve the transaction from the pending transactions list. ',
    'uwallet_processing_msg_2' => 'Please ensure you keep this window open and complete the process before the transaction expires',
    'uwallet_timeout_msg' => 'Your transaction has timed out. Please try again.',
    'u-wallet' => 'U-Wallet',
    'type_uwallet' => 'Type (U-Wallet)',
    'tapw' => 'Pending Withdrawals',
    'client_area' => 'Client Area',
    'choose_the_platform' => 'please choose the platform',
    'note_swap_page' => 'Swaps and Rollover Note',
    'note_swap_fee_page' => 'Swap-Free Limit and Fee Note',
    'cliq_details' => 'CliQ Details',
    'check_our_cliq_details' => 'Check Our CliQ Details',
    'ingot_cliq' => 'INGOT CliQ',
    'cliq_type' => 'Type (INGOT CliQ)',
    'cliq' => 'CliQ',
    'wallet_type' => 'Wallet Type',
    'torder_count' => '{0,1} :count order|[2,*] :count orders',
    'retail_only' => 'Retail Only',
    'only_available_for_retail' => 'Enable this option to make the promotion available exclusively to retail clients',
    'has_attachments' => 'Has Attachments',
    'refetch_data_from_service' => 'Re-fetch Data From Service',
    'tax_invoices' => 'Tax Invoices',
    'print' => 'Print',
    'create_return_invoice' => 'Create Return Invoice',
    'show_tax_invoice' => 'Show Tax Invoice',
    'show_origin_invoice' => 'Show Origin Invoice',
    'quantity' => 'Quantity',
    'invoice_submitted' => 'Invoice Submitted successfully !',
    'invoice_not_submitted' => 'Could not submit the invoice, please try again later !',
    'return_reason' => 'Return Reason',
    'resubmit' => 'Resubmit',
    'invoice_already_submitted' => 'Invoice already submitted !',
    'is_return_invoice' => 'Is Return Invoice',
    'full_address_for_delivery' => 'Full Address for Delivery',
    'tx_ceated_before_app' => 'This transaction was created before the application creation date!',
    'cron_signature' => 'Signature',
    'commands' => 'Commands',
    'show_command' => 'Show Command',
    'update_command' => 'Update Command',
    'all_quantity_returned' => 'All quantity of origin invoice has been returned!',
    'not_allowed_to_perform' => 'You are not allowed to perform this action!',
    'retry_all_job' => 'Retry All Job',
    'send_otp' => 'Send OTP',
    'verify' => 'Verify',
    'required_for_swap_free_tooltip' => 'Required only for Swap-Free Limit and Fee',
    'verify_phone_number' => 'Verify Your Mobile Number',
    'organic' => 'Organic',
    'complaint_tags' => 'Complaint Tags',
    'on_rate' => 'On Rate',
    'current' => 'Current',
    'active_users' => 'Active Users',
    'otp_max_attempts' => 'You have reached the maximum number of resend attempts. Please try again later.',
    'resend_otp_in' => 'Resend OTP in',
    'residency_permit' => 'residency permit',
    'sync_to_manaf' => 'Sync to MANAF',
    'request_placed_successfully' => 'Your request has been placed successfully!',
    'account_not_eligible' => 'Your account is not eligible for this action!',
    'admin' => 'Admin',
    'has_comments' => 'Has Comments',
    'sender_account_not_active' => 'The sender account is not active!',
    'limit_to_change_leverage' => 'You are not allowed to set the leverage for this account higher than 1::max.',
    'leverage_changed_successfully' => 'Account Leverage changed successfully',
    'yes_instant' => 'Yes (Dynamic)',
    'yes_request' => 'Yes (Request)',
    'disclaimer' => 'Disclaimer',
    'dynamic_leverage_disclaimer_create_account' => 'The requested leverage will apply for example to Major Forex instruments, while other instruments will have leverage adjusted to levels specified in the Leverage Terms and Conditions. <a target="_blank" href=":TERMS_LINK"> <strong><u>Dynamic Leverage</u></strong> </a> will take effect immediately upon approval. By submitting this Leverage Change request, I confirm that I have reviewed, understood, and accept the full set of INGOT Brokers\' legal documents, including the Leverage Change Terms.',
    'dynamic_leverage_disclaimer_request_change_1' => 'Please ensure that there are no open trading positions in the account before submitting the request, as the leverage change will not take effect otherwise. Note that only one subaccount may be granted the maximum leverage, while the conditions of <b> <a target="_blank" href=":TERMS_LINK"> Dynamic Leverage </a> </b> will apply to the remaining subaccounts.',
    'dynamic_leverage_disclaimer_request_change_2' => 'The requested leverage will apply for example to Major Forex instruments, while other instruments will have leverage adjusted to levels specified in the Leverage Terms and Conditions. <b> <a target="_blank" href=":TERMS_LINK"> Dynamic Leverage </a> </b> will take effect immediately upon approval. By submitting this Leverage Change request, I confirm that I have reviewed, understood, and accept the full set of INGOT Brokers\' legal documents, including the Leverage Change Terms.',
    'app_available_group' => 'App Available Group',
    'req_currency' => 'Req. Currency',
    'has_more_5_trading' => 'Has more than 5 trading accounts',
    'there_is_no_trading_server_associated_with_your_account' => 'We can\'t process your request right now , there is no trading server associated with your account!',
    'must_be_same_client' => 'The transfer from and transfer to must be for the same client.',
    'receiver_must_be_under_agent' => 'The receiver must be under the same agent.',
    'has_more_5_trading_note' => 'By selecting a "Only 1 account", users will have the opportunity to open only 1 trading accounts within this group.',
    'limit_mt_opening_acount' => 'Limit Count Opening',
    'limit_mt_opening_acount_note' => 'You cannot open more than one account of the same account type. For more information, please contact Customer Support.',
    'only_one' => 'Only 1 account',
    'restrict_client' => 'Restrict Clients',
    'ib_code' => 'IB Code',
    'ib_code_note' => 'The IB code is used to identify the numbering system for the IB. Leave this field blank if you wish to maintain numbering based on the agent number.',
    'has_ib_code' => 'Has IB Code',
    'verify_email' => 'Verify Your Email',
    'check_swift_code' => 'Check Swift Code',
    'mt_group_status_note' => 'Setting the group to "Active" allows clients to open accounts, while "Inactive" disables it and displays a 404 error to the clients on registration page.',
    'data_view' => 'Data View',
    'cellxpert_deals' => 'Cellxpert Deals',
    'lot_volume' => 'Lot Volume',
    'cbv' => 'CBV',
    'open_date' => 'Open Date',
    'close_date' => 'Close Date',
    'p_l' => 'P&L',
    'internal_name' => 'Internal Group Type',
    'internal_name_tooltip' => 'This name will be used internally and will not be displayed to clients.',
    'total_complaints' => 'Total Complaints',
    'enroll_failed_contact_cs' => 'You are currently unable to enroll in this promotion. For further assistance, please contact our customer support team.',
    'number_of_complaints' => '{0,1} :count complaint|[2,*] :count complaints',
    'sales_manager' => 'Sales Manager',
    'sales_manager_tooltip' => 'Sales Manager is the manager of the sales person',
    'only_available_not_reached_account_limit' => 'Enable this option to restrict promotions to users who have not exceeded the limit of :count trading accounts. Users will be advised to contact Customer Support for more details',
    'categories' => 'Categories',
    'instruments_count' => 'Instruments Count',
    'category_tiers' => 'Category Tiers',
    'instrument_category_tiers' => 'Instrument Category Tiers',
    'max_leverage' => 'Max Leverage',
    'create_categories' => 'Create Categories',
    'category_list' => 'Categories List',
    'edit_category' => 'Edit Categories',
    'volume_from' => 'Volume From',
    'volume_to' => 'Volume To',
    'tier_capacity' => 'Tier Capacity',
    'dynamic_leverage_calculator' => 'Dynamic Leverage Calculator',
    'instrument_category' => 'Instrument Category',
    'cryptocurrency' => 'Cryptocurrency',
    'coin' => 'Coin',
    'after_deposit' => 'After Deposit',
    'filled' => 'Filled',
    'draft' => 'Draft',
    'deleted' => 'Deleted',
    'eligible_enroll_promotion_message' => 'You will be eligible for this promotion upon verifying your account. For more information, please contact the <a href=":link"> Customer Support. </a>',
    'margin_liquidation_calculator' => 'Margin and Liquidation Calculator',
    'open_position_size' => 'Open Position Size',
    'open_position_price' => 'Open Position Price',
    'leverage_used' => 'Leverage Used',
    'calculate' => 'Calculate',
    'liquidation_level' => 'Liquidation Level',
    'margin_currency' => 'Margin Currency',
    'insufficient_equity_to_open_requested' => 'Insufficient equity to open the requested position.',
    'liquidation_point' => 'Liquidation Point',
    'liquidation_price' => 'Liquidation Price',
    'new_symbol' => 'New Symbol',
    'allowed_entities' => 'Allowed Entities',
    'terms_and_conditions_cashback_promotion' => 'By ticking the box i acknowledge through my electronic approval of all <a target="_blank" href=":TERMS_CASHBACK" class="dashed-border">terms & conditions</a>',
    'allow_multiple_enrollments' => 'Allow multiple enrollments',
    'allow_multiple_enrollments_tooltip' => 'Enable this option to allow users to subscribe multiple times to the same promotion. Note: If an MT account limit is set, multiple subscriptions will not exceed the allowed limit.',
    'tier_category' => 'Tiers category (Dynamic Leverage)',
    'back_of_the_proof_of_identification' => 'Back of the Proof of Identification',
    'card_payment' => 'Card Payment',
    '3d_secure_card_payment' => '3D Secure Card Payment',
    'authorize_and_capture_payment' => 'Authorize and Capture Payment',
    'e_wallet_payment' => 'E-wallet Payment',
    'payment_request_using_reference_number' => 'Payment Request using Reference Number',
    'e_wallet_payment_type' => 'E-Wallet Payment Type',
    'qr_code_wallet_pay' => 'QR Code',
    'r2p_wallet_pay' => 'R2P Request',
    'disable_inactive_365' => 'Disable inactive accounts',
    'disable_inactive_365_tooltip' => 'Enable this option to notify users whose MetaTrader accounts are deactivated after 365 days of inactivity.',
    'import_started' => 'Your import has started successfully. You will receive a notification once the process is complete',
    'document_number' => 'Document Number',
    'mt_campaign_participants' => 'Campaign Participants',
    'is_winner' => 'Is Winner',
    'achievement_date' => 'Achievement Date',
    'winner' => 'Winner',
    'not_winner' => 'Not Winner',
    'winner_status' => 'Winner Status',
    'sort_per_date_asc' => 'Date (Ascending)',
    'sort_per_date_desc' => 'Date (Descending)',
    'sort_per_lots_asc' => 'Lots (Ascending)',
    'sort_per_lots_desc' => 'Lots (Descending)',
    'winner_already' => 'Winner Already',
    'mark_as_winner' => 'Mark as Winner',
    'competition_has_winner' => 'Competition has winner',
    'update_link' => 'Update Link',
    'current_time_tooltip' => 'The displayed time is based on the user\'s last login country and IP address. If the user has never logged in, the time is shown according to their registered country.',
    'kyc' => 'KYC',
    'failed_cfd_failed_next' => 'Failed CFD & failed all next attempts',
    'failed_first_cfd_no_second' => 'Failed CFD first attempt & did not attempt a second',
    'failed_first_cfd_no_third' => 'Failed CFD second attempt & did not attempt a third',
    'failed_first_cfd_no_forth' => 'Failed CFD third attempt & did not attempt a forth',
    'failed_cfd_success_next' => 'Failed CFD first attempt & passed on the second attempt',
    'failed_first_cfd_success_second' => 'Failed CFD second attempt & passed on the third attempt',
    'failed_first_cfd_success_third' => 'Failed CFD third attempt & passed on the forth attempt',
    'passed_cfd_failed_tmd' => 'Passed the CFD & failed the TMD',
    'passed_tmd_no_kyc' => 'Passed the TMD but did not attempt the KYC',
    'failed_tmd' => 'Failed the TMD',
    'attempted_kyc_not_completed_docs' => 'Attempted the KYC but did not complete the documents',
    'preferred_language' => 'Preferred Language',
    'you_cannot_open_account_account' => 'You cannot open an account right now! For more information, please contact Customer Support.',
    'calculated' => 'Calculate Question',
    'show_question_dependency' => 'Show Question Dependency',
    'edit_question_dependency' => 'Edit Question Dependency',
    'dependency_question_second' => 'Second Dependency Question',
    'dependency_answer_second' => 'Second Dependency Answer',
    'unclaimed_assets_policy' => 'I acknowledge the <a href=":url" class="dashed-border" target="_blank">unclaimed assets policy</a>',
    'instrument_request' => 'Instrument Request',
    'has_group_type' => 'Has Group Type',
    'enrolled' => 'Enrolled',
    'register_new_account' => 'Register An Account',
    'check' => 'Check',
    'are_you_us_citizen_for_tax' => 'Are you a US citizen for tax purposes?',
    'address_details' => 'Address Details',
    'investor_information' => 'Investor Information',
    'trading_knowledge_and_experience' => 'Trading Knowledge & Experience',
    'web_search' => 'Web Search',
    'online_ad' => 'Online Ad',
    'friend_referral' => 'Friend Referral',
    'print_ad' => 'Print Ad',
    'how_did_you_hear_about_us' => 'How did you hear about :entity?',
    'trading_account_details' => 'Trading Account Details',
    'bulk_remove_referrals' => 'Bulk Remove Referrals',
    'get_promotion' => 'Get Promotion',
    'trading_account_details' => 'Trading Account Details',
    'register_new_account_agreement' => 'I declare that I have carefully read and fully understood the entire text of the Terms and Conditions, Order Execution Policy, Risk Disclosure, Conflicts of Interest Policy, and Privacy Policy, which I fully accept and agree with.',
    'duplicate_account' => 'There is two accounts with the same email and phone number',
    'account_exists' => 'Account with this email or phone number already exists , you can add new MetaTrader account for this it.',
    'please_retry' => 'Please try again later',
    'enabled_for' => 'Enabled For',
    'crm_users' => 'CRM',
    'mobile_app_users' => 'Mobile App',
    'enabled_for_tooltip' => 'This option permits the group to be activated for mobile users, web users, or both of them.',
    'default_on_register_tooltip' => 'Enabling this option will automatically create a new account for the user when registering a new application under this group.',
    'is_selected' => 'Is Selected Question',
    'bulk_referral_delete_tooltip' => 'Add list of referral IDs to remove their clients assigned.',
    'ingot_app' => 'INGOT Mobile App',
    'partnership_verified_restriction' => 'To join our partnership program, kindly ensure that you have verified your account.',
    'no_signed_docs_note' => 'No signed documents have been received for this user.',
    'no_signed_docs_action' => 'Please <a href=":link" class="dashed-border">click here</a> to return the application.',
    'has_country_scope_only' => 'Has Country Scope Only',
    'monitoring' => 'Monitoring',
    'telescope' => 'Telescope',
    'your_billing_number_is' => 'Your Billing number is',
    'both' => 'Both',
    'account_not_found' => '<b>We could not find an existing account!</b><br/> Please complete the form below to create a new application.',
    'new_application_request_successfully' => 'The application request has been submitted successfully. <br/> we will review it and get back to you as soon as possible.',
    'new_trading_account_request_successfully' => 'The new sub trading account request has been submitted successfully. <br/> we will review it and get back to you as soon as possible.',
    'remove_duplicate' => 'Remove Duplicate',
    'email_verified' => 'Email Verified',
    'phone_verified' => 'Phone Verified',
    'create_sub_account' => 'Create Sub Trading Account',
    'mobile_device_token' => 'Mobile Device Token',
    'mobile_app_installed' => 'Mobile App Installed',
    'device_token' => 'Device Token',
    'internal_notifications' => 'Internal Notifications',
    'crate_notification' => 'Create Internal Notification',
    'update_notification' => 'Update Internal Notification',
    'account_is_exists_create_sub' => 'Account is already exists!, please confirm to request a new sub account.',
    'num_of_accounts' => 'Number of Accounts',
    'utm_id' => 'UTM ID',
    'notify_me_once_available' => 'Notify Me',
    'notify_me_form_added' => 'Thanks for your interest! We’ll notify you when the service is available in your region.',
    'waiting_list' => 'Waiting List',
    'financial_information_already_exists' => 'Financial information already exists',
    'country_of_tax_residence' => 'Country of Tax Residence',
    'enter_ssn_tin_number' => 'Enter the SSN/TIN number',
    'ssn_tin_number_n_a' => 'I have no SSN/TIN',
    'reason_a_cy' => 'The country / jurisdiction where the Account Holder is resident does not issue SSN/TIN to its residents',
    'reason_b_cy' => 'The Account Holder is otherwise unable to obtain a SST/TIN or equivalent number (Please explain why you are unable to obtain a SSN/TIN in the below table if you have selected this reason)',
    'reason_c_cy' => 'No SSN/TIN is required (Note. Only select this reason if the domestic law of the relevant jurisdiction does not require the collection of the SSN/TIN issued by such jurisdiction.)',
    'info_declaration' => 'I declare that the provided information is true, correct, complete and submitted voluntarily. I undertake the obligation to inform immediately the Company in writing in case of any change to that information and to provide any other data or documents, if necessary.',
    'politically_exposed_person' => 'Are you and any of your family members  PEP ‘Politically Exposed Person’ ?',
    'contact_consent' => 'By submitting my personal details, I consent to be contacted by the Company and related parties, by any communication means provided.',
    'name_employer_cy' => 'Name of employer/business organisation',
    'trading_experience' => 'Trading Experience',
    'ability_to_bear_losses' => 'Ability to bear losses',
    'investment_knowledge' => 'Investment Knowledge',
    'cyprus' => 'Cyprus',
    'residence_permit' => 'Residence Permit',
    'proof_doc_accept_content_9' => 'Bill for Internet / Cable TV / Phone',
    'proof_doc_accept_content_10' => 'Credit card statement',
    'proof_doc_accept_content_11' => 'Affidavit / Residence certificate',
    'proof_doc_accept_content_12' => 'Tax bill',
    'proof_doc_accept_content_13' => 'Tax return',
    'proof_doc_accept_content_14' => 'Any government issued document',
    'has_ssn_tin_number' => 'Do you have SSN/TIN (Social Security Number /Taxpayer identification Number)?',
    'expiration_date_passport' => 'Expiration Date of Passport',
    'drivers_license' => 'Driver\'s License',
    'relation_type' => 'Relation Type',
    'test_user_tooltip' => 'This user has been flagged as a test account! As a result, their activity will not be reported to any third-party systems, such as Manaf, CellXpert, etc.',
    'is_test_user' => 'Flagged as Test Account',
    'existing_users' => 'Existing Users',
];
