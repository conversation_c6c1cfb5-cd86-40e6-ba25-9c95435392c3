<?php

namespace App\Exceptions;

use App\Helpers\ApiCall\Src\Response\Response;
use Exception;
use JetBrains\PhpStorm\Pure;
use Throwable;

class ApiCallerResponseException extends Exception
{
    protected $response;

    #[Pure]
    public function __construct(Response $response, string $message = null, ?Throwable $previous = null)
    {
        $this->response = $response;
        $this->message = $message ?? $this->response->getMessage();
        parent::__construct($message, $this->response->getCode(), $previous);
    }
}
