<?php

namespace App\Exports;

use App\Http\Controllers\Admin\TransactionManagementController;
use App\Models\Transaction;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class TransactionManagementExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    use Exportable;

    private $statuses;

    public function __construct(protected string $userId, protected array $requestData)
    {
        $this->statuses = Transaction::$statuses;
    }

    public function query()
    {
        auth()->loginUsingId($this->userId);

        return app(TransactionManagementController::class)->index(
            request: request()->merge($this->requestData),
            export: true
        );
    }

    public function headings(): array
    {
        return [
            'APP. ID',
            'REFERENCE ID',
            'PAYMENT TYPE',
            'REQUESTED',
            'TRANSACTION TYPE',
            'NAME',
            'COUNTRY',
            'ENTITY',
            'FLAG TYPE',
            'RISK LEVEL',
            'ACCOUNT MANAGER',
            'ACCOUNTABILITY',
            'LAST VIEW BY',
            'TIMEFRAME',
            'CREATED DATE',
            'STARUS',
        ];
    }

    public function map($row): array
    {
        return [
            $row->application->appIdentifierId() ?? '-',
            $row->id,
            $row->paymentGateway->name ?? '-',
            round($row->req_amount, 3) . ' ' . ($row->reqCurrency?->code) ?? '-',
            $row->transactionType->name ?? '-',
            $row?->user?->getUserHolderName() ?? '-',
            $row?->user?->getUserCountry() ?? '-',
            $row->application->getRegulatedEntity() ?? '-',
            $row->application->lastFlagType?->title ?? '-',
            $row->user?->country?->getRiskStatus($row->application->affiliate_website_id) ?? 'Not set',
            $row->accountManager?->mainUser?->full_name ?? 'Not Assigned',
            $row->role->display_name ?? '-',
            $row->lastViewer ? $row?->lastViewer?->user?->getUserHolderName() : 'Not viewed yet!',
            $row->paymentTimeFrame() ?? 'Not assigned to payment department',
            $row->created_at ?? '-',
            $this->statuses[$row->status] ?? '-',
        ];
    }
}
