<?php

namespace App\Console\Commands;

use Exception;
use App\Models\User;
use GuzzleHttp\Client;
use App\Models\UserDocument;
use Illuminate\Console\Command;
use App\Models\AffiliateWebsite;

class IdwiseFetchDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'idwise:fetch-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch and update IDWise data for all affiliate websites';

    /**
     * Guzzle HTTP client.
     *
     * @var Client
     */
    private Client $client;

    /**
     * Document type mapping.
     *
     * @var array
     */
    private array $document_lookup;

    public function __construct()
    {
        parent::__construct();

        $this->client = new Client();

        $this->document_lookup = [
            'ID Document' => 'id_card_1',
            'Document' => 'id_card_1',
            'Utility Bill' => 'utility_bill',
        ];
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $affiliateWebsites = AffiliateWebsite::query()
            ->whereIn('iso', [
                'global',
                'africa',
                'jo',
            ])
            ->pluck('iso', 'id')
            ->toArray();

        foreach ($affiliateWebsites as $id => $iso) {
            // Retrieve IDWise credentials
            $config = config('idwise');

            if (!isset($config[$iso . '_client_key'], $config[$iso . '_username'], $config[$iso . '_password'])) {
                $this->error("Missing IDWise credentials for ISO: $iso");

                continue;
            }

            $baseUrl = rtrim($config['base_url'], '/');
            $apiUsername = $config[$iso . '_username'];
            $apiPassword = $config[$iso . '_password'];
            $encodedCredentials = base64_encode($apiUsername . ':' . $apiPassword);

            $this->info("Processing affiliate website (ID: $id, ISO: $iso)");

            // Retrieve users associated with this affiliate website
            $users = User::query()->whereHas('application', fn ($query) => $query->where('affiliate_website_id', $id))
                ->whereHas(
                    'userExtra',
                    fn ($query) => $query
                    ->whereNotNull('idwise_journey_id')
                    ->where('documents_verification', 'Approved')
                )
                ->with(['userExtra:idwise_journey_id,user_id'])
                ->get();

            $this->info("Total users to process: {$users->count()}");

            foreach ($users as $user) {
                $journeyId = $user->userExtra->idwise_journey_id;
                $userId = $user->id;

                try {
                    $this->info("Fetching data for User ID: $userId, Journey ID: $journeyId");

                    // API request to fetch IDWise data
                    $response = $this->client->get("{$baseUrl}/v2/get/{$journeyId}", [
                        'headers' => [
                            'Authorization' => 'Basic ' . $encodedCredentials,
                        ],
                    ]);

                    $data = json_decode($response->getBody()->getContents(), true);

                    if (!isset($data['documents'])) {
                        $this->warn("No documents found for User ID: $userId, Journey ID: $journeyId");

                        continue;
                    }

                    $this->processDocuments($userId, $data['documents']);
                } catch (Exception $e) {
                    $this->error("Error fetching data for Journey ID: $journeyId - {$e->getMessage()}");
                }

                $this->newLine();
            }

            $this->line('------------------------------------');
        }

        $this->info('IDWise data fetch process completed.');
    }

    /**
     * Process and store document data for a user.
     *
     * @param int   $userId
     * @param array $documents
     */
    private function processDocuments(int $userId, array $documents): void
    {
        foreach ($documents as $doc) {
            $documentType = $doc['step_title'] ?? null;

            if (!$documentType || !isset($this->document_lookup[$documentType])) {
                $this->warn("Unrecognized document type: $documentType");

                continue;
            }

            $mappedDocumentType = $this->document_lookup[$documentType];

            $extractedFields = $doc['extracted_fields'] ?? [];

            if (empty($extractedFields)) {
                $this->warn("Missing extracted fields for document type: $documentType");

                continue;
            }

            // Extract required fields
            $issueDate = $extractedFields['Issue Date']['value'] ?? null;
            $expiryDate = $extractedFields['Expiry Date']['value'] ?? null;
            $documentNumber = $extractedFields['Document Number']['value'] ?? null;

            if (!$issueDate && !$expiryDate && !$documentNumber) {
                $this->warn("Incomplete document data for User ID: $userId, Document Type: $documentType");

                continue;
            }

            // Store or update document data in the database
            $userDocument = UserDocument::query()
                ->where('user_id', $userId)
                ->where('type', $mappedDocumentType)
                ->first();

            if (!$userDocument) {
                $this->info("No document found for User ID: $userId, Type: $mappedDocumentType");

                continue;
            }

            $userDocument->update([
                'issue_date' => $issueDate,
                'expiry_date' => $expiryDate,
                'document_number' => $documentNumber,
            ]);

            $this->info("Stored document for User ID: $userId - Type: $mappedDocumentType, Issue Date: $issueDate, Expiry Date: $expiryDate, Number: $documentNumber");
        }
    }
}
