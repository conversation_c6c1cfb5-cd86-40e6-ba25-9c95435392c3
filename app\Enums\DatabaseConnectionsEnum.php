<?php

namespace App\Enums;

enum DatabaseConnectionsEnum: string
{
    case INGOT_BROKERS = 'mysql';
    case MT5 = 'mysql_mt5';
    case MT4_REAL = 'mysql_real_mt4';
    case MT4_DEMO = 'mysql_demo_mt4';
    case META_TRADER_REAL = 'meta_trader_real';
    case META_TRADER_DEMO = 'meta_trader_demo';

    public function databaseName(): string
    {
        return config("database.connections.{$this->value}.database", 'ingotbrokers');
    }

    public function tableName(string $table): string
    {
        return $this->databaseName() . '.' . $table;
    }
}
